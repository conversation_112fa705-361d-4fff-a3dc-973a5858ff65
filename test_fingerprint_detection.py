#!/usr/bin/env python3
"""
指纹检测测试脚本
用于验证指纹伪装功能是否正常工作
"""

import time
from logger import logging
from fingerprint_browser_utils import FingerprintBrowserManager


def test_fingerprint_spoofing():
    """测试指纹伪装功能"""
    try:
        logging.info("🧪 开始指纹伪装功能测试...")
        
        # 初始化指纹浏览器
        browser_manager = FingerprintBrowserManager()
        browser = browser_manager.init_browser()
        
        if not browser:
            logging.error("❌ 浏览器初始化失败")
            return False
        
        tab = browser.latest_tab
        
        # 测试网站列表
        test_sites = [
            {
                "name": "Canvas指纹测试",
                "url": "https://browserleaks.com/canvas",
                "description": "测试Canvas指纹是否被正确伪装"
            },
            {
                "name": "WebGL指纹测试", 
                "url": "https://browserleaks.com/webgl",
                "description": "测试WebGL指纹是否被正确伪装"
            },
            {
                "name": "地理位置测试",
                "url": "https://browserleaks.com/geo",
                "description": "测试地理位置是否被设置为美国"
            },
            {
                "name": "语言和时区测试",
                "url": "https://browserleaks.com/javascript",
                "description": "测试语言和时区设置"
            },
            {
                "name": "综合指纹测试",
                "url": "https://amiunique.org/",
                "description": "综合指纹唯一性测试"
            }
        ]
        
        for i, site in enumerate(test_sites, 1):
            logging.info(f"\n{'='*20} 测试 {i}/{len(test_sites)}: {site['name']} {'='*20}")
            logging.info(f"📝 描述: {site['description']}")
            logging.info(f"🌐 访问: {site['url']}")
            
            try:
                # 访问测试网站
                tab.get(site['url'])
                time.sleep(5)  # 等待页面加载
                
                # 截图记录
                screenshot_path = f"fingerprint_test_{i}_{site['name'].replace(' ', '_')}_{int(time.time())}.png"
                tab.get_screenshot(path=screenshot_path)
                logging.info(f"📸 截图已保存: {screenshot_path}")
                
                # 获取页面标题
                page_title = tab.title
                logging.info(f"📄 页面标题: {page_title}")
                
                # 执行一些JavaScript来检查指纹伪装
                if "canvas" in site['url']:
                    # 测试Canvas指纹
                    canvas_result = tab.run_js("""
                        var canvas = document.createElement('canvas');
                        var ctx = canvas.getContext('2d');
                        ctx.textBaseline = 'top';
                        ctx.font = '14px Arial';
                        ctx.fillText('Canvas fingerprint test', 2, 2);
                        return canvas.toDataURL();
                    """)
                    logging.info(f"🎨 Canvas指纹: {canvas_result[:50]}...")
                
                elif "webgl" in site['url']:
                    # 测试WebGL指纹
                    webgl_result = tab.run_js("""
                        var canvas = document.createElement('canvas');
                        var gl = canvas.getContext('webgl');
                        if (gl) {
                            return {
                                renderer: gl.getParameter(gl.RENDERER),
                                vendor: gl.getParameter(gl.VENDOR),
                                version: gl.getParameter(gl.VERSION)
                            };
                        }
                        return null;
                    """)
                    logging.info(f"🔺 WebGL信息: {webgl_result}")
                
                elif "geo" in site['url']:
                    # 测试地理位置
                    geo_result = tab.run_js("""
                        return new Promise((resolve) => {
                            if (navigator.geolocation) {
                                navigator.geolocation.getCurrentPosition(
                                    (position) => resolve({
                                        latitude: position.coords.latitude,
                                        longitude: position.coords.longitude
                                    }),
                                    (error) => resolve({error: error.message}),
                                    {timeout: 5000}
                                );
                            } else {
                                resolve({error: 'Geolocation not supported'});
                            }
                        });
                    """)
                    logging.info(f"📍 地理位置: {geo_result}")
                
                elif "javascript" in site['url']:
                    # 测试语言和时区
                    lang_tz_result = tab.run_js("""
                        return {
                            language: navigator.language,
                            languages: navigator.languages,
                            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                            timezoneOffset: new Date().getTimezoneOffset()
                        };
                    """)
                    logging.info(f"🌐 语言时区: {lang_tz_result}")
                
                logging.info(f"✅ {site['name']} 测试完成")
                
                # 等待用户查看结果
                input(f"请查看 {site['name']} 的测试结果，按 Enter 继续下一个测试...")
                
            except Exception as e:
                logging.error(f"❌ {site['name']} 测试失败: {e}")
                continue
        
        logging.info("\n🎉 所有指纹伪装测试完成！")
        logging.info("📋 请检查截图和日志，验证指纹伪装是否正常工作")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 指纹伪装测试失败: {e}")
        return False
    finally:
        # 保持浏览器打开供检查
        input("按 Enter 键关闭浏览器...")
        if browser_manager:
            browser_manager.quit()


def main():
    """主函数"""
    try:
        logging.info("=" * 80)
        logging.info("指纹伪装功能测试")
        logging.info("=" * 80)
        
        success = test_fingerprint_spoofing()
        
        if success:
            logging.info("🎉 指纹伪装功能测试成功！")
            print("\n✅ 指纹伪装功能测试完成！")
            print("📋 请查看生成的截图验证指纹伪装效果")
        else:
            logging.error("😞 指纹伪装功能测试失败！")
            print("\n❌ 指纹伪装功能测试失败！")
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 测试程序执行失败: {e}")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
