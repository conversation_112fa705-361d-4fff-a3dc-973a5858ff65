#!/usr/bin/env python3
"""
测试修复后的条款同意功能
验证复选框勾选和按钮启用逻辑
"""

import time
from logger import logging

def test_terms_fix():
    """测试修复后的条款同意功能"""
    try:
        logging.info("🧪 测试修复后的条款同意功能...")
        
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        logging.info("✅ 浏览器初始化成功")
        
        # 直接访问条款同意页面
        terms_url = "https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=test&code_challenge=test&code_challenge_method=S256"
        
        logging.info("🌐 访问条款同意页面...")
        tab.get(terms_url)
        time.sleep(5)
        
        # 截图记录初始状态
        tab.get_screenshot(path=f"terms_initial_{int(time.time())}.png")
        
        # 查找条款复选框
        terms_checkbox = tab.ele("#terms-of-service-checkbox", timeout=5)
        if not terms_checkbox:
            logging.error("❌ 未找到条款同意复选框")
            return False
        
        logging.info("🔍 找到条款同意复选框")
        
        # 检查初始状态
        is_checked_initial = terms_checkbox.attr("checked")
        logging.info(f"📋 复选框初始状态: {'已勾选' if is_checked_initial else '未勾选'}")
        
        # 查找注册按钮
        signup_button = tab.ele("#signup-button", timeout=3)
        if not signup_button:
            logging.error("❌ 未找到注册按钮")
            return False
        
        # 检查按钮初始状态
        is_disabled_initial = signup_button.attr("disabled") is not None
        logging.info(f"🔍 按钮初始状态: {'禁用' if is_disabled_initial else '启用'}")
        
        # 如果复选框未勾选，执行勾选操作
        if not is_checked_initial:
            logging.info("☑️ 开始勾选条款同意复选框...")
            
            # 方法1: 直接点击
            logging.info("🖱️ 方法1: 直接点击复选框...")
            terms_checkbox.click()
            time.sleep(2)
            
            is_checked_after_click = terms_checkbox.attr("checked")
            is_disabled_after_click = signup_button.attr("disabled") is not None
            
            logging.info(f"📋 点击后复选框状态: {'已勾选' if is_checked_after_click else '未勾选'}")
            logging.info(f"🔍 点击后按钮状态: {'禁用' if is_disabled_after_click else '启用'}")
            
            # 如果直接点击失败，尝试 JavaScript 方法
            if not is_checked_after_click:
                logging.info("🔧 方法2: 使用 JavaScript 勾选...")
                
                try:
                    tab.run_js("""
                        var checkbox = document.getElementById('terms-of-service-checkbox');
                        if (checkbox) {
                            console.log('找到复选框，当前状态:', checkbox.checked);
                            checkbox.checked = true;
                            console.log('设置后状态:', checkbox.checked);
                            
                            // 触发 change 事件
                            var event = new Event('change', { bubbles: true });
                            checkbox.dispatchEvent(event);
                            console.log('已触发 change 事件');
                            
                            // 尝试调用 onchange 函数
                            if (typeof updateSignupButton === 'function') {
                                updateSignupButton(checkbox);
                                console.log('已调用 updateSignupButton 函数');
                            } else {
                                console.log('updateSignupButton 函数不存在');
                            }
                        } else {
                            console.log('未找到复选框');
                        }
                    """)
                    time.sleep(2)
                    
                    is_checked_after_js = terms_checkbox.attr("checked")
                    is_disabled_after_js = signup_button.attr("disabled") is not None
                    
                    logging.info(f"📋 JavaScript 后复选框状态: {'已勾选' if is_checked_after_js else '未勾选'}")
                    logging.info(f"🔍 JavaScript 后按钮状态: {'禁用' if is_disabled_after_js else '启用'}")
                    
                except Exception as e:
                    logging.error(f"❌ JavaScript 方法失败: {e}")
        
        # 截图记录勾选后状态
        tab.get_screenshot(path=f"terms_after_check_{int(time.time())}.png")
        
        # 最终检查状态
        final_checked = terms_checkbox.attr("checked")
        final_disabled = signup_button.attr("disabled") is not None
        
        logging.info("=" * 50)
        logging.info("🎯 最终状态检查:")
        logging.info(f"📋 复选框状态: {'已勾选' if final_checked else '未勾选'}")
        logging.info(f"🔍 按钮状态: {'禁用' if final_disabled else '启用'}")
        
        # 如果按钮仍然禁用，尝试强制启用
        if final_disabled:
            logging.info("🔧 尝试强制启用按钮...")
            try:
                tab.run_js("""
                    var button = document.getElementById('signup-button');
                    if (button) {
                        console.log('找到按钮，当前禁用状态:', button.disabled);
                        button.removeAttribute('disabled');
                        button.disabled = false;
                        console.log('强制启用后状态:', button.disabled);
                    }
                """)
                time.sleep(1)
                
                final_disabled_after_force = signup_button.attr("disabled") is not None
                logging.info(f"🔍 强制启用后按钮状态: {'禁用' if final_disabled_after_force else '启用'}")
                
            except Exception as e:
                logging.error(f"❌ 强制启用失败: {e}")
        
        # 尝试点击按钮
        final_button_disabled = signup_button.attr("disabled") is not None
        if not final_button_disabled:
            logging.info("🖱️ 尝试点击注册按钮...")
            
            try:
                signup_button.click()
                logging.info("✅ 按钮点击成功")
                
                time.sleep(5)
                
                # 检查页面是否跳转
                final_url = tab.url
                logging.info(f"🔍 点击后 URL: {final_url}")
                
                if "terms-accept" not in final_url:
                    logging.info("🎉 成功跳转离开条款同意页面！")
                    return True
                else:
                    logging.warning("⚠️ 仍在条款同意页面")
                    
                    # 截图记录最终状态
                    tab.get_screenshot(path=f"terms_final_{int(time.time())}.png")
                    return False
                    
            except Exception as e:
                logging.error(f"❌ 按钮点击失败: {e}")
                return False
        else:
            logging.error("❌ 按钮仍然禁用，无法点击")
            return False
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        try:
            input("按 Enter 键关闭浏览器...")
            browser_manager.quit()
        except:
            pass

def main():
    """主函数"""
    logging.info("=" * 80)
    logging.info("修复后的条款同意功能测试")
    logging.info("=" * 80)
    
    success = test_terms_fix()
    
    if success:
        logging.info("🎉 条款同意功能测试成功！")
    else:
        logging.error("❌ 条款同意功能测试失败！")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
