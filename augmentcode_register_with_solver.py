#!/usr/bin/env python3
"""
使用专业 Turnstile Solver 的 AugmentCode 注册器
"""

import os
import sys
import time
import re
from logger import logging
from augmentcode_register import AugmentCodeRegister, generate_email_with_timestamp
from turnstile_solver_integration import TurnstileSolverIntegration

class AugmentCodeRegisterWithSolver(AugmentCodeRegister):
    """使用专业 Turnstile Solver 的 AugmentCode 注册器"""
    
    def __init__(self, email: str):
        """
        初始化注册器
        
        Args:
            email: 注册邮箱
        """
        super().__init__(email)
        self.turnstile_solver = TurnstileSolverIntegration()
        self.solver_initialized = False
    
    def init_turnstile_solver(self) -> bool:
        """
        初始化 Turnstile Solver
        
        Returns:
            bool: 初始化是否成功
        """
        if self.solver_initialized:
            return True
        
        try:
            logging.info("🔧 初始化专业 Turnstile Solver...")
            
            # 1. 安装依赖
            if not self.turnstile_solver.install_turnstile_solver():
                logging.error("❌ 安装 Turnstile Solver 依赖失败")
                return False
            
            # 2. 下载代码
            if not self.turnstile_solver.download_turnstile_solver():
                logging.error("❌ 下载 Turnstile Solver 代码失败")
                return False
            
            # 3. 启动 API 服务
            if not self.turnstile_solver.start_solver_api():
                logging.error("❌ 启动 Turnstile Solver API 失败")
                return False
            
            self.solver_initialized = True
            logging.info("✅ 专业 Turnstile Solver 初始化成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 初始化 Turnstile Solver 失败: {e}")
            return False
    
    def extract_turnstile_sitekey(self) -> str:
        """
        从页面提取 Turnstile sitekey
        
        Returns:
            str: sitekey，失败返回 None
        """
        try:
            logging.info("🔍 提取 Turnstile sitekey...")
            
            # 获取页面 HTML
            page_html = self.tab.html
            
            # 查找 sitekey 的多种模式
            sitekey_patterns = [
                r'data-sitekey="([^"]+)"',
                r'sitekey:\s*["\']([^"\']+)["\']',
                r'"sitekey":\s*"([^"]+)"',
                r'data-captcha-sitekey="([^"]+)"',
                r'turnstile.*?sitekey.*?["\']([^"\']+)["\']'
            ]
            
            for pattern in sitekey_patterns:
                match = re.search(pattern, page_html, re.IGNORECASE)
                if match:
                    sitekey = match.group(1)
                    logging.info(f"✅ 找到 sitekey: {sitekey}")
                    return sitekey
            
            # 如果没找到，尝试从 JavaScript 中提取
            js_result = self.tab.run_js("""
                // 查找页面中的 Turnstile 配置
                const scripts = document.querySelectorAll('script');
                for (let script of scripts) {
                    const content = script.textContent || script.innerHTML;
                    const sitekeyMatch = content.match(/sitekey['":\s]*['"]([^'"]+)['"]/i);
                    if (sitekeyMatch) {
                        return sitekeyMatch[1];
                    }
                }
                
                // 查找 data 属性
                const elements = document.querySelectorAll('[data-sitekey], [data-captcha-sitekey]');
                for (let el of elements) {
                    const sitekey = el.getAttribute('data-sitekey') || el.getAttribute('data-captcha-sitekey');
                    if (sitekey) {
                        return sitekey;
                    }
                }
                
                return null;
            """)
            
            if js_result:
                logging.info(f"✅ 通过 JavaScript 找到 sitekey: {js_result}")
                return js_result
            
            logging.warning("⚠️ 未找到 Turnstile sitekey")
            return None
            
        except Exception as e:
            logging.error(f"❌ 提取 sitekey 失败: {e}")
            return None
    
    def handle_turnstile_verification_with_solver(self) -> bool:
        """
        使用专业 Turnstile Solver 处理人机验证
        
        Returns:
            bool: 验证是否成功
        """
        try:
            logging.info("🎯 使用专业 Turnstile Solver 处理人机验证...")
            
            # 1. 初始化 Solver
            if not self.init_turnstile_solver():
                logging.error("❌ Turnstile Solver 初始化失败")
                return False
            
            # 2. 提取 sitekey
            sitekey = self.extract_turnstile_sitekey()
            if not sitekey:
                logging.error("❌ 无法提取 Turnstile sitekey")
                return False
            
            # 3. 获取当前页面 URL
            current_url = self.tab.url
            
            # 4. 使用专业 Solver 解决验证
            token = self.turnstile_solver.solve_turnstile(
                url=current_url,
                sitekey=sitekey,
                action="login"  # 可选的动作参数
            )
            
            if not token:
                logging.error("❌ 专业 Turnstile Solver 验证失败")
                return False
            
            # 5. 将令牌注入到页面
            success = self.inject_turnstile_token(token)
            
            if success:
                logging.info("🎉 专业 Turnstile Solver 验证成功！")
                return True
            else:
                logging.error("❌ 令牌注入失败")
                return False
                
        except Exception as e:
            logging.error(f"❌ 专业 Turnstile Solver 验证过程中发生错误: {e}")
            return False
    
    def inject_turnstile_token(self, token: str) -> bool:
        """
        将 Turnstile 令牌注入到页面
        
        Args:
            token: Turnstile 验证令牌
            
        Returns:
            bool: 注入是否成功
        """
        try:
            logging.info(f"💉 注入 Turnstile 令牌: {token[:20]}...")
            
            # 注入令牌的 JavaScript 代码
            inject_js = f"""
            // 查找并填充隐藏的 Turnstile 响应字段
            const responseFields = document.querySelectorAll(
                'input[name="cf-turnstile-response"], input[name*="turnstile"], input[name*="captcha"]'
            );
            
            let injected = false;
            for (let field of responseFields) {{
                if (field.type === 'hidden' || field.name.includes('turnstile') || field.name.includes('captcha')) {{
                    field.value = '{token}';
                    console.log('Injected token into:', field.name);
                    injected = true;
                }}
            }}
            
            // 如果没有找到字段，尝试创建一个
            if (!injected) {{
                const form = document.querySelector('form');
                if (form) {{
                    const hiddenField = document.createElement('input');
                    hiddenField.type = 'hidden';
                    hiddenField.name = 'cf-turnstile-response';
                    hiddenField.value = '{token}';
                    form.appendChild(hiddenField);
                    console.log('Created and injected token field');
                    injected = true;
                }}
            }}
            
            // 触发 Turnstile 回调（如果存在）
            if (typeof window.turnstileCallback === 'function') {{
                window.turnstileCallback('{token}');
                console.log('Called turnstile callback');
            }}
            
            // 触发表单验证事件
            const forms = document.querySelectorAll('form');
            for (let form of forms) {{
                form.dispatchEvent(new Event('change', {{ bubbles: true }}));
                form.dispatchEvent(new Event('input', {{ bubbles: true }}));
            }}
            
            return injected;
            """
            
            result = self.tab.run_js(inject_js)
            
            if result:
                logging.info("✅ Turnstile 令牌注入成功")
                
                # 等待页面处理令牌
                time.sleep(2)
                
                # 检查验证是否成功
                return self.check_turnstile_success()
            else:
                logging.error("❌ Turnstile 令牌注入失败")
                return False
                
        except Exception as e:
            logging.error(f"❌ 注入 Turnstile 令牌失败: {e}")
            return False
    
    def step2_handle_captcha(self) -> bool:
        """
        第二步：处理人机验证（使用专业 Solver）
        
        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("第二步：使用专业 Turnstile Solver 处理人机验证...")
            
            # 保存验证前的状态
            self.save_screenshot("before_professional_solver")
            
            # 使用专业 Solver 处理验证
            success = self.handle_turnstile_verification_with_solver()
            
            if success:
                logging.info("✅ 专业 Turnstile Solver 验证成功")
                self.save_screenshot("professional_solver_success")
                return True
            else:
                logging.error("❌ 专业 Turnstile Solver 验证失败")
                self.save_screenshot("professional_solver_failed")
                
                # 如果专业 Solver 失败，回退到原始方法
                logging.info("🔄 回退到原始 Turnstile 验证方法...")
                return self.handle_turnstile_verification()
                
        except Exception as e:
            logging.error(f"❌ 第二步处理人机验证失败: {e}")
            return False
    
    def __del__(self):
        """析构函数，确保 Solver 服务被停止"""
        try:
            if hasattr(self, 'turnstile_solver') and self.turnstile_solver:
                self.turnstile_solver.stop_solver_api()
        except:
            pass

def main():
    """测试使用专业 Turnstile Solver 的注册流程"""
    try:
        logging.info("=" * 80)
        logging.info("测试使用专业 Turnstile Solver 的 AugmentCode 注册")
        logging.info("=" * 80)
        
        # 生成邮箱地址
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱地址: {email}")
        
        # 创建注册器
        register = AugmentCodeRegisterWithSolver(email)
        
        # 执行注册流程
        success = register.register()
        
        if success:
            logging.info("🎉 使用专业 Turnstile Solver 的注册成功！")
        else:
            logging.error("😞 使用专业 Turnstile Solver 的注册失败！")
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
