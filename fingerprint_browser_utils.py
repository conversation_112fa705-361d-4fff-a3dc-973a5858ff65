#!/usr/bin/env python3
"""
指纹浏览器工具 - 高级指纹伪装功能
基于cursor_style_register.py的逻辑，增强指纹伪装能力
"""

import os
import sys
import time
import json
import random
import requests
import tempfile
import uuid
from typing import Dict, Any, Optional, Tuple
from DrissionPage import ChromiumOptions, Chromium
from logger import logging
from dotenv import load_dotenv

load_dotenv()


class IPLocationDetector:
    """IP地理位置检测器"""
    
    def __init__(self):
        self.cache = {}
    
    def get_location_info(self) -> Dict[str, Any]:
        """
        获取当前IP的地理位置信息
        
        Returns:
            Dict: 包含国家、语言、时区等信息
        """
        try:
            # 尝试多个IP检测服务
            services = [
                "http://ipapi.co/json/",
                "https://ipinfo.io/json",
                "http://ip-api.com/json/"
            ]
            
            for service in services:
                try:
                    response = requests.get(service, timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        return self._normalize_location_data(data, service)
                except:
                    continue
            
            # 如果所有服务都失败，返回默认值
            logging.warning("⚠️ 无法获取IP地理位置，使用默认值")
            return self._get_default_location()
            
        except Exception as e:
            logging.error(f"❌ IP地理位置检测失败: {e}")
            return self._get_default_location()
    
    def _normalize_location_data(self, data: Dict, service: str) -> Dict[str, Any]:
        """标准化不同服务的数据格式"""
        if "ipapi.co" in service:
            return {
                "country": data.get("country_name", "United States"),
                "country_code": data.get("country_code", "US"),
                "language": self._get_language_by_country(data.get("country_code", "US")),
                "timezone": data.get("timezone", "America/New_York"),
                "latitude": data.get("latitude", 40.7128),
                "longitude": data.get("longitude", -74.0060)
            }
        elif "ipinfo.io" in service:
            loc = data.get("loc", "40.7128,-74.0060").split(",")
            return {
                "country": data.get("country", "US"),
                "country_code": data.get("country", "US"),
                "language": self._get_language_by_country(data.get("country", "US")),
                "timezone": data.get("timezone", "America/New_York"),
                "latitude": float(loc[0]) if len(loc) > 0 else 40.7128,
                "longitude": float(loc[1]) if len(loc) > 1 else -74.0060
            }
        else:  # ip-api.com
            return {
                "country": data.get("country", "United States"),
                "country_code": data.get("countryCode", "US"),
                "language": self._get_language_by_country(data.get("countryCode", "US")),
                "timezone": data.get("timezone", "America/New_York"),
                "latitude": data.get("lat", 40.7128),
                "longitude": data.get("lon", -74.0060)
            }
    
    def _get_language_by_country(self, country_code: str) -> str:
        """根据国家代码获取对应语言"""
        language_map = {
            "US": "en-US",
            "CN": "zh-CN", 
            "JP": "ja-JP",
            "KR": "ko-KR",
            "DE": "de-DE",
            "FR": "fr-FR",
            "ES": "es-ES",
            "IT": "it-IT",
            "RU": "ru-RU",
            "BR": "pt-BR",
            "IN": "hi-IN",
            "GB": "en-GB",
            "CA": "en-CA",
            "AU": "en-AU"
        }
        return language_map.get(country_code.upper(), "en-US")
    
    def _get_default_location(self) -> Dict[str, Any]:
        """获取默认地理位置信息"""
        return {
            "country": "United States",
            "country_code": "US", 
            "language": "en-US",
            "timezone": "America/New_York",
            "latitude": 40.7128,
            "longitude": -74.0060
        }


class FingerprintSpoofing:
    """指纹伪装核心类"""
    
    def __init__(self, location_info: Dict[str, Any]):
        self.location_info = location_info
        self.fingerprint_seed = random.randint(1000, 9999)  # 用于生成一致的噪音
    
    def get_all_spoofing_scripts(self) -> str:
        """
        获取所有指纹伪装的JavaScript代码
        
        Returns:
            str: 完整的JavaScript代码
        """
        scripts = [
            self._get_language_timezone_script(),
            self._get_geolocation_script(),
            self._get_screen_resolution_script(),
            self._get_fonts_script(),
            self._get_webrtc_script(),
            self._get_canvas_script(),
            self._get_webgl_script(),
            self._get_audio_script(),
            self._get_speech_voices_script(),
            self._get_media_devices_script(),
            self._get_navigator_script()
        ]
        
        return "\n".join(scripts)
    
    def _get_language_timezone_script(self) -> str:
        """语言和时区伪装脚本"""
        language = self.location_info["language"]
        timezone = self.location_info["timezone"]
        
        return f"""
        // 语言和时区伪装
        (function() {{
            // 设置语言
            Object.defineProperty(navigator, 'language', {{
                get: function() {{ return '{language}'; }}
            }});
            Object.defineProperty(navigator, 'languages', {{
                get: function() {{ return ['{language}', 'en']; }}
            }});
            
            // 设置时区
            const originalDateTimeFormat = Intl.DateTimeFormat;
            Intl.DateTimeFormat = function(...args) {{
                if (args.length === 0 || !args[1] || !args[1].timeZone) {{
                    args[1] = args[1] || {{}};
                    args[1].timeZone = '{timezone}';
                }}
                return new originalDateTimeFormat(...args);
            }};
            
            // 重写Date的时区相关方法
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {{
                // 根据时区返回对应的偏移量
                const timezoneOffsets = {{
                    'America/New_York': 300,
                    'America/Los_Angeles': 480,
                    'Europe/London': 0,
                    'Asia/Shanghai': -480,
                    'Asia/Tokyo': -540
                }};
                return timezoneOffsets['{timezone}'] || 300;
            }};
            
            console.log('✅ 语言和时区伪装已设置:', '{language}', '{timezone}');
        }})();
        """

    def _get_geolocation_script(self) -> str:
        """地理位置伪装脚本 - 固定为美国中心"""
        # 使用美国地理中心坐标（按用户要求）
        lat, lng = 39.8283, -98.5795  # 美国地理中心（堪萨斯州）

        return f"""
        // 地理位置伪装 - 美国
        (function() {{
            const mockPosition = {{
                coords: {{
                    latitude: {lat},
                    longitude: {lng},
                    accuracy: 20,
                    altitude: null,
                    altitudeAccuracy: null,
                    heading: null,
                    speed: null
                }},
                timestamp: Date.now()
            }};

            if (navigator.geolocation) {{
                const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
                const originalWatchPosition = navigator.geolocation.watchPosition;

                navigator.geolocation.getCurrentPosition = function(success, error, options) {{
                    setTimeout(() => success(mockPosition), 100);
                }};

                navigator.geolocation.watchPosition = function(success, error, options) {{
                    setTimeout(() => success(mockPosition), 100);
                    return 1;
                }};
            }}

            console.log('✅ 地理位置伪装已设置:', {lat}, {lng});
        }})();
        """

    def _get_screen_resolution_script(self) -> str:
        """屏幕分辨率脚本 - 使用真实分辨率"""
        return """
        // 屏幕分辨率 - 保持真实
        (function() {
            // 不修改screen对象，保持真实分辨率
            console.log('✅ 屏幕分辨率保持真实:', screen.width, 'x', screen.height);
        })();
        """

    def _get_fonts_script(self) -> str:
        """字体伪装脚本"""
        return f"""
        // 字体伪装
        (function() {{
            const fontSeed = {self.fingerprint_seed};

            // 保持真实字体列表，但对字体指纹添加噪音
            const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
            const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');

            function addFontNoise(value) {{
                // 基于种子生成一致的微小噪音
                const noise = (fontSeed % 3) - 1; // -1, 0, 或 1
                return value + noise;
            }}

            Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {{
                get: function() {{
                    const value = originalOffsetWidth.get.call(this);
                    if (this.style && this.style.fontFamily) {{
                        return addFontNoise(value);
                    }}
                    return value;
                }}
            }});

            Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {{
                get: function() {{
                    const value = originalOffsetHeight.get.call(this);
                    if (this.style && this.style.fontFamily) {{
                        return addFontNoise(value);
                    }}
                    return value;
                }}
            }});

            console.log('✅ 字体指纹噪音已设置');
        }})();
        """

    def _get_webrtc_script(self) -> str:
        """WebRTC隐藏脚本"""
        return """
        // WebRTC隐藏
        (function() {
            // 禁用WebRTC
            if (window.RTCPeerConnection) {
                window.RTCPeerConnection = undefined;
            }
            if (window.webkitRTCPeerConnection) {
                window.webkitRTCPeerConnection = undefined;
            }
            if (window.mozRTCPeerConnection) {
                window.mozRTCPeerConnection = undefined;
            }

            // 禁用getUserMedia
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                navigator.mediaDevices.getUserMedia = function() {
                    return Promise.reject(new Error('WebRTC disabled'));
                };
            }

            console.log('✅ WebRTC已隐藏');
        })();
        """

    def _get_canvas_script(self) -> str:
        """Canvas指纹噪音脚本"""
        return f"""
        // Canvas指纹噪音
        (function() {{
            const canvasSeed = {self.fingerprint_seed};

            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

            function addCanvasNoise(imageData) {{
                const data = imageData.data;
                // 基于种子添加微小噪音
                for (let i = 0; i < data.length; i += 4) {{
                    const noise = (canvasSeed + i) % 3 - 1; // -1, 0, 或 1
                    data[i] = Math.max(0, Math.min(255, data[i] + noise));     // R
                    data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + noise)); // G
                    data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + noise)); // B
                }}
                return imageData;
            }}

            HTMLCanvasElement.prototype.toDataURL = function(...args) {{
                const context = this.getContext('2d');
                if (context) {{
                    const imageData = context.getImageData(0, 0, this.width, this.height);
                    addCanvasNoise(imageData);
                    context.putImageData(imageData, 0, 0);
                }}
                return originalToDataURL.apply(this, args);
            }};

            CanvasRenderingContext2D.prototype.getImageData = function(...args) {{
                const imageData = originalGetImageData.apply(this, args);
                return addCanvasNoise(imageData);
            }};

            console.log('✅ Canvas指纹噪音已设置');
        }})();
        """

    def _get_webgl_script(self) -> str:
        """WebGL指纹伪装脚本"""
        return """
        // WebGL指纹伪装
        (function() {
            const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
            const originalGetExtension = WebGLRenderingContext.prototype.getExtension;

            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                // 伪造特定的WebGL信息
                if (parameter === this.RENDERER) {
                    return 'Google Inc. (AMD) ANGLE (AMD, AMD Radeon(TM) R5 Graphics Direct3D9Ex vs_3_0 ps_3_0, aticfx64.dll)';
                }
                if (parameter === this.VENDOR) {
                    return 'Google Inc. (AMD)';
                }
                if (parameter === this.VERSION) {
                    return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
                }
                if (parameter === this.SHADING_LANGUAGE_VERSION) {
                    return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
                }
                return originalGetParameter.apply(this, arguments);
            };

            // 对WebGL2也应用相同的伪装
            if (window.WebGL2RenderingContext) {
                WebGL2RenderingContext.prototype.getParameter = WebGLRenderingContext.prototype.getParameter;
            }

            console.log('✅ WebGL指纹伪装已设置');
        })();
        """

    def _get_audio_script(self) -> str:
        """AudioContext指纹噪音脚本"""
        return f"""
        // AudioContext指纹噪音
        (function() {{
            const audioSeed = {self.fingerprint_seed};

            if (window.AudioContext || window.webkitAudioContext) {{
                const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;

                function AudioContextProxy() {{
                    const context = new OriginalAudioContext();

                    // 重写createOscillator方法添加噪音
                    const originalCreateOscillator = context.createOscillator;
                    context.createOscillator = function() {{
                        const oscillator = originalCreateOscillator.call(this);
                        const originalStart = oscillator.start;
                        oscillator.start = function(when) {{
                            // 添加微小的频率噪音
                            const noise = (audioSeed % 100) / 100000; // 0.00001 - 0.00099
                            oscillator.frequency.value += noise;
                            return originalStart.call(this, when);
                        }};
                        return oscillator;
                    }};

                    return context;
                }}

                AudioContextProxy.prototype = OriginalAudioContext.prototype;
                window.AudioContext = AudioContextProxy;
                if (window.webkitAudioContext) {{
                    window.webkitAudioContext = AudioContextProxy;
                }}
            }}

            console.log('✅ AudioContext指纹噪音已设置');
        }})();
        """

    def _get_speech_voices_script(self) -> str:
        """语音合成伪装脚本"""
        language = self.location_info["language"]
        return f"""
        // 语音合成伪装
        (function() {{
            if (window.speechSynthesis) {{
                const originalGetVoices = speechSynthesis.getVoices;
                speechSynthesis.getVoices = function() {{
                    const voices = originalGetVoices.call(this);
                    // 过滤并返回与当前语言匹配的语音
                    const filteredVoices = voices.filter(voice =>
                        voice.lang.startsWith('{language.split("-")[0]}') ||
                        voice.lang === 'en-US'
                    );
                    return filteredVoices.length > 0 ? filteredVoices : voices;
                }};
            }}

            console.log('✅ 语音合成已设置为:', '{language}');
        }})();
        """

    def _get_media_devices_script(self) -> str:
        """媒体设备伪装脚本"""
        return f"""
        // 媒体设备伪装
        (function() {{
            const deviceSeed = {self.fingerprint_seed};

            if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {{
                const originalEnumerateDevices = navigator.mediaDevices.enumerateDevices;

                navigator.mediaDevices.enumerateDevices = function() {{
                    return originalEnumerateDevices.call(this).then(devices => {{
                        // 生成一致的虚假设备信息
                        const fakeDevices = [
                            {{
                                deviceId: 'default',
                                kind: 'audioinput',
                                label: 'Default - Microphone (Realtek Audio)',
                                groupId: 'group' + (deviceSeed % 1000)
                            }},
                            {{
                                deviceId: 'communications',
                                kind: 'audioinput',
                                label: 'Communications - Microphone (Realtek Audio)',
                                groupId: 'group' + (deviceSeed % 1000)
                            }},
                            {{
                                deviceId: 'camera' + deviceSeed,
                                kind: 'videoinput',
                                label: 'USB2.0 HD UVC WebCam',
                                groupId: 'group' + (deviceSeed % 1000 + 1)
                            }}
                        ];
                        return fakeDevices;
                    }});
                }};
            }}

            console.log('✅ 媒体设备伪装已设置');
        }})();
        """

    def _get_navigator_script(self) -> str:
        """Navigator对象伪装脚本"""
        return """
        // Navigator对象伪装
        (function() {
            // 伪装硬件并发数 (8核CPU)
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: function() { return 8; },
                configurable: true
            });

            // 伪装设备内存 (16GB)
            if (navigator.deviceMemory !== undefined) {
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: function() { return 16; },
                    configurable: true
                });
            }

            // 伪装平台信息
            Object.defineProperty(navigator, 'platform', {
                get: function() { return 'Win32'; }
            });

            console.log('✅ Navigator对象伪装已设置');
        })();
        """


class FingerprintBrowserManager:
    """增强版浏览器管理器，集成指纹伪装功能"""

    def __init__(self, browser_type="chrome"):
        self.browser = None
        self.browser_type = browser_type.lower()
        self.temp_user_data_dir = None
        self.location_detector = IPLocationDetector()
        self.location_info = None
        self.fingerprint_spoofing = None

    def init_browser(self, user_agent=None):
        """初始化带指纹伪装的浏览器"""
        try:
            logging.info("🚀 初始化指纹伪装浏览器...")

            # 获取地理位置信息
            self.location_info = self.location_detector.get_location_info()
            logging.info(f"📍 检测到位置: {self.location_info['country']} ({self.location_info['language']})")

            # 初始化指纹伪装
            self.fingerprint_spoofing = FingerprintSpoofing(self.location_info)

            # 获取浏览器配置
            co = self._get_enhanced_browser_options(user_agent)

            # 创建浏览器实例
            self.browser = Chromium(co)

            # 注入指纹伪装脚本
            self._inject_fingerprint_scripts()

            logging.info("✅ 指纹伪装浏览器初始化成功")
            return self.browser

        except Exception as e:
            logging.error(f"❌ 指纹伪装浏览器初始化失败: {e}")
            return None

    def _get_enhanced_browser_options(self, user_agent=None):
        """获取增强版浏览器配置"""
        co = ChromiumOptions()

        # 基础浏览器配置
        browser_path = self._get_browser_path()
        if browser_path:
            co.set_paths(browser_path=browser_path)
            logging.info(f"🌐 使用浏览器: {self.browser_type} -> {browser_path}")

        # 添加扩展
        try:
            extension_path = self._get_extension_path("turnstilePatch")
            co.add_extension(extension_path)
        except FileNotFoundError as e:
            logging.warning(f"警告: {e}")

        # 无痕模式和环境隔离
        self._setup_incognito_mode(co)

        # 基础设置
        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")

        # 代理设置
        proxy = os.getenv("BROWSER_PROXY")
        if proxy:
            co.set_proxy(proxy)

        co.auto_port()
        if user_agent:
            co.set_user_agent(user_agent)

        # 根据检测到的位置设置语言
        language = self.location_info["language"]
        co.set_argument(f"--lang={language.split('-')[0]}")
        co.set_argument(f"--accept-lang={language},{language.split('-')[0]};q=0.9,en;q=0.8")
        co.set_pref("intl.accept_languages", f"{language},{language.split('-')[0]},en")

        # 地理位置设置（美国）
        co.set_pref("profile.default_content_setting_values.geolocation", 1)

        # 无头模式
        co.headless(os.getenv("BROWSER_HEADLESS", "True").lower() == "true")

        # 反检测设置（优化版 - 允许必要资源加载）
        co.set_argument("--disable-blink-features=AutomationControlled")
        co.set_argument("--disable-dev-shm-usage")
        co.set_argument("--no-sandbox")

        # 允许必要的网络资源加载
        co.set_argument("--allow-running-insecure-content")
        co.set_argument("--disable-web-security")

        # 保持基本的反检测，但不影响资源加载
        co.set_argument("--disable-features=VizDisplayCompositor")

        return co

    def _inject_fingerprint_scripts(self):
        """注入指纹伪装脚本"""
        if not self.browser or not self.fingerprint_spoofing:
            return

        try:
            # 获取所有伪装脚本
            spoofing_script = self.fingerprint_spoofing.get_all_spoofing_scripts()

            # 设置页面加载模式为eager，确保脚本尽早执行
            self.browser.set.load_mode.eager()

            # 注入脚本到页面 - 在页面初始化时立即执行
            tab = self.browser.latest_tab
            if tab:
                try:
                    # 立即执行指纹伪装脚本
                    tab.run_js(spoofing_script)
                    logging.info("✅ 指纹伪装脚本注入成功")
                except Exception as js_error:
                    logging.warning(f"⚠️ JavaScript注入失败，将在页面访问时重试: {js_error}")

        except Exception as e:
            logging.error(f"❌ 指纹伪装脚本注入失败: {e}")

    def inject_scripts_to_tab(self, tab):
        """向指定标签页注入指纹伪装脚本"""
        if not self.fingerprint_spoofing:
            return

        try:
            spoofing_script = self.fingerprint_spoofing.get_all_spoofing_scripts()
            # 直接执行指纹伪装脚本，不包装在函数中
            tab.run_js(spoofing_script)
            logging.info("✅ 标签页指纹伪装脚本注入成功")
        except Exception as e:
            logging.error(f"❌ 标签页指纹伪装脚本注入失败: {e}")

    def _setup_incognito_mode(self, co):
        """设置无痕模式和环境隔离"""
        logging.info("🔒 配置无痕模式和环境隔离...")

        # 无痕模式
        if self.browser_type == "edge":
            co.set_argument("--inprivate")
        else:
            co.set_argument("--incognito")

        # 临时用户数据目录
        temp_dir = tempfile.mkdtemp(prefix=f"fingerprint_{self.browser_type}_{uuid.uuid4().hex[:8]}_")
        self.temp_user_data_dir = temp_dir
        co.set_user_data_path(temp_dir)
        logging.info(f"📁 临时用户数据目录: {temp_dir}")

        # 禁用各种持久化功能
        co.set_argument("--no-first-run")
        co.set_argument("--no-default-browser-check")
        co.set_argument("--disable-default-apps")
        co.set_argument("--disable-background-mode")
        co.set_argument("--disable-application-cache")
        co.set_argument("--disable-local-storage")
        co.set_argument("--disable-session-storage")
        co.set_argument("--disable-databases")
        co.set_argument("--disable-sync")
        co.set_argument("--disable-extensions")

        # 设置临时下载目录
        temp_download_dir = os.path.join(temp_dir, "downloads")
        os.makedirs(temp_download_dir, exist_ok=True)
        co.set_pref("download.default_directory", temp_download_dir)

        logging.info("✅ 无痕模式配置完成")

    def _get_browser_path(self):
        """获取浏览器路径"""
        # 复用原有的浏览器路径检测逻辑
        env_path = os.getenv("BROWSER_PATH")
        if env_path:
            return env_path

        if self.browser_type == "edge":
            return self._find_edge_path()
        elif self.browser_type == "chrome":
            return self._find_chrome_path()
        elif self.browser_type == "chromium":
            return self._find_chromium_path()
        else:
            logging.warning(f"⚠️ 未知的浏览器类型: {self.browser_type}")
            return None

    def _find_edge_path(self):
        """查找Microsoft Edge浏览器路径"""
        possible_paths = []

        if sys.platform == "win32":
            possible_paths = [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                os.path.expanduser(r"~\AppData\Local\Microsoft\Edge\Application\msedge.exe"),
            ]
        elif sys.platform == "darwin":
            possible_paths = [
                "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
            ]
        else:
            possible_paths = [
                "/usr/bin/microsoft-edge",
                "/usr/bin/microsoft-edge-stable",
                "/opt/microsoft/msedge/msedge",
            ]

        for path in possible_paths:
            if os.path.exists(path):
                logging.info(f"✅ 找到Edge浏览器: {path}")
                return path

        logging.warning("⚠️ 未找到Microsoft Edge浏览器")
        return None

    def _find_chrome_path(self):
        """查找Google Chrome浏览器路径"""
        possible_paths = []

        if sys.platform == "win32":
            possible_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
            ]
        elif sys.platform == "darwin":
            possible_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            ]
        else:
            possible_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
            ]

        for path in possible_paths:
            if os.path.exists(path):
                logging.info(f"✅ 找到Chrome浏览器: {path}")
                return path

        logging.warning("⚠️ 未找到Google Chrome浏览器")
        return None

    def _find_chromium_path(self):
        """查找Chromium浏览器路径"""
        possible_paths = []

        if sys.platform == "win32":
            possible_paths = [
                r"C:\Program Files\Chromium\Application\chrome.exe",
                r"C:\Program Files (x86)\Chromium\Application\chrome.exe",
            ]
        elif sys.platform == "darwin":
            possible_paths = [
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
            ]
        else:
            possible_paths = [
                "/usr/bin/chromium",
                "/usr/bin/chromium-browser",
            ]

        for path in possible_paths:
            if os.path.exists(path):
                logging.info(f"✅ 找到Chromium浏览器: {path}")
                return path

        logging.warning("⚠️ 未找到Chromium浏览器")
        return None

    def _get_extension_path(self, exname='turnstilePatch'):
        """获取插件路径"""
        root_dir = os.getcwd()
        extension_path = os.path.join(root_dir, exname)

        if hasattr(sys, "_MEIPASS"):
            extension_path = os.path.join(sys._MEIPASS, exname)

        if not os.path.exists(extension_path):
            raise FileNotFoundError(f"插件不存在: {extension_path}")

        return extension_path

    def quit(self):
        """关闭浏览器并清理临时文件"""
        if self.browser:
            try:
                self.browser.quit()
            except:
                pass

        self._cleanup_temp_directory()

    def _cleanup_temp_directory(self):
        """清理临时用户数据目录"""
        if self.temp_user_data_dir and os.path.exists(self.temp_user_data_dir):
            try:
                import shutil
                time.sleep(2)
                logging.info(f"🧹 清理临时目录: {self.temp_user_data_dir}")
                shutil.rmtree(self.temp_user_data_dir, ignore_errors=True)
                logging.info("✅ 临时目录清理完成")
            except Exception as e:
                logging.warning(f"⚠️ 清理临时目录失败: {e}")
            finally:
                self.temp_user_data_dir = None
