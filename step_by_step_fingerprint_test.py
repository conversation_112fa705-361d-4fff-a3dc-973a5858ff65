#!/usr/bin/env python3
"""
分步骤指纹伪装测试脚本
逐步测试每个指纹伪装功能
"""

import time
from logger import logging
from fingerprint_browser_utils import FingerprintBrowserManager, FingerprintSpoofing


class StepByStepTester:
    """分步骤测试器"""
    
    def __init__(self):
        self.browser_manager = None
        self.browser = None
        self.tab = None
    
    def init_basic_browser(self):
        """初始化基础浏览器（无指纹伪装）"""
        try:
            logging.info("🚀 初始化基础浏览器...")
            self.browser_manager = FingerprintBrowserManager()
            # 暂时禁用指纹伪装
            self.browser_manager.fingerprint_spoofing = None
            self.browser = self.browser_manager.init_browser()
            
            if not self.browser:
                logging.error("❌ 浏览器初始化失败")
                return False
            
            self.tab = self.browser.latest_tab
            logging.info("✅ 基础浏览器初始化成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 基础浏览器初始化失败: {e}")
            return False
    
    def test_baseline(self):
        """测试基线指纹（无伪装）"""
        try:
            logging.info("\n" + "="*50)
            logging.info("📊 基线指纹测试（无伪装）")
            logging.info("="*50)
            
            # 访问测试页面
            self.tab.get("https://browserleaks.com/canvas")
            time.sleep(3)
            
            # 获取基线指纹
            baseline = self._get_fingerprint_data()
            
            logging.info("📋 基线指纹数据:")
            for key, value in baseline.items():
                logging.info(f"  {key}: {value}")
            
            return baseline
            
        except Exception as e:
            logging.error(f"❌ 基线测试失败: {e}")
            return {}
    
    def test_geolocation_only(self):
        """仅测试地理位置伪装"""
        try:
            logging.info("\n" + "="*50)
            logging.info("📍 步骤1: 仅地理位置伪装测试")
            logging.info("="*50)
            
            # 注入地理位置伪装脚本
            geo_script = """
            // 地理位置伪装 - 美国中心
            (function() {
                const mockPosition = {
                    coords: {
                        latitude: 39.8283,
                        longitude: -98.5795,
                        accuracy: 20,
                        altitude: null,
                        altitudeAccuracy: null,
                        heading: null,
                        speed: null
                    },
                    timestamp: Date.now()
                };

                if (navigator.geolocation) {
                    const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
                    const originalWatchPosition = navigator.geolocation.watchPosition;

                    navigator.geolocation.getCurrentPosition = function(success, error, options) {
                        setTimeout(() => success(mockPosition), 100);
                    };

                    navigator.geolocation.watchPosition = function(success, error, options) {
                        setTimeout(() => success(mockPosition), 100);
                        return 1;
                    };
                }

                console.log('✅ 地理位置伪装已设置:', 39.8283, -98.5795);
            })();
            """
            
            self.tab.run_js(geo_script)
            time.sleep(2)
            
            # 测试地理位置
            geo_result = self._test_geolocation()
            
            logging.info("📍 地理位置伪装结果:")
            logging.info(f"  坐标: {geo_result}")
            
            # 检查是否成功
            if geo_result and 'latitude' in geo_result:
                lat = geo_result['latitude']
                lng = geo_result['longitude']
                if abs(lat - 39.8283) < 0.01 and abs(lng - (-98.5795)) < 0.01:
                    logging.info("✅ 地理位置伪装成功！")
                    return True
                else:
                    logging.error("❌ 地理位置伪装失败！")
                    return False
            else:
                logging.error("❌ 地理位置获取失败！")
                return False
            
        except Exception as e:
            logging.error(f"❌ 地理位置伪装测试失败: {e}")
            return False
    
    def test_hardware_only(self):
        """仅测试硬件信息伪装"""
        try:
            logging.info("\n" + "="*50)
            logging.info("💻 步骤2: 仅硬件信息伪装测试")
            logging.info("="*50)
            
            # 注入硬件信息伪装脚本
            hardware_script = """
            // 硬件信息伪装
            (function() {
                // 伪装硬件并发数 (8核CPU)
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: function() { return 8; },
                    configurable: true
                });

                // 伪装设备内存 (16GB)
                if (navigator.deviceMemory !== undefined) {
                    Object.defineProperty(navigator, 'deviceMemory', {
                        get: function() { return 16; },
                        configurable: true
                    });
                }

                console.log('✅ 硬件信息伪装已设置: 8核CPU, 16GB内存');
            })();
            """
            
            self.tab.run_js(hardware_script)
            time.sleep(2)
            
            # 测试硬件信息
            hardware_result = self._test_hardware()
            
            logging.info("💻 硬件信息伪装结果:")
            logging.info(f"  CPU核心数: {hardware_result.get('cores')}")
            logging.info(f"  内存大小: {hardware_result.get('memory')}GB")
            
            # 检查是否成功
            success = (hardware_result.get('cores') == 8 and 
                      hardware_result.get('memory') == 16)
            
            if success:
                logging.info("✅ 硬件信息伪装成功！")
                return True
            else:
                logging.error("❌ 硬件信息伪装失败！")
                return False
            
        except Exception as e:
            logging.error(f"❌ 硬件信息伪装测试失败: {e}")
            return False
    
    def test_webgl_only(self):
        """仅测试WebGL伪装"""
        try:
            logging.info("\n" + "="*50)
            logging.info("🔺 步骤3: 仅WebGL伪装测试")
            logging.info("="*50)
            
            # 注入WebGL伪装脚本
            webgl_script = """
            // WebGL伪装
            (function() {
                const getParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {
                    if (parameter === 37445) { // RENDERER
                        return 'AMD Radeon(TM) R5 Graphics';
                    }
                    if (parameter === 37446) { // VENDOR
                        return 'AMD';
                    }
                    return getParameter.call(this, parameter);
                };

                console.log('✅ WebGL伪装已设置: AMD显卡');
            })();
            """
            
            self.tab.run_js(webgl_script)
            time.sleep(2)
            
            # 测试WebGL
            webgl_result = self._test_webgl()
            
            logging.info("🔺 WebGL伪装结果:")
            logging.info(f"  渲染器: {webgl_result.get('renderer')}")
            logging.info(f"  厂商: {webgl_result.get('vendor')}")
            
            # 检查是否成功
            success = ('AMD' in str(webgl_result.get('renderer', '')) or 
                      'AMD' in str(webgl_result.get('vendor', '')))
            
            if success:
                logging.info("✅ WebGL伪装成功！")
                return True
            else:
                logging.error("❌ WebGL伪装失败！")
                return False
            
        except Exception as e:
            logging.error(f"❌ WebGL伪装测试失败: {e}")
            return False
    
    def _get_fingerprint_data(self):
        """获取完整指纹数据"""
        try:
            return self.tab.run_js("""
                return {
                    userAgent: navigator.userAgent.substring(0, 100),
                    language: navigator.language,
                    platform: navigator.platform,
                    cores: navigator.hardwareConcurrency,
                    memory: navigator.deviceMemory,
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                };
            """)
        except:
            return {}
    
    def _test_geolocation(self):
        """测试地理位置"""
        try:
            return self.tab.run_js("""
                return new Promise((resolve) => {
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(
                            (position) => resolve({
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude
                            }),
                            (error) => resolve({error: error.message}),
                            {timeout: 3000}
                        );
                    } else {
                        resolve({error: 'Geolocation not supported'});
                    }
                });
            """)
        except:
            return {}
    
    def _test_hardware(self):
        """测试硬件信息"""
        try:
            return self.tab.run_js("""
                return {
                    cores: navigator.hardwareConcurrency,
                    memory: navigator.deviceMemory
                };
            """)
        except:
            return {}
    
    def _test_webgl(self):
        """测试WebGL信息"""
        try:
            return self.tab.run_js("""
                var canvas = document.createElement('canvas');
                var gl = canvas.getContext('webgl');
                if (gl) {
                    return {
                        renderer: gl.getParameter(gl.RENDERER),
                        vendor: gl.getParameter(gl.VENDOR)
                    };
                }
                return null;
            """)
        except:
            return {}
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.browser_manager:
                self.browser_manager.quit()
        except:
            pass


def main():
    """主函数"""
    tester = StepByStepTester()
    
    try:
        logging.info("=" * 60)
        logging.info("分步骤指纹伪装测试")
        logging.info("=" * 60)
        
        # 初始化浏览器
        if not tester.init_basic_browser():
            return False
        
        # 测试基线
        baseline = tester.test_baseline()
        
        # 分步测试
        results = {}
        
        # 步骤1: 地理位置伪装
        results['geolocation'] = tester.test_geolocation_only()
        
        # 步骤2: 硬件信息伪装
        results['hardware'] = tester.test_hardware_only()
        
        # 步骤3: WebGL伪装
        results['webgl'] = tester.test_webgl_only()
        
        # 总结结果
        logging.info("\n" + "="*50)
        logging.info("📊 分步测试结果总结")
        logging.info("="*50)
        
        for step, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            logging.info(f"  {step}: {status}")
        
        all_success = all(results.values())
        
        if all_success:
            logging.info("🎉 所有分步测试都成功！")
            print("\n✅ 分步测试全部通过！")
        else:
            logging.error("😞 部分测试失败！")
            print("\n❌ 部分测试失败！")
        
        return all_success
        
    except Exception as e:
        logging.error(f"❌ 测试程序执行失败: {e}")
        return False
    finally:
        input("按 Enter 键关闭浏览器...")
        tester.cleanup()


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
