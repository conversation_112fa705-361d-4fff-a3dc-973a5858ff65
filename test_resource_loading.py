#!/usr/bin/env python3
"""
资源加载测试脚本
验证reCAPTCHA等关键资源是否能正常加载
"""

import time
from logger import logging
from fingerprint_browser_utils import FingerprintBrowserManager


def test_resource_loading():
    """测试关键资源加载"""
    try:
        logging.info("🧪 开始资源加载测试...")
        
        # 初始化指纹浏览器
        browser_manager = FingerprintBrowserManager()
        browser = browser_manager.init_browser()
        
        if not browser:
            logging.error("❌ 浏览器初始化失败")
            return False
        
        tab = browser.latest_tab
        
        # 测试访问AugmentCode页面
        logging.info("🌐 访问AugmentCode页面...")
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(5)
        
        # 注入指纹伪装脚本
        browser_manager.inject_scripts_to_tab(tab)
        time.sleep(2)
        
        # 检查页面加载状态
        page_title = tab.title
        logging.info(f"📄 页面标题: {page_title}")
        
        # 检查网络请求
        logging.info("🔍 检查网络请求状态...")
        
        # 使用JavaScript检查资源加载
        resource_check = tab.run_js("""
            // 检查页面中的资源加载状态
            const resources = [];
            
            // 检查所有script标签
            const scripts = document.querySelectorAll('script[src]');
            scripts.forEach(script => {
                resources.push({
                    type: 'script',
                    src: script.src,
                    loaded: !script.hasAttribute('data-failed')
                });
            });
            
            // 检查所有link标签
            const links = document.querySelectorAll('link[href]');
            links.forEach(link => {
                resources.push({
                    type: 'link',
                    src: link.href,
                    loaded: !link.hasAttribute('data-failed')
                });
            });
            
            // 检查是否有reCAPTCHA相关资源
            const recaptchaResources = resources.filter(r => 
                r.src.includes('recaptcha') || 
                r.src.includes('google.com') ||
                r.src.includes('gstatic.com')
            );
            
            return {
                totalResources: resources.length,
                recaptchaResources: recaptchaResources,
                failedResources: resources.filter(r => !r.loaded)
            };
        """)
        
        logging.info(f"📊 资源加载统计:")
        logging.info(f"  总资源数: {resource_check.get('totalResources', 0)}")
        logging.info(f"  reCAPTCHA相关资源: {len(resource_check.get('recaptchaResources', []))}")
        logging.info(f"  失败资源数: {len(resource_check.get('failedResources', []))}")
        
        # 详细显示reCAPTCHA资源
        recaptcha_resources = resource_check.get('recaptchaResources', [])
        if recaptcha_resources:
            logging.info("🔐 reCAPTCHA相关资源:")
            for resource in recaptcha_resources:
                status = "✅ 成功" if resource['loaded'] else "❌ 失败"
                logging.info(f"  {status} {resource['type']}: {resource['src']}")
        
        # 检查控制台错误
        console_errors = tab.run_js("""
            // 获取控制台错误（如果有的话）
            return window.console._errors || [];
        """)
        
        if console_errors:
            logging.warning("⚠️ 控制台错误:")
            for error in console_errors:
                logging.warning(f"  {error}")
        
        # 测试点击注册按钮看是否会触发reCAPTCHA加载
        logging.info("🔍 查找注册按钮...")
        
        button_texts = [
            "Get your free month",
            "Claim offer", 
            "Get started",
            "Continue",
            "Sign up",
            "Register"
        ]
        
        button_found = False
        for button_text in button_texts:
            try:
                button = tab.ele(f"@text()={button_text}", timeout=2)
                if button:
                    logging.info(f"✅ 找到按钮: {button_text}")
                    
                    # 点击按钮
                    button.click()
                    logging.info("🖱️ 点击按钮...")
                    button_found = True
                    break
            except:
                continue
        
        if button_found:
            # 等待页面跳转和资源加载
            time.sleep(5)
            
            # 再次检查资源加载
            new_url = tab.url
            logging.info(f"🌐 跳转后URL: {new_url}")
            
            # 重新注入指纹脚本
            browser_manager.inject_scripts_to_tab(tab)
            
            # 再次检查资源
            post_click_check = tab.run_js("""
                const scripts = document.querySelectorAll('script[src]');
                const recaptchaScripts = [];
                
                scripts.forEach(script => {
                    if (script.src.includes('recaptcha') || 
                        script.src.includes('google.com') ||
                        script.src.includes('gstatic.com')) {
                        recaptchaScripts.push({
                            src: script.src,
                            loaded: script.readyState === 'complete' || !script.hasAttribute('data-failed')
                        });
                    }
                });
                
                return recaptchaScripts;
            """)
            
            logging.info("📊 点击后reCAPTCHA资源状态:")
            for resource in post_click_check:
                status = "✅ 成功" if resource['loaded'] else "❌ 失败"
                logging.info(f"  {status} {resource['src']}")
        
        # 截图记录
        tab.get_screenshot(path=f"resource_loading_test_{int(time.time())}.png")
        
        logging.info("✅ 资源加载测试完成！")
        return True
        
    except Exception as e:
        logging.error(f"❌ 资源加载测试失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        # 保持浏览器打开供检查
        input("按 Enter 键关闭浏览器...")
        if browser_manager:
            browser_manager.quit()


def main():
    """主函数"""
    try:
        logging.info("=" * 60)
        logging.info("资源加载测试")
        logging.info("=" * 60)
        
        success = test_resource_loading()
        
        if success:
            logging.info("🎉 资源加载测试成功！")
            print("\n✅ 资源加载测试完成！")
        else:
            logging.error("😞 资源加载测试失败！")
            print("\n❌ 资源加载测试失败！")
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 测试程序执行失败: {e}")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
