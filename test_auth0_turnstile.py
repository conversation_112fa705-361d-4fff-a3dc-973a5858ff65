#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的 Auth0 V2 Turnstile 验证功能
"""

import sys
import os
import time
import logging

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from augmentcode_register import AugmentCodeRegister

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_auth0_turnstile.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_auth0_turnstile():
    """测试 Auth0 V2 Turnstile 验证"""
    print("=== 测试 Auth0 V2 Turnstile 验证 ===")
    
    registrar = None
    try:
        # 创建注册器实例
        print("创建注册器实例...")
        test_email = "<EMAIL>"  # 测试邮箱
        registrar = AugmentCodeRegister(test_email)
        
        # 步骤1: 访问页面并点击注册
        print("\n步骤1: 访问页面并点击注册...")
        step1_success = registrar.step1_click_get_free_month()
        print(f"步骤1结果: {'✅ 成功' if step1_success else '❌ 失败'}")
        
        if not step1_success:
            print("步骤1失败，停止测试")
            return False
        
        # 保存当前页面状态
        current_url = registrar.tab.url
        print(f"当前页面URL: {current_url}")
        
        # 分析页面中的验证码元素
        print("\n分析页面中的验证码元素...")
        analyze_captcha_elements(registrar)
        
        # 步骤2: 处理人机验证
        print("\n步骤2: 处理人机验证...")
        step2_success = registrar.step2_handle_captcha()
        print(f"步骤2结果: {'✅ 成功' if step2_success else '❌ 失败'}")
        
        if step2_success:
            print("✅ Auth0 V2 Turnstile 验证成功！")
            
            # 保存成功后的页面状态
            final_url = registrar.tab.url
            print(f"验证后页面URL: {final_url}")
            
            # 保存页面HTML用于分析
            save_page_html(registrar, "auth0_turnstile_success.html")
                
            # 等待观察页面状态
            print("等待5秒观察页面状态...")
            time.sleep(5)
            
            return True
        else:
            print("❌ Auth0 V2 Turnstile 验证失败")
            
            # 保存失败页面用于调试
            save_page_html(registrar, "auth0_turnstile_failed.html")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        logging.error(f"Auth0 Turnstile测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        if registrar:
            try:
                registrar.close()
                print("✓ 浏览器资源已清理")
            except Exception as e:
                print(f"清理资源时出错: {str(e)}")

def analyze_captcha_elements(registrar):
    """分析页面中的验证码元素"""
    print("分析页面中的验证码相关元素...")
    
    # 查找各种可能的验证码元素
    selectors_to_check = [
        "#cf-turnstile",
        "#ulp-auth0-v2-captcha", 
        ".ulp-captcha",
        ".ulp-auth0-v2-captcha",
        "[data-captcha-provider]",
        "[data-captcha-sitekey]",
        "iframe[src*='turnstile']",
        "iframe[src*='cloudflare']",
        "div[data-captcha-provider='auth0_v2']",
        ".ulp-captcha-container"
    ]
    
    found_elements = []
    for selector in selectors_to_check:
        try:
            element = registrar.tab.ele(selector, timeout=2)
            if element:
                print(f"✓ 找到元素: {selector}")
                if hasattr(element, 'attrs'):
                    attrs = element.attrs
                    print(f"  属性: {attrs}")
                    if 'data-captcha-provider' in attrs:
                        print(f"  验证码提供商: {attrs['data-captcha-provider']}")
                    if 'data-captcha-sitekey' in attrs:
                        print(f"  站点密钥: {attrs['data-captcha-sitekey']}")
                found_elements.append(selector)
            else:
                print(f"✗ 未找到元素: {selector}")
        except Exception as e:
            print(f"✗ 检查 {selector} 时出错: {str(e)}")
    
    # 检查页面中的iframe
    print("\n检查页面中的iframe...")
    try:
        iframes = registrar.tab.eles("iframe", timeout=3)
        if iframes:
            for i, iframe in enumerate(iframes):
                src = iframe.attr('src') if iframe.attr('src') else 'No src'
                title = iframe.attr('title') if iframe.attr('title') else 'No title'
                print(f"  iframe {i+1}: src={src}, title={title}")
        else:
            print("  未找到iframe")
    except Exception as e:
        print(f"  检查iframe时出错: {str(e)}")
    
    return found_elements

def save_page_html(registrar, filename):
    """保存页面HTML"""
    try:
        with open(filename, "w", encoding="utf-8") as f:
            f.write(registrar.tab.html)
        print(f"已保存页面HTML到 {filename}")
    except Exception as e:
        print(f"保存HTML失败: {str(e)}")

def main():
    """主函数"""
    print("开始测试修复后的 Auth0 V2 Turnstile 验证功能...")
    
    # 测试修复效果
    success = test_auth0_turnstile()
    
    if success:
        print("\n✅ Auth0 V2 Turnstile 验证修复成功！")
    else:
        print("\n❌ Auth0 V2 Turnstile 验证仍需进一步修复")
    
    print("\n测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
