#!/usr/bin/env python3
"""
反检测浏览器管理器
动态修改浏览器指纹以突破网站检测
"""

from DrissionPage import ChromiumOptions, Chromium
import sys
import os
import logging
import random
import json
from dotenv import load_dotenv
from anti_detection_config import AntiDetectionConfig

load_dotenv()


class AntiDetectionBrowserManager:
    """反检测浏览器管理器"""
    
    def __init__(self):
        self.browser = None
        self.config = AntiDetectionConfig()

    def init_browser(self):
        """初始化反检测浏览器"""
        logging.info("初始化反检测浏览器...")
        
        # 随机选择指纹参数
        fingerprint = self.config.generate_random_fingerprint()
        logging.info(f"生成美国指纹: {fingerprint['user_agent'][:50]}...")
        
        # 配置浏览器选项
        co = self._get_anti_detection_options(fingerprint)
        
        # 创建浏览器实例
        self.browser = Chromium(co)
        
        # 应用 JavaScript 指纹修改
        self._apply_js_fingerprint_modifications(fingerprint)
        
        logging.info("反检测浏览器初始化完成")
        return self.browser
    

    
    def _get_anti_detection_options(self, fingerprint):
        """获取反检测浏览器配置"""
        co = ChromiumOptions()
        
        # 基础反检测参数
        co.set_argument("--no-first-run")
        co.set_argument("--no-default-browser-check")
        co.set_argument("--disable-blink-features=AutomationControlled")
        co.set_argument("--disable-features=VizDisplayCompositor")
        co.set_argument("--disable-ipc-flooding-protection")
        co.set_argument("--disable-renderer-backgrounding")
        co.set_argument("--disable-backgrounding-occluded-windows")
        co.set_argument("--disable-client-side-phishing-detection")
        co.set_argument("--disable-sync")
        co.set_argument("--disable-default-apps")
        co.set_argument("--disable-extensions-file-access-check")
        co.set_argument("--disable-extensions-http-throttling")
        co.set_argument("--disable-web-security")
        co.set_argument("--allow-running-insecure-content")
        co.set_argument("--disable-features=TranslateUI")
        co.set_argument("--disable-component-extensions-with-background-pages")
        
        # 设置用户代理
        co.set_user_agent(fingerprint["user_agent"])
        
        # 设置语言
        co.set_argument(f"--lang={fingerprint['language'].split(',')[0]}")
        co.set_pref("intl.accept_languages", fingerprint["language"])
        
        # 设置时区
        co.set_argument(f"--timezone={fingerprint['timezone']}")
        
        # 设置窗口大小
        co.set_argument(f"--window-size={fingerprint['screen_width']},{fingerprint['screen_height']}")
        
        # 禁用自动化检测
        co.set_pref("excludeSwitches", ["enable-automation"])
        co.set_pref("useAutomationExtension", False)
        
        # 设置其他首选项
        co.set_pref("credentials_enable_service", False)
        co.set_pref("profile.password_manager_enabled", False)
        co.set_pref("profile.default_content_setting_values.notifications", 2)
        
        # 加载扩展（如果存在）
        try:
            extension_path = self._get_extension_path("turnstilePatch")
            co.add_extension(extension_path)
            logging.info("已加载 turnstilePatch 扩展")
        except FileNotFoundError as e:
            logging.warning(f"扩展加载失败: {e}")
        
        # 代理设置
        proxy = os.getenv("BROWSER_PROXY")
        if proxy:
            co.set_proxy(proxy)
            logging.info(f"已设置代理: {proxy}")
        
        # 浏览器路径
        browser_path = os.getenv("BROWSER_PATH")
        if browser_path:
            co.set_paths(browser_path=browser_path)
        
        co.auto_port()
        
        # 无头模式设置
        headless = os.getenv("BROWSER_HEADLESS", "False").lower() == "true"
        co.headless(headless)
        
        # Mac 系统特殊处理
        if sys.platform == "darwin":
            co.set_argument("--no-sandbox")
            co.set_argument("--disable-gpu")
        
        return co
    
    def _apply_js_fingerprint_modifications(self, fingerprint):
        """应用 JavaScript 指纹修改"""
        if not self.browser:
            return
            
        # 等待页面加载
        tab = self.browser.latest_tab
        
        # 注入指纹修改脚本
        js_script = f"""
        // 修改 navigator 属性
        Object.defineProperty(navigator, 'webdriver', {{
            get: () => undefined,
        }});
        
        Object.defineProperty(navigator, 'language', {{
            get: () => '{fingerprint['language'].split(',')[0]}',
        }});
        
        Object.defineProperty(navigator, 'languages', {{
            get: () => {json.dumps(fingerprint['language'].split(','))},
        }});
        
        Object.defineProperty(navigator, 'platform', {{
            get: () => '{fingerprint['platform']}',
        }});
        
        Object.defineProperty(navigator, 'hardwareConcurrency', {{
            get: () => {fingerprint['hardware_concurrency']},
        }});
        
        Object.defineProperty(navigator, 'deviceMemory', {{
            get: () => {fingerprint['device_memory']},
        }});
        
        // 修改屏幕属性
        Object.defineProperty(screen, 'width', {{
            get: () => {fingerprint['screen_width']},
        }});
        
        Object.defineProperty(screen, 'height', {{
            get: () => {fingerprint['screen_height']},
        }});
        
        Object.defineProperty(screen, 'availWidth', {{
            get: () => {fingerprint['screen_width']},
        }});
        
        Object.defineProperty(screen, 'availHeight', {{
            get: () => {fingerprint['screen_height'] - 40},
        }});
        
        // 修改时区
        Date.prototype.getTimezoneOffset = function() {{
            return {self.config.get_timezone_offset(fingerprint['timezone'])};
        }};

        // 修改 Intl.DateTimeFormat 时区
        const originalDateTimeFormat = Intl.DateTimeFormat;
        Intl.DateTimeFormat = function(...args) {{
            if (args.length === 0 || !args[1] || !args[1].timeZone) {{
                args[1] = args[1] || {{}};
                args[1].timeZone = '{fingerprint['timezone']}';
            }}
            return new originalDateTimeFormat(...args);
        }};

        // 修改 Intl.DateTimeFormat.prototype.resolvedOptions
        const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
        Intl.DateTimeFormat.prototype.resolvedOptions = function() {{
            const options = originalResolvedOptions.call(this);
            options.timeZone = '{fingerprint['timezone']}';
            return options;
        }};
        
        // 修改 WebGL 指纹 - 更真实的配置
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {{
            if (parameter === 37445) {{  // VENDOR
                return '{fingerprint['webgl_renderer'].split('(')[0].strip()}';
            }}
            if (parameter === 37446) {{  // RENDERER
                return '{fingerprint['webgl_renderer']}';
            }}
            return getParameter(parameter);
        }};

        // 同样修改 WebGL2 上下文
        if (window.WebGL2RenderingContext) {{
            const getParameter2 = WebGL2RenderingContext.prototype.getParameter;
            WebGL2RenderingContext.prototype.getParameter = function(parameter) {{
                if (parameter === 37445) {{
                    return '{fingerprint['webgl_renderer'].split('(')[0].strip()}';
                }}
                if (parameter === 37446) {{
                    return '{fingerprint['webgl_renderer']}';
                }}
                return getParameter2(parameter);
            }};
        }}

        // 添加 Canvas 指纹噪音
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function(...args) {{
            const result = originalToDataURL.apply(this, args);
            // 添加轻微噪音
            const noise = Math.random() * 0.0001;
            return result + noise.toString(36).substr(2, 5);
        }};

        // 添加 AudioContext 指纹噪音
        if (window.AudioContext || window.webkitAudioContext) {{
            const AudioContextClass = window.AudioContext || window.webkitAudioContext;
            const originalCreateAnalyser = AudioContextClass.prototype.createAnalyser;
            AudioContextClass.prototype.createAnalyser = function() {{
                const analyser = originalCreateAnalyser.call(this);
                const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                analyser.getFloatFrequencyData = function(array) {{
                    originalGetFloatFrequencyData.call(this, array);
                    // 添加轻微噪音
                    for (let i = 0; i < array.length; i++) {{
                        array[i] += (Math.random() - 0.5) * 0.0001;
                    }}
                }};
                return analyser;
            }};
        }}

        // 隐藏 WebRTC
        if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {{
            navigator.mediaDevices.enumerateDevices = function() {{
                return Promise.resolve([]);
            }};
        }}

        // 修改字体列表 - 返回真实的字体
        if (document.fonts && document.fonts.check) {{
            const originalCheck = document.fonts.check;
            document.fonts.check = function(font, text) {{
                // 模拟真实的字体检测结果
                const commonFonts = [
                    'Arial', 'Times New Roman', 'Courier New', 'Verdana', 'Georgia',
                    'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS', 'Trebuchet MS',
                    'Arial Black', 'Impact', 'Lucida Sans Unicode', 'Tahoma', 'Lucida Console'
                ];
                const fontFamily = font.split(' ')[0].replace(/['"]/g, '');
                return commonFonts.includes(fontFamily) ? true : originalCheck.call(this, font, text);
            }};
        }}

        // 隐藏自动化痕迹
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        delete window.chrome.runtime.onConnect;
        delete window.chrome.runtime.onMessage;
        
        console.log('反检测指纹已应用');
        """
        
        try:
            tab.run_js(js_script)
            logging.info("JavaScript 指纹修改已应用")
        except Exception as e:
            logging.warning(f"JavaScript 指纹修改失败: {e}")
    

    
    def _get_extension_path(self, exname='turnstilePatch'):
        """获取插件路径"""
        root_dir = os.getcwd()
        extension_path = os.path.join(root_dir, exname)

        if hasattr(sys, "_MEIPASS"):
            extension_path = os.path.join(sys._MEIPASS, exname)

        if not os.path.exists(extension_path):
            raise FileNotFoundError(f"插件不存在: {extension_path}")

        return extension_path
    
    def quit(self):
        """关闭浏览器"""
        if self.browser:
            try:
                self.browser.quit()
                logging.info("反检测浏览器已关闭")
            except:
                pass
