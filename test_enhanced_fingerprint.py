#!/usr/bin/env python3
"""
测试增强的浏览器指纹和 Turnstile 验证
"""

import os
import sys
import time
from logger import logging
from augmentcode_register import AugmentCodeRegister, generate_email_with_timestamp

def main():
    """测试增强的浏览器指纹和 Turnstile 验证"""
    try:
        logging.info("=" * 80)
        logging.info("测试增强的浏览器指纹和 Turnstile 验证")
        logging.info("=" * 80)
        
        # 检查环境配置
        proxy = os.getenv("BROWSER_PROXY", "").strip()
        if proxy:
            logging.info(f"✅ 代理配置: {proxy}")
        else:
            logging.warning("⚠️  未配置代理，建议设置 BROWSER_PROXY=http://127.0.0.1:1080")
        
        # 生成邮箱地址
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱地址: {email}")
        
        # 创建注册器
        register = AugmentCodeRegister(email)
        
        # 初始化浏览器
        logging.info("🚀 初始化浏览器...")
        from browser_utils import BrowserManager
        register.browser_manager = BrowserManager()
        
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6876.4 Safari/537.36"
        browser = register.browser_manager.init_browser(user_agent)
        register.tab = browser.latest_tab
        
        # 应用增强的美国地区设置和反检测指纹
        logging.info("🛡️ 应用增强的反检测指纹...")
        register._apply_us_region_settings()
        
        # 验证指纹修改是否生效
        logging.info("🔍 验证指纹修改效果...")
        fingerprint_check = register.tab.run_js("""
            return {
                language: navigator.language,
                languages: navigator.languages,
                screenWidth: screen.width,
                screenHeight: screen.height,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                webglVendor: (() => {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl');
                        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                        return gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                    } catch(e) { return 'Error: ' + e.message; }
                })(),
                webglRenderer: (() => {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl');
                        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                        return gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                    } catch(e) { return 'Error: ' + e.message; }
                })()
            };
        """)
        
        logging.info("📊 当前浏览器指纹:")
        logging.info(f"  语言: {fingerprint_check.get('language')}")
        logging.info(f"  语言列表: {fingerprint_check.get('languages')}")
        logging.info(f"  屏幕尺寸: {fingerprint_check.get('screenWidth')}x{fingerprint_check.get('screenHeight')}")
        logging.info(f"  时区: {fingerprint_check.get('timezone')}")
        logging.info(f"  WebGL 供应商: {fingerprint_check.get('webglVendor')}")
        logging.info(f"  WebGL 渲染器: {fingerprint_check.get('webglRenderer')}")
        
        # 执行第一步：访问页面
        logging.info("📋 执行第一步：访问页面...")
        if not register.step1_click_get_free_month():
            logging.error("第一步失败")
            return False
        
        # 执行 Turnstile 验证（使用优化的方法）
        logging.info("🎯 开始执行优化的 Turnstile 验证...")
        success = register.handle_turnstile_verification(max_retries=1)
        
        if success:
            logging.info("🎉 Turnstile 验证成功！")
            
            # 继续执行完整的注册流程
            logging.info("📋 继续执行完整注册流程...")
            
            # 第三步：输入邮箱
            if register.step3_input_email():
                logging.info("✅ 第三步：输入邮箱成功")
                
                # 第四步：点击继续
                if register.step4_click_continue():
                    logging.info("✅ 第四步：点击继续成功")
                    
                    # 等待页面跳转
                    time.sleep(3)
                    current_url = register.tab.url
                    logging.info(f"当前页面: {current_url}")
                    
                    # 检查是否遇到地区限制
                    if "increased demand" in register.tab.html.lower() or "limiting signups" in register.tab.html.lower():
                        logging.warning("⚠️ 遇到地区限制，但 Turnstile 验证已成功")
                        logging.info("🎯 增强的反检测指纹可能需要进一步优化")
                    else:
                        logging.info("✅ 未遇到地区限制，继续后续步骤...")
                        
                        # 第五步：输入验证码
                        if register.step5_input_verification_code():
                            logging.info("✅ 第五步：输入验证码成功")
                            
                            # 第六步：上传文件
                            if register.step6_upload_file():
                                logging.info("🎉 完整注册流程成功！")
                            else:
                                logging.warning("第六步失败，但前面步骤都成功了")
                        else:
                            logging.warning("第五步失败")
                else:
                    logging.warning("第四步失败")
            else:
                logging.warning("第三步失败")
        else:
            logging.error("😞 Turnstile 验证失败！")
            
            # 保存失败状态用于分析
            register.save_screenshot("enhanced_fingerprint_failed")
            register.save_shadow_root_html("enhanced_fingerprint_failed")
            register._log_turnstile_debug_info("增强指纹验证失败")
        
        # 保持浏览器打开一段时间供手动检查
        logging.info("🔍 保持浏览器打开 30 秒供手动检查...")
        time.sleep(30)
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        # 清理资源
        try:
            if 'register' in locals() and register.browser_manager:
                register.browser_manager.quit()
        except:
            pass

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 增强指纹测试成功！")
        print("✅ Turnstile 验证和反检测功能正常")
        sys.exit(0)
    else:
        print("\n😞 增强指纹测试失败！")
        print("❌ 需要进一步优化反检测策略")
        sys.exit(1)
