#!/usr/bin/env python3
"""
测试条款同意页面处理
验证我们能正确处理条款同意和注册按钮点击
"""

import time
from logger import logging

def test_terms_agreement():
    """测试条款同意页面处理"""
    try:
        logging.info("🧪 测试条款同意页面处理...")
        
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        logging.info("✅ 浏览器初始化成功")
        
        # 访问页面
        logging.info("🌐 访问 Cursor 资源页面...")
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(3)
        
        # 点击按钮
        logging.info("🖱️ 点击 Get your free month 按钮...")
        button = tab.ele("@text()=Get your free month", timeout=5)
        if button:
            button.click()
            time.sleep(5)
            logging.info("✅ 按钮点击成功")
        else:
            logging.error("❌ 未找到按钮")
            return False
        
        # 等待页面加载并检查当前状态
        logging.info("⏳ 等待页面加载...")
        time.sleep(5)
        
        # 检查是否在条款同意页面
        terms_checkbox = tab.ele("#terms-of-service-checkbox", timeout=5)
        if terms_checkbox:
            logging.info("🔍 检测到条款同意页面")
            
            # 检查复选框状态
            is_checked = terms_checkbox.attr("checked")
            logging.info(f"📋 条款复选框状态: {'已勾选' if is_checked else '未勾选'}")
            
            # 如果未勾选，则勾选
            if not is_checked:
                logging.info("☑️ 勾选条款同意复选框...")
                terms_checkbox.click()
                time.sleep(1)
                
                # 验证勾选结果
                is_checked_after = terms_checkbox.attr("checked")
                logging.info(f"✅ 勾选后状态: {'已勾选' if is_checked_after else '未勾选'}")
                
                if is_checked_after:
                    logging.info("🎉 条款复选框勾选成功！")
                else:
                    logging.warning("⚠️ 条款复选框勾选失败")
            
            # 检查注册按钮
            signup_button = tab.ele("#signup-button", timeout=3)
            if signup_button:
                is_disabled = signup_button.attr("disabled")
                button_text = signup_button.text
                
                logging.info(f"🔍 注册按钮状态:")
                logging.info(f"   - 文本: '{button_text}'")
                logging.info(f"   - 禁用: {is_disabled}")
                
                if is_disabled:
                    logging.info("⏳ 注册按钮禁用，等待启用...")
                    
                    # 等待按钮启用
                    for i in range(10):
                        time.sleep(1)
                        is_disabled = signup_button.attr("disabled")
                        if not is_disabled:
                            logging.info(f"✅ 注册按钮在 {i+1} 秒后启用")
                            break
                        logging.debug(f"等待按钮启用... ({i+1}/10)")
                    
                    if is_disabled:
                        logging.warning("⚠️ 注册按钮仍然禁用")
                
                if not signup_button.attr("disabled"):
                    logging.info("🖱️ 点击注册按钮...")
                    
                    # 截图记录点击前状态
                    tab.get_screenshot(path=f"before_signup_click_{int(time.time())}.png")
                    
                    signup_button.click()
                    time.sleep(3)
                    
                    # 截图记录点击后状态
                    tab.get_screenshot(path=f"after_signup_click_{int(time.time())}.png")
                    
                    logging.info("✅ 注册按钮点击成功")
                    
                    # 检查页面是否跳转
                    current_url = tab.url
                    logging.info(f"🔍 点击后 URL: {current_url}")
                    
                    # 等待一下看是否有新的页面元素
                    time.sleep(5)
                    
                    # 检查是否出现邮箱输入框
                    email_input = tab.ele("@type=email", timeout=3)
                    if email_input:
                        logging.info("🎉 检测到邮箱输入框，注册流程继续！")
                        return True
                    else:
                        logging.info("ℹ️ 未检测到邮箱输入框，可能需要等待更长时间")
                        
                        # 等待更长时间
                        logging.info("⏳ 等待页面完全加载...")
                        time.sleep(10)
                        
                        email_input = tab.ele("@type=email", timeout=5)
                        if email_input:
                            logging.info("🎉 延迟检测到邮箱输入框！")
                            return True
                        else:
                            logging.warning("⚠️ 仍未检测到邮箱输入框")
                            
                            # 截图记录最终状态
                            tab.get_screenshot(path=f"final_state_{int(time.time())}.png")
                            
                            # 让用户手动检查
                            input("请手动检查页面状态，按 Enter 继续...")
                            return True
                else:
                    logging.error("❌ 注册按钮仍然禁用，无法点击")
                    return False
            else:
                logging.error("❌ 未找到注册按钮")
                return False
        else:
            logging.info("ℹ️ 未检测到条款同意页面，可能已在其他页面")
            
            # 检查当前页面状态
            current_url = tab.url
            logging.info(f"🔍 当前 URL: {current_url}")
            
            # 截图记录当前状态
            tab.get_screenshot(path=f"current_page_{int(time.time())}.png")
            
            input("请手动检查页面状态，按 Enter 继续...")
            return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        try:
            browser_manager.quit()
        except:
            pass

def main():
    """主函数"""
    logging.info("=" * 80)
    logging.info("条款同意页面处理测试")
    logging.info("=" * 80)
    
    success = test_terms_agreement()
    
    if success:
        logging.info("🎉 条款同意页面处理测试成功！")
    else:
        logging.error("❌ 条款同意页面处理测试失败！")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
