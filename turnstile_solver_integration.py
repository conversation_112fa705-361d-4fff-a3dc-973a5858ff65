#!/usr/bin/env python3
"""
集成专业的 Turnstile Solver 库来解决人机验证问题
基于 Theyka/Turnstile-Solver 项目
"""

import os
import sys
import time
import subprocess
import requests
import json
from logger import logging

class TurnstileSolverIntegration:
    """集成专业的 Turnstile Solver"""
    
    def __init__(self, solver_api_url: str = "http://127.0.0.1:5000"):
        """
        初始化 Turnstile Solver 集成
        
        Args:
            solver_api_url: Turnstile Solver API 地址
        """
        self.solver_api_url = solver_api_url
        self.solver_process = None
        
    def install_turnstile_solver(self) -> bool:
        """
        安装 Turnstile Solver 依赖
        
        Returns:
            bool: 安装是否成功
        """
        try:
            logging.info("🔧 开始安装 Turnstile Solver 依赖...")
            
            # 安装 patchright
            subprocess.run([sys.executable, "-m", "pip", "install", "patchright"], check=True)
            logging.info("✅ patchright 安装成功")
            
            # 安装 camoufox
            subprocess.run([sys.executable, "-m", "pip", "install", "camoufox"], check=True)
            logging.info("✅ camoufox 安装成功")
            
            # 安装浏览器
            subprocess.run([sys.executable, "-m", "patchright", "install", "chromium"], check=True)
            logging.info("✅ chromium 安装成功")
            
            # 获取 camoufox 浏览器
            subprocess.run([sys.executable, "-m", "camoufox", "fetch"], check=True)
            logging.info("✅ camoufox 浏览器获取成功")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logging.error(f"❌ 安装 Turnstile Solver 依赖失败: {e}")
            return False
        except Exception as e:
            logging.error(f"❌ 安装过程中发生错误: {e}")
            return False
    
    def download_turnstile_solver(self) -> bool:
        """
        下载 Turnstile Solver 代码
        
        Returns:
            bool: 下载是否成功
        """
        try:
            solver_dir = "Turnstile-Solver"
            
            if os.path.exists(solver_dir):
                logging.info(f"✅ {solver_dir} 目录已存在")
                return True
            
            logging.info("📥 下载 Turnstile Solver 代码...")
            
            # 克隆仓库
            subprocess.run([
                "git", "clone", 
                "https://github.com/Theyka/Turnstile-Solver.git",
                solver_dir
            ], check=True)
            
            logging.info("✅ Turnstile Solver 代码下载成功")
            return True
            
        except subprocess.CalledProcessError as e:
            logging.error(f"❌ 下载 Turnstile Solver 失败: {e}")
            return False
        except Exception as e:
            logging.error(f"❌ 下载过程中发生错误: {e}")
            return False
    
    def start_solver_api(self) -> bool:
        """
        启动 Turnstile Solver API 服务
        
        Returns:
            bool: 启动是否成功
        """
        try:
            solver_dir = "Turnstile-Solver"
            
            if not os.path.exists(solver_dir):
                logging.error(f"❌ {solver_dir} 目录不存在，请先下载")
                return False
            
            logging.info("🚀 启动 Turnstile Solver API 服务...")
            
            # 启动 API 服务（使用 camoufox 浏览器）
            api_script = os.path.join(solver_dir, "api_solver.py")
            
            self.solver_process = subprocess.Popen([
                sys.executable, api_script,
                "--browser_type", "camoufox",
                "--host", "127.0.0.1",
                "--port", "5000",
                "--proxy", "True"  # 使用代理
            ], cwd=solver_dir)
            
            # 等待服务启动
            time.sleep(10)
            
            # 检查服务是否启动成功
            if self.check_solver_api():
                logging.info("✅ Turnstile Solver API 服务启动成功")
                return True
            else:
                logging.error("❌ Turnstile Solver API 服务启动失败")
                return False
                
        except Exception as e:
            logging.error(f"❌ 启动 Turnstile Solver API 失败: {e}")
            return False
    
    def check_solver_api(self) -> bool:
        """
        检查 Turnstile Solver API 是否可用
        
        Returns:
            bool: API 是否可用
        """
        try:
            response = requests.get(f"{self.solver_api_url}/", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def solve_turnstile(self, url: str, sitekey: str, action: str = None, cdata: str = None) -> str:
        """
        使用专业的 Turnstile Solver 解决验证
        
        Args:
            url: 目标页面 URL
            sitekey: Turnstile 站点密钥
            action: 可选的动作参数
            cdata: 可选的自定义数据
            
        Returns:
            str: 验证令牌，失败返回 None
        """
        try:
            logging.info(f"🎯 开始使用专业 Turnstile Solver 解决验证...")
            logging.info(f"  URL: {url}")
            logging.info(f"  Sitekey: {sitekey}")
            
            # 构建请求参数
            params = {
                "url": url,
                "sitekey": sitekey
            }
            
            if action:
                params["action"] = action
            if cdata:
                params["cdata"] = cdata
            
            # 提交验证任务
            response = requests.get(f"{self.solver_api_url}/turnstile", params=params, timeout=30)
            
            if response.status_code != 200:
                logging.error(f"❌ 提交验证任务失败: {response.status_code}")
                return None
            
            task_data = response.json()
            task_id = task_data.get("task_id")
            
            if not task_id:
                logging.error("❌ 未获取到任务 ID")
                return None
            
            logging.info(f"✅ 验证任务已提交，任务 ID: {task_id}")
            
            # 轮询获取结果
            max_wait_time = 60  # 最多等待 60 秒
            check_interval = 3  # 每 3 秒检查一次
            waited_time = 0
            
            while waited_time < max_wait_time:
                time.sleep(check_interval)
                waited_time += check_interval
                
                # 获取结果
                result_response = requests.get(
                    f"{self.solver_api_url}/result", 
                    params={"id": task_id}, 
                    timeout=10
                )
                
                if result_response.status_code == 200:
                    result_data = result_response.json()
                    
                    if "value" in result_data:
                        token = result_data["value"]
                        elapsed_time = result_data.get("elapsed_time", 0)
                        
                        logging.info(f"🎉 Turnstile 验证成功！")
                        logging.info(f"  令牌: {token}")
                        logging.info(f"  耗时: {elapsed_time:.2f} 秒")
                        
                        return token
                
                logging.info(f"⏳ 等待验证完成... {waited_time}/{max_wait_time}s")
            
            logging.error(f"❌ Turnstile 验证超时（{max_wait_time}s）")
            return None
            
        except Exception as e:
            logging.error(f"❌ Turnstile 验证过程中发生错误: {e}")
            return None
    
    def stop_solver_api(self) -> None:
        """停止 Turnstile Solver API 服务"""
        try:
            if self.solver_process:
                self.solver_process.terminate()
                self.solver_process.wait(timeout=10)
                logging.info("✅ Turnstile Solver API 服务已停止")
        except Exception as e:
            logging.warning(f"⚠️ 停止 Turnstile Solver API 服务时出错: {e}")
    
    def __del__(self):
        """析构函数，确保服务被停止"""
        self.stop_solver_api()

def main():
    """测试 Turnstile Solver 集成"""
    solver = TurnstileSolverIntegration()
    
    try:
        # 1. 安装依赖
        if not solver.install_turnstile_solver():
            return False
        
        # 2. 下载代码
        if not solver.download_turnstile_solver():
            return False
        
        # 3. 启动 API 服务
        if not solver.start_solver_api():
            return False
        
        # 4. 测试验证（使用示例参数）
        test_url = "https://login.augmentcode.com/u/login/identifier"
        test_sitekey = "0x4AAAAAAAQFNSW6xordsuIq"  # 从页面获取的实际 sitekey
        
        token = solver.solve_turnstile(test_url, test_sitekey)
        
        if token:
            print(f"🎉 测试成功！获取到令牌: {token}")
            return True
        else:
            print("😞 测试失败！")
            return False
            
    finally:
        solver.stop_solver_api()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
