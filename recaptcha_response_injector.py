#!/usr/bin/env python3
"""
reCAPTCHA Response 注入器
用于直接注入有效的 g-recaptcha-response 和 verisoul-session-id
"""

import time
from logger import logging

class RecaptchaResponseInjector:
    """reCAPTCHA Response 注入器"""

    def __init__(self, tab):
        self.tab = tab

    def inject_response(self, g_recaptcha_response: str, verisoul_session_id: str = None) -> bool:
        """
        注入有效的 reCAPTCHA response

        Args:
            g_recaptcha_response: 有效的 g-recaptcha-response 值
            verisoul_session_id: 有效的 verisoul-session-id 值（可选）

        Returns:
            bool: 注入是否成功
        """
        try:
            logging.info("🔧 开始注入 reCAPTCHA response...")

            # 方法1: 直接设置隐藏字段值
            success1 = self._inject_hidden_fields(g_recaptcha_response, verisoul_session_id)

            # 方法2: 模拟 reCAPTCHA 回调
            success2 = self._simulate_recaptcha_callback(g_recaptcha_response)

            # 方法3: 直接修改表单数据
            success3 = self._modify_form_data(g_recaptcha_response, verisoul_session_id)

            if success1 or success2 or success3:
                logging.info("✅ reCAPTCHA response 注入成功")
                return True
            else:
                logging.error("❌ reCAPTCHA response 注入失败")
                return False

        except Exception as e:
            logging.error(f"❌ 注入过程异常: {e}")
            return False

    def _inject_hidden_fields(self, g_recaptcha_response: str, verisoul_session_id: str = None) -> bool:
        """注入隐藏字段"""
        try:
            logging.info("🔧 方法1: 注入隐藏字段...")

            js_code = f"""
                // 查找或创建 g-recaptcha-response 字段
                let recaptchaField = document.querySelector('textarea[name="g-recaptcha-response"]');
                if (!recaptchaField) {{
                    // 创建隐藏的 textarea
                    recaptchaField = document.createElement('textarea');
                    recaptchaField.name = 'g-recaptcha-response';
                    recaptchaField.style.display = 'none';
                    document.body.appendChild(recaptchaField);
                    console.log('已创建 g-recaptcha-response 字段');
                }}

                // 设置值
                recaptchaField.value = '{g_recaptcha_response}';
                console.log('已设置 g-recaptcha-response:', recaptchaField.value.substring(0, 50) + '...');

                // 触发 change 事件
                const event = new Event('change', {{ bubbles: true }});
                recaptchaField.dispatchEvent(event);
            """

            if verisoul_session_id:
                js_code += f"""
                    // 查找或创建 verisoul-session-id 字段
                    let verisoulField = document.querySelector('input[name="verisoul-session-id"]');
                    if (!verisoulField) {{
                        verisoulField = document.createElement('input');
                        verisoulField.type = 'hidden';
                        verisoulField.name = 'verisoul-session-id';
                        document.body.appendChild(verisoulField);
                        console.log('已创建 verisoul-session-id 字段');
                    }}

                    verisoulField.value = '{verisoul_session_id}';
                    console.log('已设置 verisoul-session-id:', verisoulField.value);
                """

            js_code += """
                return {
                    recaptchaSet: !!document.querySelector('textarea[name="g-recaptcha-response"]').value,
                    verisoulSet: !!document.querySelector('input[name="verisoul-session-id"]')?.value
                };
            """

            result = self.tab.run_js(js_code)

            if result and result.get('recaptchaSet'):
                logging.info("✅ 隐藏字段注入成功")
                return True
            else:
                logging.warning("⚠️ 隐藏字段注入失败")
                return False

        except Exception as e:
            logging.error(f"❌ 隐藏字段注入异常: {e}")
            return False

    def _simulate_recaptcha_callback(self, g_recaptcha_response: str) -> bool:
        """模拟 reCAPTCHA 回调"""
        try:
            logging.info("🔧 方法2: 模拟 reCAPTCHA 回调...")

            js_code = f"""
                // 查找 reCAPTCHA 回调函数
                const possibleCallbacks = [
                    'onRecaptchaSuccess',
                    'recaptchaCallback',
                    'onCaptchaSuccess',
                    'handleRecaptcha',
                    'recaptchaVerified'
                ];

                let callbackExecuted = false;

                for (const callbackName of possibleCallbacks) {{
                    if (typeof window[callbackName] === 'function') {{
                        try {{
                            window[callbackName]('{g_recaptcha_response}');
                            console.log('已执行回调函数:', callbackName);
                            callbackExecuted = true;
                        }} catch (e) {{
                            console.log('回调函数执行失败:', callbackName, e);
                        }}
                    }}
                }}

                // 尝试触发 grecaptcha 回调
                if (typeof grecaptcha !== 'undefined' && grecaptcha.getResponse) {{
                    try {{
                        // 模拟设置 response
                        const widgets = document.querySelectorAll('.g-recaptcha');
                        widgets.forEach(widget => {{
                            const widgetId = widget.getAttribute('data-widget-id');
                            if (widgetId) {{
                                // 尝试设置 response
                                console.log('尝试为 widget', widgetId, '设置 response');
                            }}
                        }});
                        callbackExecuted = true;
                    }} catch (e) {{
                        console.log('grecaptcha 操作失败:', e);
                    }}
                }}

                return {{ callbackExecuted: callbackExecuted }};
            """

            result = self.tab.run_js(js_code)

            if result and result.get('callbackExecuted'):
                logging.info("✅ reCAPTCHA 回调模拟成功")
                return True
            else:
                logging.warning("⚠️ reCAPTCHA 回调模拟失败")
                return False

        except Exception as e:
            logging.error(f"❌ reCAPTCHA 回调模拟异常: {e}")
            return False

    def _modify_form_data(self, g_recaptcha_response: str, verisoul_session_id: str = None) -> bool:
        """修改表单数据"""
        try:
            logging.info("🔧 方法3: 修改表单数据...")

            js_code = f"""
                // 查找所有表单
                const forms = document.querySelectorAll('form');
                let formModified = false;

                forms.forEach(form => {{
                    // 添加或修改 g-recaptcha-response
                    let recaptchaInput = form.querySelector('textarea[name="g-recaptcha-response"]');
                    if (!recaptchaInput) {{
                        recaptchaInput = document.createElement('textarea');
                        recaptchaInput.name = 'g-recaptcha-response';
                        recaptchaInput.style.display = 'none';
                        form.appendChild(recaptchaInput);
                    }}
                    recaptchaInput.value = '{g_recaptcha_response}';
                    formModified = true;
                    console.log('已修改表单中的 g-recaptcha-response');
            """

            if verisoul_session_id:
                js_code += f"""
                    // 添加或修改 verisoul-session-id
                    let verisoulInput = form.querySelector('input[name="verisoul-session-id"]');
                    if (!verisoulInput) {{
                        verisoulInput = document.createElement('input');
                        verisoulInput.type = 'hidden';
                        verisoulInput.name = 'verisoul-session-id';
                        form.appendChild(verisoulInput);
                    }}
                    verisoulInput.value = '{verisoul_session_id}';
                    console.log('已修改表单中的 verisoul-session-id');
                """

            js_code += """
                });

                return { formModified: formModified };
            """

            result = self.tab.run_js(js_code)

            if result and result.get('formModified'):
                logging.info("✅ 表单数据修改成功")
                return True
            else:
                logging.warning("⚠️ 表单数据修改失败")
                return False

        except Exception as e:
            logging.error(f"❌ 表单数据修改异常: {e}")
            return False

    def verify_injection(self) -> dict:
        """验证注入是否成功"""
        try:
            result = self.tab.run_js("""
                return {
                    recaptchaResponse: document.querySelector('textarea[name="g-recaptcha-response"]')?.value || null,
                    verisoulSessionId: document.querySelector('input[name="verisoul-session-id"]')?.value || null,
                    hasRecaptchaField: !!document.querySelector('textarea[name="g-recaptcha-response"]'),
                    hasVerisoulField: !!document.querySelector('input[name="verisoul-session-id"]')
                };
            """)

            return result or {}

        except Exception as e:
            logging.error(f"❌ 验证注入状态异常: {e}")
            return {}