#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行为模式深度模拟注册脚本
基于方案3：针对g-recaptcha-response和verisoul-session-id的行为模拟绕过
包含完整日志记录和网页内容保存功能
"""

import os
import sys
import time
import json
import random
import math
from datetime import datetime
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from browser_utils import BrowserManager
from augmentcode_register import generate_email_with_timestamp, AugmentCodeRegister

class BehaviorSimulationRegister:
    def __init__(self):
        self.browser_manager = BrowserManager()
        self.browser = None
        self.tab = None
        self.email = None
        self.verification_code = None
        self.augment_register = None  # 将在生成邮箱后初始化
        
        # 创建日志目录
        self.log_dir = Path("logs/behavior_simulation")
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建网页内容保存目录
        self.content_dir = Path("logs/behavior_simulation/page_contents")
        self.content_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 行为模拟参数
        self.setup_behavior_params()
    
    def setup_logging(self):
        """设置详细日志记录"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = self.log_dir / f"behavior_simulation_{timestamp}.log"
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("=== 行为模式深度模拟注册开始 ===")
    
    def setup_behavior_params(self):
        """设置行为模拟参数"""
        # 美国用户典型行为参数
        self.behavior_config = {
            # 鼠标移动参数
            "mouse_speed": random.uniform(0.8, 1.5),  # 鼠标移动速度
            "mouse_curve": random.uniform(0.1, 0.3),  # 鼠标轨迹弯曲度
            "click_delay": random.uniform(0.1, 0.3),  # 点击前停顿
            
            # 打字参数
            "typing_speed": random.uniform(80, 120),  # WPM (美国平均打字速度)
            "typing_variation": random.uniform(0.2, 0.4),  # 打字速度变化
            "backspace_probability": 0.05,  # 退格概率
            
            # 浏览行为参数
            "scroll_speed": random.uniform(300, 800),  # 滚动速度
            "read_pause": random.uniform(1.5, 3.0),  # 阅读停顿时间
            "form_fill_pause": random.uniform(0.5, 1.5),  # 表单填写间隔
        }
        
        self.logger.info(f"行为参数配置: {json.dumps(self.behavior_config, indent=2, ensure_ascii=False)}")
    
    def save_page_content(self, step_name, additional_info=None):
        """保存当前页面内容和信息"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]

            # 保存HTML内容
            html_content = self.tab.html
            html_file = self.content_dir / f"{step_name}_{timestamp}.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # 保存纯文本内容
            try:
                text_content = self.tab.get_text()
                text_file = self.content_dir / f"{step_name}_{timestamp}.txt"
                with open(text_file, 'w', encoding='utf-8') as f:
                    f.write(text_content)
            except Exception as e:
                self.logger.warning(f"保存文本内容失败: {e}")

            # 保存页面信息
            page_info = {
                "step": step_name,
                "timestamp": timestamp,
                "url": self.tab.url,
                "title": self.tab.title,
                "additional_info": additional_info or {}
            }
            
            info_file = self.content_dir / f"{step_name}_{timestamp}_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(page_info, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"页面内容已保存: {step_name} - {self.tab.url}")
            return True

        except Exception as e:
            self.logger.error(f"保存页面内容失败: {e}")
            return False
    
    def human_like_delay(self, min_delay=0.5, max_delay=2.0):
        """人类行为延迟"""
        delay = random.uniform(min_delay, max_delay)
        self.logger.debug(f"人类行为延迟: {delay:.2f}秒")
        time.sleep(delay)
    
    def simulate_mouse_movement(self, element):
        """模拟真实鼠标移动到元素"""
        try:
            # 获取元素位置
            rect = element.rect
            target_x = rect.center[0]
            target_y = rect.center[1]
            
            # 添加随机偏移（模拟人类不精确点击）
            offset_x = random.uniform(-5, 5)
            offset_y = random.uniform(-5, 5)
            
            # 模拟鼠标移动轨迹（简化版贝塞尔曲线）
            steps = random.randint(8, 15)
            for i in range(steps):
                progress = i / steps
                # 添加轻微的弯曲
                curve_offset = math.sin(progress * math.pi) * self.behavior_config["mouse_curve"] * 20
                time.sleep(0.01 + random.uniform(0, 0.02))
            
            # 最终移动到目标位置
            element.hover()
            self.human_like_delay(0.1, 0.3)
            
            self.logger.debug(f"鼠标移动到元素: {element.tag} - 位置({target_x}, {target_y})")
            return True
            
        except Exception as e:
            self.logger.warning(f"鼠标移动模拟失败: {e}")
            return False
    
    def human_like_click(self, element):
        """人类行为点击"""
        try:
            # 先模拟鼠标移动
            self.simulate_mouse_movement(element)
            
            # 点击前短暂停顿
            self.human_like_delay(
                self.behavior_config["click_delay"], 
                self.behavior_config["click_delay"] + 0.2
            )
            
            # 执行点击
            element.click()
            
            # 点击后短暂停顿
            self.human_like_delay(0.2, 0.5)
            
            self.logger.info(f"人类行为点击: {element.tag}")
            return True
            
        except Exception as e:
            self.logger.error(f"人类行为点击失败: {e}")
            return False
    
    def human_like_typing(self, element, text):
        """人类行为打字"""
        try:
            # 先点击输入框
            self.human_like_click(element)
            
            # 清空现有内容
            element.clear()
            self.human_like_delay(0.2, 0.4)
            
            # 模拟人类打字
            base_delay = 60.0 / self.behavior_config["typing_speed"]  # 基础打字间隔
            
            for i, char in enumerate(text):
                # 计算当前字符的打字延迟
                variation = random.uniform(
                    -self.behavior_config["typing_variation"], 
                    self.behavior_config["typing_variation"]
                )
                char_delay = base_delay * (1 + variation)
                
                # 偶尔模拟退格和重新输入
                if random.random() < self.behavior_config["backspace_probability"] and i > 0:
                    element.input('\b')  # 退格
                    time.sleep(char_delay)
                    element.input(char)  # 重新输入
                    time.sleep(char_delay)
                
                element.input(char)
                time.sleep(char_delay)
            
            # 输入完成后短暂停顿
            self.human_like_delay(0.3, 0.8)
            
            self.logger.info(f"人类行为输入完成: {text}")
            return True
            
        except Exception as e:
            self.logger.error(f"人类行为输入失败: {e}")
            return False
    
    def inject_us_behavior_fingerprint(self):
        """注入美国用户行为指纹"""
        fingerprint_js = """
        // === 美国用户行为指纹模拟 ===
        
        // 1. 地理位置伪装（纽约）
        const US_COORDS = {
            latitude: 40.7128,
            longitude: -74.0060,
            accuracy: 10
        };
        
        if (navigator.geolocation) {
            const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
            navigator.geolocation.getCurrentPosition = function(success, error, options) {
                if (success) {
                    success({
                        coords: US_COORDS,
                        timestamp: Date.now()
                    });
                }
            };
        }
        
        // 2. 时区伪装（美国东部时区 UTC-5）
        const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
        Date.prototype.getTimezoneOffset = function() {
            return 300; // EST: UTC-5
        };
        
        // 3. 语言环境伪装
        Object.defineProperty(navigator, 'language', {
            get: function() { return 'en-US'; }
        });
        
        Object.defineProperty(navigator, 'languages', {
            get: function() { return ['en-US', 'en']; }
        });
        
        // 4. 屏幕和设备信息（美国常见配置）
        Object.defineProperty(screen, 'width', {
            get: function() { return 1920; }
        });
        
        Object.defineProperty(screen, 'height', {
            get: function() { return 1080; }
        });
        
        Object.defineProperty(screen, 'availWidth', {
            get: function() { return 1920; }
        });
        
        Object.defineProperty(screen, 'availHeight', {
            get: function() { return 1040; }
        });
        
        // 5. 用户代理和平台信息
        Object.defineProperty(navigator, 'platform', {
            get: function() { return 'Win32'; }
        });
        
        // 6. 硬件并发数（美国常见配置）
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: function() { return 8; }
        });
        
        // 7. 内存信息（如果支持）
        if (navigator.deviceMemory !== undefined) {
            Object.defineProperty(navigator, 'deviceMemory', {
                get: function() { return 8; }
            });
        }
        
        // 8. 连接信息伪装
        if (navigator.connection) {
            Object.defineProperty(navigator.connection, 'effectiveType', {
                get: function() { return '4g'; }
            });
            
            Object.defineProperty(navigator.connection, 'downlink', {
                get: function() { return 10; }
            });
        }
        
        console.log('美国用户行为指纹注入完成');
        """
        
        try:
            self.tab.run_js(fingerprint_js)
            self.logger.info("美国用户行为指纹注入成功")
            return True
        except Exception as e:
            self.logger.error(f"指纹注入失败: {e}")
            return False

    def step1_visit_promotion_page(self):
        """步骤1: 访问推广页面"""
        try:
            self.logger.info("=== 步骤1: 访问推广页面 ===")

            # 初始化浏览器
            self.browser = self.browser_manager.init_browser()
            if not self.browser:
                raise Exception("浏览器初始化失败")

            # 获取标签页
            self.tab = self.browser.latest_tab

            # 注入美国用户指纹
            self.inject_us_behavior_fingerprint()

            # 访问推广页面
            url = "https://www.augmentcode.com/resources/cursor"
            self.logger.info(f"访问URL: {url}")
            self.tab.get(url)

            # 模拟真实用户浏览行为
            self.human_like_delay(2.0, 4.0)  # 页面加载后的阅读时间

            # 保存页面内容
            self.save_page_content("step1_promotion_page", {
                "action": "访问推广页面",
                "url": url
            })

            # 模拟滚动浏览
            self.tab.scroll.to_bottom()
            self.human_like_delay(1.0, 2.0)
            self.tab.scroll.to_top()
            self.human_like_delay(0.5, 1.0)

            self.logger.info("步骤1完成: 推广页面访问成功")
            return True

        except Exception as e:
            self.logger.error(f"步骤1失败: {e}")
            self.save_page_content("step1_error", {"error": str(e)})
            return False

    def step2_click_registration_button(self):
        """步骤2: 点击注册按钮"""
        try:
            self.logger.info("=== 步骤2: 点击注册按钮 ===")

            # 查找注册按钮
            button_texts = [
                "Get your free month", "Claim offer", "Get started",
                "Continue", "Sign up", "Register", "Start free trial", "Redeem"
            ]

            button_found = False
            for text in button_texts:
                try:
                    # 尝试多种选择器
                    selectors = [
                        f'button:contains("{text}")',
                        f'a:contains("{text}")',
                        f'[role="button"]:contains("{text}")',
                        f'.btn:contains("{text}")',
                        f'.button:contains("{text}")'
                    ]

                    for selector in selectors:
                        try:
                            button = self.tab.ele(selector, timeout=3)
                            if button:
                                self.logger.info(f"找到注册按钮: {text} - {selector}")

                                # 模拟真实用户行为：先观察按钮
                                self.simulate_mouse_movement(button)
                                self.human_like_delay(0.5, 1.0)

                                # 人类行为点击
                                if self.human_like_click(button):
                                    button_found = True
                                    break
                        except Exception as e:
                            self.logger.debug(f"选择器 '{selector}' 未找到: {e}")
                            continue

                    if button_found:
                        break

                except Exception as e:
                    self.logger.debug(f"按钮文本 '{text}' 未找到: {e}")
                    continue

            if not button_found:
                raise Exception("未找到注册按钮")

            # 等待页面跳转
            self.human_like_delay(2.0, 4.0)

            # 保存页面内容
            self.save_page_content("step2_after_button_click", {
                "action": "点击注册按钮后",
                "button_text": text if button_found else "未找到"
            })

            self.logger.info("步骤2完成: 注册按钮点击成功")
            return True

        except Exception as e:
            self.logger.error(f"步骤2失败: {e}")
            self.save_page_content("step2_error", {"error": str(e)})
            return False

    def step3_handle_turnstile(self):
        """步骤3: 处理Turnstile验证"""
        try:
            self.logger.info("=== 步骤3: 处理Turnstile验证 ===")

            # 等待Turnstile加载
            self.human_like_delay(2.0, 3.0)

            # 保存当前页面状态
            self.save_page_content("step3_before_turnstile", {
                "action": "Turnstile验证前"
            })

            # 查找Turnstile复选框
            turnstile_selectors = [
                'iframe[src*="turnstile"]',
                '[data-sitekey]',
                '.cf-turnstile',
                '#cf-turnstile'
            ]

            turnstile_found = False
            for selector in turnstile_selectors:
                try:
                    turnstile = self.tab.ele(selector, timeout=3)
                    if turnstile:
                        self.logger.info(f"找到Turnstile: {selector}")

                        # 模拟人类行为：观察验证码
                        self.simulate_mouse_movement(turnstile)
                        self.human_like_delay(1.0, 2.0)

                        # 点击Turnstile
                        if self.human_like_click(turnstile):
                            turnstile_found = True
                            break

                except Exception as e:
                    self.logger.debug(f"Turnstile选择器 '{selector}' 未找到: {e}")
                    continue

            if not turnstile_found:
                self.logger.warning("未找到Turnstile，可能已自动通过或不存在")

            # 等待验证完成
            self.human_like_delay(3.0, 5.0)

            # 保存验证后页面状态
            self.save_page_content("step3_after_turnstile", {
                "action": "Turnstile验证后",
                "turnstile_found": turnstile_found
            })

            self.logger.info("步骤3完成: Turnstile处理完成")
            return True

        except Exception as e:
            self.logger.error(f"步骤3失败: {e}")
            self.save_page_content("step3_error", {"error": str(e)})
            return False

    def step4_fill_email(self):
        """步骤4: 填写邮箱"""
        try:
            self.logger.info("=== 步骤4: 填写邮箱 ===")

            # 生成邮箱
            self.email = generate_email_with_timestamp()
            self.logger.info(f"生成邮箱: {self.email}")

            # 初始化AugmentCodeRegister
            self.augment_register = AugmentCodeRegister(self.email)

            # 保存填写前页面状态
            self.save_page_content("step4_before_email", {
                "action": "填写邮箱前",
                "email": self.email
            })

            # 查找邮箱输入框
            email_input = None
            email_selectors = ['#username', 'input[type="email"]', 'input[name="email"]', 'input[placeholder*="email"]']

            for selector in email_selectors:
                try:
                    email_input = self.tab.ele(selector, timeout=3)
                    if email_input:
                        self.logger.info(f"找到邮箱输入框: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"邮箱选择器 '{selector}' 未找到: {e}")
                    continue

            if not email_input:
                raise Exception("未找到邮箱输入框")

            # 模拟人类行为填写邮箱
            if not self.human_like_typing(email_input, self.email):
                raise Exception("邮箱输入失败")

            # 保存填写后页面状态
            self.save_page_content("step4_after_email", {
                "action": "填写邮箱后",
                "email": self.email
            })

            self.logger.info("步骤4完成: 邮箱填写成功")
            return True

        except Exception as e:
            self.logger.error(f"步骤4失败: {e}")
            self.save_page_content("step4_error", {"error": str(e), "email": self.email})
            return False

    def step5_agree_terms_and_submit(self):
        """步骤5: 同意条款并提交"""
        try:
            self.logger.info("=== 步骤5: 同意条款并提交 ===")

            # 保存提交前页面状态
            self.save_page_content("step5_before_submit", {
                "action": "提交前"
            })

            # 查找并处理条款复选框
            checkbox_selectors = [
                'input[type="checkbox"]',
                '.checkbox input',
                '[role="checkbox"]'
            ]

            checkbox_found = False
            for selector in checkbox_selectors:
                try:
                    checkbox = self.tab.ele(selector, timeout=3)
                    if checkbox:
                        self.logger.info(f"找到条款复选框: {selector}")

                        # 模拟人类行为：先阅读条款
                        self.human_like_delay(2.0, 4.0)

                        # 勾选复选框
                        if not checkbox.states.is_checked:
                            self.human_like_click(checkbox)
                            checkbox_found = True
                        break

                except Exception as e:
                    self.logger.debug(f"复选框选择器 '{selector}' 未找到: {e}")
                    continue

            # 如果没找到复选框，尝试JavaScript强制启用提交按钮
            if not checkbox_found:
                self.logger.warning("未找到条款复选框，尝试JavaScript启用提交按钮")
                enable_js = """
                // 查找所有复选框并勾选
                const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(cb => {
                    if (!cb.checked) {
                        cb.checked = true;
                        cb.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });

                // 查找并启用提交按钮
                const buttons = document.querySelectorAll('button, input[type="submit"]');
                buttons.forEach(btn => {
                    btn.disabled = false;
                    btn.style.opacity = '1';
                    btn.style.pointerEvents = 'auto';
                });

                console.log('条款复选框和提交按钮已强制启用');
                """
                self.tab.run_js(enable_js)
                self.human_like_delay(1.0, 2.0)

            # 查找提交按钮
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Submit")',
                'button:contains("Continue")',
                'button:contains("Next")',
                '.submit-btn',
                '.btn-submit'
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = self.tab.ele(selector, timeout=3)
                    if submit_button:
                        self.logger.info(f"找到提交按钮: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"提交按钮选择器 '{selector}' 未找到: {e}")
                    continue

            if not submit_button:
                raise Exception("未找到提交按钮")

            # 模拟人类行为：提交前的最后确认
            self.human_like_delay(1.0, 2.0)

            # 点击提交按钮
            if not self.human_like_click(submit_button):
                raise Exception("提交按钮点击失败")

            # 等待页面跳转
            self.human_like_delay(3.0, 5.0)

            # 保存提交后页面状态
            self.save_page_content("step5_after_submit", {
                "action": "提交后",
                "checkbox_found": checkbox_found
            })

            self.logger.info("步骤5完成: 条款同意并提交成功")
            return True

        except Exception as e:
            self.logger.error(f"步骤5失败: {e}")
            self.save_page_content("step5_error", {"error": str(e)})
            return False

    def step6_get_verification_code(self):
        """步骤6: 获取验证码"""
        try:
            self.logger.info("=== 步骤6: 获取验证码 ===")

            # 保存获取验证码前页面状态
            self.save_page_content("step6_before_code", {
                "action": "获取验证码前",
                "email": self.email
            })

            # 模拟用户等待邮件的行为
            self.logger.info("等待邮件发送...")
            self.human_like_delay(5.0, 8.0)

            # 获取验证码
            self.verification_code = self.augment_register.get_verification_code(self.email)

            if not self.verification_code:
                raise Exception("获取验证码失败")

            self.logger.info(f"获取到验证码: {self.verification_code}")

            # 保存获取验证码后状态
            self.save_page_content("step6_after_code", {
                "action": "获取验证码后",
                "email": self.email,
                "verification_code": self.verification_code
            })

            self.logger.info("步骤6完成: 验证码获取成功")
            return True

        except Exception as e:
            self.logger.error(f"步骤6失败: {e}")
            self.save_page_content("step6_error", {
                "error": str(e),
                "email": self.email
            })
            return False

    def step7_submit_verification_code(self):
        """步骤7: 提交验证码"""
        try:
            self.logger.info("=== 步骤7: 提交验证码 ===")

            # 保存提交验证码前页面状态
            self.save_page_content("step7_before_code_submit", {
                "action": "提交验证码前",
                "verification_code": self.verification_code
            })

            # 查找验证码输入框
            code_input = None
            code_selectors = ['#code', 'input[name="code"]', 'input[placeholder*="code"]', 'input[type="text"]']

            for selector in code_selectors:
                try:
                    code_input = self.tab.ele(selector, timeout=3)
                    if code_input:
                        self.logger.info(f"找到验证码输入框: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"验证码选择器 '{selector}' 未找到: {e}")
                    continue

            if not code_input:
                raise Exception("未找到验证码输入框")

            # 模拟人类行为输入验证码
            if not self.human_like_typing(code_input, self.verification_code):
                raise Exception("验证码输入失败")

            # 查找提交按钮
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Verify")',
                'button:contains("Submit")',
                'button:contains("Continue")',
                '.verify-btn',
                '.submit-btn'
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = self.tab.ele(selector, timeout=3)
                    if submit_button:
                        self.logger.info(f"找到验证提交按钮: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"验证提交按钮选择器 '{selector}' 未找到: {e}")
                    continue

            if not submit_button:
                raise Exception("未找到验证提交按钮")

            # 提交前短暂停顿
            self.human_like_delay(1.0, 2.0)

            # 点击提交按钮
            if not self.human_like_click(submit_button):
                raise Exception("验证提交按钮点击失败")

            # 等待验证完成
            self.human_like_delay(3.0, 5.0)

            # 保存最终页面状态
            self.save_page_content("step7_final_result", {
                "action": "验证码提交后最终结果",
                "verification_code": self.verification_code,
                "email": self.email
            })

            # 检查是否出现地区限制信息
            page_text = self.tab.get_text().lower()
            if any(keyword in page_text for keyword in [
                "limiting signups", "certain regions", "increased demand",
                "地区限制", "region", "限制注册"
            ]):
                self.logger.warning("⚠️  检测到地区限制信息")
                return "REGION_RESTRICTED"

            self.logger.info("步骤7完成: 验证码提交成功")
            return True

        except Exception as e:
            self.logger.error(f"步骤7失败: {e}")
            self.save_page_content("step7_error", {
                "error": str(e),
                "verification_code": self.verification_code
            })
            return False

    def run_full_registration(self):
        """运行完整注册流程"""
        try:
            self.logger.info("🚀 开始行为模式深度模拟注册流程")

            # 执行所有步骤
            steps = [
                ("访问推广页面", self.step1_visit_promotion_page),
                ("点击注册按钮", self.step2_click_registration_button),
                ("处理Turnstile验证", self.step3_handle_turnstile),
                ("填写邮箱", self.step4_fill_email),
                ("同意条款并提交", self.step5_agree_terms_and_submit),
                ("获取验证码", self.step6_get_verification_code),
                ("提交验证码", self.step7_submit_verification_code)
            ]

            for step_name, step_func in steps:
                self.logger.info(f"执行步骤: {step_name}")
                result = step_func()

                if result == "REGION_RESTRICTED":
                    self.logger.error("❌ 注册失败: 检测到地区限制")
                    return False
                elif not result:
                    self.logger.error(f"❌ 步骤失败: {step_name}")
                    return False

                # 步骤间的自然停顿
                self.human_like_delay(1.0, 2.0)

            self.logger.info("✅ 行为模式深度模拟注册流程完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 注册流程异常: {e}")
            return False

        finally:
            # 清理资源
            if self.browser:
                try:
                    self.browser.quit()
                except:
                    pass

def main():
    """主函数"""
    print("=== 行为模式深度模拟注册测试 ===")

    register = BehaviorSimulationRegister()
    success = register.run_full_registration()

    if success:
        print("✅ 注册成功完成")
        print(f"📧 使用邮箱: {register.email}")
        print(f"🔑 验证码: {register.verification_code}")
    else:
        print("❌ 注册失败")

    print(f"📁 日志目录: {register.log_dir}")
    print(f"📄 页面内容: {register.content_dir}")

if __name__ == "__main__":
    main()
