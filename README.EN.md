# Cursor Pro Automation Tool User Guide

README also available in: [中文](./README.md)

## Online Documentation
[cursor-auto-free-doc.vercel.app](https://cursor-auto-free-doc.vercel.app)

## Note
Recently, some users have sold this software on platforms like Xianyu. Please avoid such practices—there's no need to earn money this way.

## Sponsor for More Updates
![image](./screen/afdian-[未认证]阿臻.jpg)

## License
This project is licensed under [CC BY-NC-ND 4.0](https://creativecommons.org/licenses/by-nc-nd/4.0/).  
This means you may:  
- **Share** — Copy and redistribute the material in any medium or format.  
But you must comply with the following conditions:
- **Non-commercial** — You may not use the material for commercial purposes.

## Features
Automated account registration and token refreshing to free your hands.

## Important Notes
1. **Ensure you have Chrome installed. If not, [download here](https://www.google.com/intl/en_pk/chrome/).**  
2. **You must log into your account, regardless of its validity. Logged-in is mandatory.**  
3. **A stable internet connection is required, preferably via an overseas node. Do not enable global proxy.**

## Configuration Instructions
Please refer to our [online documentation](https://cursor-auto-free-doc.vercel.app) for detailed configuration instructions.

## Download
[https://github.com/chengazhen/cursor-auto-free/releases](https://github.com/chengazhen/cursor-auto-free/releases)

## Update Log
- **2025-01-09**: Added logs and auto-build feature.  
- **2025-01-10**: Switched to Cloudflare domain email.  
- **2025-01-11**: Added headless mode and proxy configuration through .env file.
- **2025-01-20**: Added IMAP to replace tempmail.plus.

## Special Thanks
This project has received support and help from many open source projects and community members. We would like to express our special gratitude to:

### Open Source Projects
- [go-cursor-help](https://github.com/yuaotian/go-cursor-help) - An excellent Cursor machine code reset tool with 9.1k Stars. Our machine code reset functionality is implemented using this project, which is one of the most popular Cursor auxiliary tools.

Inspired by [gpt-cursor-auto](https://github.com/hmhm2022/gpt-cursor-auto); optimized verification and email auto-registration logic; solved the issue of not being able to receive email verification codes.
