#!/usr/bin/env python3
"""
测试最终条款同意页面处理
模拟验证码后的条款同意流程
"""

import time
from logger import logging

def test_final_terms():
    """测试最终条款同意处理"""
    try:
        logging.info("🧪 测试最终条款同意处理...")
        
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        logging.info("✅ 浏览器初始化成功")
        
        # 直接访问条款同意页面（模拟验证码后的状态）
        terms_url = "https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=test&code_challenge=test&code_challenge_method=S256"
        
        logging.info("🌐 访问条款同意页面...")
        tab.get(terms_url)
        time.sleep(5)
        
        current_url = tab.url
        logging.info(f"🔍 当前 URL: {current_url}")
        
        # 检查页面标题
        page_title = tab.title
        logging.info(f"📄 页面标题: {page_title}")
        
        # 截图记录初始状态
        tab.get_screenshot(path=f"terms_page_initial_{int(time.time())}.png")
        
        # 查找条款复选框
        terms_checkbox = tab.ele("#terms-of-service-checkbox", timeout=5)
        if terms_checkbox:
            logging.info("🔍 找到条款同意复选框")
            
            # 检查复选框状态
            is_checked = terms_checkbox.attr("checked")
            logging.info(f"📋 复选框状态: {'已勾选' if is_checked else '未勾选'}")
            
            # 如果未勾选，则勾选
            if not is_checked:
                logging.info("☑️ 勾选条款同意复选框...")
                terms_checkbox.click()
                time.sleep(1)
                
                # 验证勾选结果
                is_checked_after = terms_checkbox.attr("checked")
                logging.info(f"✅ 勾选后状态: {'已勾选' if is_checked_after else '未勾选'}")
                
                if not is_checked_after:
                    logging.warning("⚠️ 条款复选框勾选失败")
                    return False
            
            # 查找注册按钮
            signup_button = tab.ele("#signup-button", timeout=3)
            if signup_button:
                # 检查按钮状态
                is_disabled = signup_button.attr("disabled")
                button_text = signup_button.text
                
                logging.info(f"🔍 注册按钮:")
                logging.info(f"   - 文本: '{button_text}'")
                logging.info(f"   - 禁用: {is_disabled}")
                
                # 等待按钮启用
                if is_disabled:
                    logging.info("⏳ 等待注册按钮启用...")
                    
                    for i in range(10):
                        time.sleep(1)
                        is_disabled = signup_button.attr("disabled")
                        if not is_disabled:
                            logging.info(f"✅ 注册按钮在 {i+1} 秒后启用")
                            break
                        logging.debug(f"等待按钮启用... ({i+1}/10)")
                    
                    if is_disabled:
                        logging.error("❌ 注册按钮仍然禁用")
                        return False
                
                # 点击注册按钮
                logging.info("🖱️ 点击注册按钮...")
                
                # 截图记录点击前状态
                tab.get_screenshot(path=f"before_final_click_{int(time.time())}.png")
                
                signup_button.click()
                time.sleep(3)
                
                # 截图记录点击后状态
                tab.get_screenshot(path=f"after_final_click_{int(time.time())}.png")
                
                logging.info("✅ 注册按钮点击成功")
                
                # 等待页面跳转
                logging.info("⏳ 等待页面跳转...")
                time.sleep(5)
                
                # 检查跳转结果
                final_url = tab.url
                final_title = tab.title
                
                logging.info(f"🔍 最终 URL: {final_url}")
                logging.info(f"📄 最终标题: {final_title}")
                
                # 截图记录最终状态
                tab.get_screenshot(path=f"final_state_{int(time.time())}.png")
                
                # 检查是否跳转成功
                if "terms-accept" not in final_url:
                    logging.info("🎉 成功跳转离开条款同意页面！")
                    
                    # 检查是否到达成功页面
                    success_indicators = [
                        "welcome", "success", "congratulations", 
                        "account created", "registration complete", 
                        "dashboard", "app.augmentcode.com"
                    ]
                    
                    page_content = tab.html.lower()
                    found_indicators = [indicator for indicator in success_indicators if indicator in page_content]
                    
                    if found_indicators:
                        logging.info(f"🎉 检测到成功标志: {found_indicators}")
                        return True
                    else:
                        logging.info("ℹ️ 未检测到明确的成功标志，但已离开条款页面")
                        return True
                else:
                    logging.warning("⚠️ 仍在条款同意页面")
                    
                    # 检查是否有错误信息
                    error_elements = tab.eles(".error, .alert, .warning")
                    if error_elements:
                        for error in error_elements:
                            error_text = error.text
                            if error_text:
                                logging.warning(f"⚠️ 页面错误信息: {error_text}")
                    
                    return False
            else:
                logging.error("❌ 未找到注册按钮")
                return False
        else:
            logging.error("❌ 未找到条款同意复选框")
            return False
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        try:
            input("按 Enter 键关闭浏览器...")
            browser_manager.quit()
        except:
            pass

def main():
    """主函数"""
    logging.info("=" * 80)
    logging.info("最终条款同意处理测试")
    logging.info("=" * 80)
    
    success = test_final_terms()
    
    if success:
        logging.info("🎉 最终条款同意处理测试成功！")
    else:
        logging.error("❌ 最终条款同意处理测试失败！")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
