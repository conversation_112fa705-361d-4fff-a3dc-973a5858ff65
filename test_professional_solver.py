#!/usr/bin/env python3
"""
测试专业的 Turnstile Solver 解决方案
"""

import os
import sys
import time
from logger import logging
from turnstile_solver_integration import TurnstileSolverIntegration

def test_solver_installation():
    """测试 Solver 安装"""
    logging.info("🔧 测试 Turnstile Solver 安装...")
    
    solver = TurnstileSolverIntegration()
    
    # 1. 安装依赖
    if not solver.install_turnstile_solver():
        logging.error("❌ 安装失败")
        return False
    
    # 2. 下载代码
    if not solver.download_turnstile_solver():
        logging.error("❌ 下载失败")
        return False
    
    logging.info("✅ Turnstile Solver 安装测试成功")
    return True

def test_solver_api():
    """测试 Solver API 服务"""
    logging.info("🚀 测试 Turnstile Solver API 服务...")
    
    solver = TurnstileSolverIntegration()
    
    try:
        # 启动 API 服务
        if not solver.start_solver_api():
            logging.error("❌ API 服务启动失败")
            return False
        
        # 等待服务完全启动
        logging.info("⏳ 等待 API 服务完全启动...")
        time.sleep(15)
        
        # 检查服务状态
        if solver.check_solver_api():
            logging.info("✅ Turnstile Solver API 服务运行正常")
            return True
        else:
            logging.error("❌ API 服务检查失败")
            return False
            
    finally:
        solver.stop_solver_api()

def test_solver_with_augmentcode():
    """测试 Solver 解决 AugmentCode Turnstile"""
    logging.info("🎯 测试 Solver 解决 AugmentCode Turnstile...")
    
    solver = TurnstileSolverIntegration()
    
    try:
        # 启动 API 服务
        if not solver.start_solver_api():
            logging.error("❌ API 服务启动失败")
            return False
        
        # 等待服务启动
        logging.info("⏳ 等待 API 服务启动...")
        time.sleep(15)
        
        # 测试解决 AugmentCode 的 Turnstile
        test_url = "https://login.augmentcode.com/u/login/identifier"
        
        # 常见的 Turnstile sitekey 格式
        test_sitekeys = [
            "0x4AAAAAAAQFNSW6xordsuIq",  # 可能的 AugmentCode sitekey
            "0x4AAAAAAAQFNSWxordsuIq",   # 变体1
            "0x4AAAAAAAQFNSW6xordsuI",    # 变体2
            "0x4AAAAAAAQFNSW6xordsu",     # 变体3
        ]
        
        for sitekey in test_sitekeys:
            logging.info(f"🧪 测试 sitekey: {sitekey}")
            
            token = solver.solve_turnstile(
                url=test_url,
                sitekey=sitekey,
                action="login"
            )
            
            if token:
                logging.info(f"🎉 成功获取令牌: {token[:20]}...")
                return True
            else:
                logging.warning(f"⚠️ sitekey {sitekey} 验证失败")
        
        logging.error("❌ 所有 sitekey 测试都失败了")
        return False
        
    finally:
        solver.stop_solver_api()

def test_alternative_browsers():
    """测试不同浏览器的 Solver"""
    logging.info("🌐 测试不同浏览器的 Turnstile Solver...")
    
    browsers = ["camoufox", "chromium"]
    
    for browser in browsers:
        logging.info(f"🧪 测试浏览器: {browser}")
        
        solver = TurnstileSolverIntegration()
        
        try:
            # 修改启动参数以使用不同浏览器
            solver_dir = "Turnstile-Solver"
            
            if not os.path.exists(solver_dir):
                logging.warning(f"⚠️ {solver_dir} 不存在，跳过浏览器测试")
                continue
            
            import subprocess
            
            # 启动特定浏览器的 API 服务
            api_script = os.path.join(solver_dir, "api_solver.py")
            
            solver.solver_process = subprocess.Popen([
                sys.executable, api_script,
                "--browser_type", browser,
                "--host", "127.0.0.1",
                "--port", "5000",
                "--debug", "True"
            ], cwd=solver_dir)
            
            # 等待服务启动
            time.sleep(15)
            
            # 检查服务
            if solver.check_solver_api():
                logging.info(f"✅ {browser} 浏览器 API 服务正常")
                
                # 简单测试
                token = solver.solve_turnstile(
                    url="https://login.augmentcode.com/u/login/identifier",
                    sitekey="0x4AAAAAAAQFNSW6xordsuIq"
                )
                
                if token:
                    logging.info(f"🎉 {browser} 浏览器验证成功: {token[:20]}...")
                else:
                    logging.warning(f"⚠️ {browser} 浏览器验证失败")
            else:
                logging.error(f"❌ {browser} 浏览器 API 服务启动失败")
                
        except Exception as e:
            logging.error(f"❌ 测试 {browser} 浏览器时出错: {e}")
        finally:
            solver.stop_solver_api()

def main():
    """主测试函数"""
    logging.info("=" * 80)
    logging.info("专业 Turnstile Solver 测试套件")
    logging.info("=" * 80)
    
    tests = [
        ("安装测试", test_solver_installation),
        ("API 服务测试", test_solver_api),
        ("AugmentCode 验证测试", test_solver_with_augmentcode),
        ("多浏览器测试", test_alternative_browsers),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logging.info(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logging.info(f"✅ {test_name} 通过")
            else:
                logging.error(f"❌ {test_name} 失败")
                
        except Exception as e:
            logging.error(f"❌ {test_name} 异常: {e}")
            results[test_name] = False
    
    # 输出总结
    logging.info("\n" + "="*80)
    logging.info("测试结果总结")
    logging.info("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logging.info(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        logging.info("🎉 所有测试都通过了！专业 Turnstile Solver 可以使用")
        return True
    else:
        logging.warning(f"⚠️ 有 {total - passed} 个测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 专业 Turnstile Solver 测试成功！")
        print("✅ 可以使用 python augmentcode_register_with_solver.py 进行注册")
    else:
        print("\n😞 专业 Turnstile Solver 测试失败！")
        print("❌ 需要检查安装和配置")
    
    sys.exit(0 if success else 1)
