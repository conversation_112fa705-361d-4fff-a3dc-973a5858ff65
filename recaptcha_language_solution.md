# reCAPTCHA 语言设置解决方案

## 🎯 问题分析

根据用户发现，reCAPTCHA 的语言参数 `hl` 会影响验证通过率：
- `hl=zh`（中文）：验证较难通过
- `hl=ja`（日语）：验证更容易通过

用户提供的 reCAPTCHA URL 示例：
```
https://www.google.com/recaptcha/enterprise/anchor?ar=1&k=6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1&co=aHR0cHM6Ly9hdXRoLmF1Z21lbnRjb2RlLmNvbTo0NDM.&hl=ja&v=ngcIAHyEnHQZZIKkyKneDTW3&size=invisible&anchor-ms=20000&execute-ms=15000&cb=1wdfzftwu7pq
```

## 🔧 解决方案

### 方案1：浏览器级别语言设置（推荐）

**修改文件：** `browser_utils.py`

```python
# 在 _get_browser_options 方法中添加：
co.set_argument("--lang=ja")
co.set_argument("--accept-lang=ja-JP,ja;q=0.9,en;q=0.8")
co.set_pref("intl.accept_languages", "ja-JP,ja,en")
```

**优点：**
- 从浏览器启动就设置日语环境
- 影响所有网络请求的 Accept-Language 头
- 最彻底的解决方案

### 方案2：JavaScript 动态语言设置

**修改文件：** `cursor_style_register.py`

在 Turnstile 处理前调用 `_setup_japanese_language()` 方法：

```python
def _setup_japanese_language(self):
    """设置日语语言环境以提高 reCAPTCHA 验证成功率"""
    # 修改 navigator 对象
    self.tab.run_js("""
        Object.defineProperty(navigator, 'language', {
            get: function() { return 'ja-JP'; }
        });
        Object.defineProperty(navigator, 'languages', {
            get: function() { return ['ja-JP', 'ja', 'en']; }
        });
    """)
    
    # 拦截并修改请求头
    # ... (详见代码实现)
```

**优点：**
- 运行时动态修改
- 可以针对特定页面应用
- 灵活性高

### 方案3：直接注入 reCAPTCHA Response（高级）

**新文件：** `recaptcha_response_injector.py`

如果用户能提供有效的 `g-recaptcha-response` 和 `verisoul-session-id`：

```python
injector = RecaptchaResponseInjector(tab)
success = injector.inject_response(
    g_recaptcha_response="有效的response值",
    verisoul_session_id="有效的session-id值"
)
```

**优点：**
- 绕过验证过程
- 成功率最高
- 需要有效的 response 值

## 🚀 使用方法

### 测试语言设置效果

```bash
python test_language_setup.py
```

这个脚本会：
1. 启动浏览器并应用日语设置
2. 访问注册页面
3. 检查 reCAPTCHA URL 中的 `hl` 参数
4. 验证是否变为 `hl=ja`

### 运行完整注册流程

```bash
# 设置代理（如需要）
set BROWSER_PROXY=127.0.0.1:1080

# 运行注册
python cursor_style_register.py
```

## 📋 验证方法

### 1. 检查浏览器语言
```javascript
console.log('Language:', navigator.language);
console.log('Languages:', navigator.languages);
```

### 2. 检查 reCAPTCHA URL
在浏览器开发者工具中查看网络请求，找到包含 `recaptcha` 的请求，检查 `hl` 参数。

### 3. 检查请求头
```javascript
// 在控制台中检查
fetch('https://example.com', {
    method: 'GET'
}).then(response => {
    console.log('Request headers sent');
});
```

## 🎯 预期效果

应用这些修改后：

1. **浏览器启动时**：
   - `navigator.language` = `"ja-JP"`
   - `navigator.languages` = `["ja-JP", "ja", "en"]`
   - HTTP 请求头 `Accept-Language` = `"ja-JP,ja;q=0.9,en;q=0.8"`

2. **reCAPTCHA 加载时**：
   - URL 参数变为 `hl=ja`
   - 验证界面显示日语
   - 验证通过率提高

3. **注册流程**：
   - Turnstile 验证更容易通过
   - 减少验证失败导致的流程中断
   - 整体成功率提升

## 🔍 故障排除

### 如果语言设置不生效：

1. **检查浏览器参数**：
   ```python
   # 在 browser_utils.py 中添加调试
   logging.info(f"浏览器参数: {co.arguments}")
   ```

2. **检查 JavaScript 执行**：
   ```python
   # 在设置后立即验证
   result = tab.run_js("return navigator.language;")
   logging.info(f"当前语言: {result}")
   ```

3. **手动验证**：
   - 打开浏览器开发者工具
   - 在控制台输入 `navigator.language`
   - 应该返回 `"ja-JP"`

### 如果 reCAPTCHA 仍然使用中文：

1. **清除浏览器缓存**
2. **重启浏览器**
3. **检查是否有其他语言设置覆盖**

## 📝 注意事项

1. **地理位置**：某些情况下可能还需要设置地理位置为日本
2. **时区设置**：可以考虑同时设置日本时区
3. **用户代理**：必要时可以使用日本地区的 User-Agent
4. **代理设置**：如果使用代理，确保代理服务器支持日本地区访问

## 🎉 总结

通过设置日语语言环境，我们可以显著提高 reCAPTCHA 验证的成功率。建议按以下优先级应用解决方案：

1. **首选**：浏览器级别语言设置（方案1）
2. **备用**：JavaScript 动态设置（方案2）  
3. **高级**：直接注入 response（方案3，需要有效值）

这些修改应该能解决 `hl=zh` 导致的验证失败问题，让注册流程更加稳定。
