#!/usr/bin/env python3
"""
测试反检测浏览器功能
"""

import time
from logger import logging
from config import Config
from augmentcode_register import AugmentCodeRegister


def test_anti_detection_browser():
    """测试反检测浏览器功能"""
    print("=" * 60)
    print("测试反检测浏览器功能")
    print("=" * 60)
    
    try:
        # 获取邮箱配置
        config = Config()
        
        if config.get_temp_mail() != "null":
            email = f"{config.get_temp_mail()}{config.get_temp_mail_ext()}"
        else:
            imap_config = config.get_imap()
            if imap_config:
                email = imap_config['imap_user']
            else:
                print("❌ 未配置有效的邮箱")
                return False
        
        print(f"✓ 使用邮箱: {email}")
        
        # 创建注册器
        register = AugmentCodeRegister(email)
        print("✓ 注册器创建成功")
        
        # 手动初始化反检测浏览器
        print("\n正在初始化反检测浏览器...")
        try:
            from anti_detection_browser import AntiDetectionBrowserManager
            register.browser_manager = AntiDetectionBrowserManager()
            browser = register.browser_manager.init_browser()
            register.tab = browser.latest_tab
            print("✓ 反检测浏览器初始化成功")
        except Exception as e:
            print(f"❌ 反检测浏览器初始化失败: {str(e)}")
            return False
        
        # 测试指纹检测
        print("\n测试浏览器指纹...")
        test_fingerprint_detection(register.tab)
        
        # 执行第一步：访问页面
        print("\n执行第一步：访问 AugmentCode 页面...")
        if not register.step1_click_get_free_month():
            print("❌ 第一步失败")
            if register.browser_manager:
                register.browser_manager.quit()
            return False
        
        print("✓ 第一步成功")
        
        # 执行第二步：处理 Turnstile 验证
        print("\n执行第二步：处理 Turnstile 验证...")
        if register.step2_handle_captcha():
            print("✓ Turnstile 验证成功！")
        else:
            print("❌ Turnstile 验证失败")
        
        # 执行第三步：输入邮箱
        print("\n执行第三步：输入邮箱...")
        if register.step3_input_email():
            print("✓ 邮箱输入成功！")
        else:
            print("❌ 邮箱输入失败")
        
        # 执行第四步：点击继续
        print("\n执行第四步：点击 Continue...")
        if register.step4_click_continue():
            print("✓ Continue 点击成功！")
        else:
            print("❌ Continue 点击失败")
        
        # 执行第五步：输入验证码
        print("\n执行第五步：输入验证码...")
        if register.step5_input_verification_code():
            print("✓ 验证码输入成功！")
        else:
            print("❌ 验证码输入失败")
        
        # 执行第六步：上传文件
        print("\n执行第六步：上传文件...")
        if register.step6_upload_file():
            print("✅ 文件上传成功！注册完成！")
            result = True
        else:
            print("❌ 文件上传失败")
            result = False
        
        # 等待一段时间观察结果
        print("\n等待 10 秒观察页面状态...")
        time.sleep(10)
        
        # 清理资源
        print("\n清理浏览器资源...")
        if register.browser_manager:
            register.browser_manager.quit()
        print("✓ 清理完成")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        logging.error(f"反检测测试出错: {str(e)}")
        return False


def test_fingerprint_detection(tab):
    """测试浏览器指纹检测"""
    print("检测浏览器指纹信息...")
    
    try:
        # 检测基本指纹信息
        fingerprint_js = """
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            languages: navigator.languages,
            platform: navigator.platform,
            hardwareConcurrency: navigator.hardwareConcurrency,
            deviceMemory: navigator.deviceMemory,
            screenWidth: screen.width,
            screenHeight: screen.height,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            webdriver: navigator.webdriver,
            automation: window.chrome && window.chrome.runtime && window.chrome.runtime.onConnect
        };
        """
        
        fingerprint = tab.run_js(fingerprint_js)
        
        print(f"  User Agent: {fingerprint.get('userAgent', 'N/A')[:80]}...")
        print(f"  Language: {fingerprint.get('language', 'N/A')}")
        print(f"  Languages: {fingerprint.get('languages', 'N/A')}")
        print(f"  Platform: {fingerprint.get('platform', 'N/A')}")
        print(f"  Hardware Concurrency: {fingerprint.get('hardwareConcurrency', 'N/A')}")
        print(f"  Device Memory: {fingerprint.get('deviceMemory', 'N/A')} GB")
        print(f"  Screen: {fingerprint.get('screenWidth', 'N/A')}x{fingerprint.get('screenHeight', 'N/A')}")
        print(f"  Timezone: {fingerprint.get('timezone', 'N/A')}")
        print(f"  WebDriver: {fingerprint.get('webdriver', 'N/A')}")
        print(f"  Automation: {fingerprint.get('automation', 'N/A')}")
        
        # 检查是否为美国指纹
        is_us_fingerprint = (
            fingerprint.get('language', '').startswith('en-US') and
            fingerprint.get('timezone', '').startswith('America/') and
            fingerprint.get('webdriver') is None
        )
        
        if is_us_fingerprint:
            print("✅ 美国指纹检测通过")
        else:
            print("⚠️  指纹可能被检测为非美国用户")
            
    except Exception as e:
        print(f"❌ 指纹检测失败: {str(e)}")


def main():
    """主函数"""
    print("AugmentCode 反检测浏览器测试")
    print("=" * 60)
    
    # 询问是否进行测试
    choice = input("是否进行反检测浏览器测试？(y/n): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        print("\n开始反检测测试...")
        success = test_anti_detection_browser()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 反检测测试成功！")
            print("浏览器指纹修改生效，可以突破检测")
        else:
            print("❌ 反检测测试失败")
            print("可能需要进一步优化指纹参数")
        print("=" * 60)
    else:
        print("\n跳过反检测测试。")
        print("如需测试，请重新运行此脚本")


if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试程序出错: {str(e)}")
        input("\n按回车键退出...")
