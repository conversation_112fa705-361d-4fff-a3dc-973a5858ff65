#!/usr/bin/env python3
"""
测试 Cursor 的浏览器配置是否能通过 Turnstile 验证
"""

import os
import sys
import time
from logger import logging

def test_cursor_browser_config():
    """测试 Cursor 的浏览器配置"""
    try:
        logging.info("🧪 测试 Cursor 浏览器配置...")
        
        # 1. 使用 Cursor 的 BrowserManager
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        logging.info("✅ Cursor BrowserManager 初始化成功")
        
        # 2. 检查浏览器配置
        user_agent = tab.run_js("return navigator.userAgent")
        logging.info(f"🔍 User-Agent: {user_agent}")
        
        webdriver_status = tab.run_js("return navigator.webdriver")
        logging.info(f"🔍 WebDriver 状态: {webdriver_status}")
        
        # 3. 访问 AugmentCode Cursor 资源页面测试 Turnstile
        logging.info("🌐 访问 AugmentCode Cursor 资源页面...")
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(5)
        
        # 截图记录
        screenshot_path = f"cursor_browser_test_{int(time.time())}.png"
        tab.get_screenshot(path=screenshot_path)
        logging.info(f"📸 截图已保存: {screenshot_path}")
        
        # 4. 检查 Turnstile 状态
        try:
            # 使用 Cursor 的方法检查 Turnstile
            auth0_captcha = tab.ele("#ulp-auth0-v2-captcha", timeout=5)
            
            if auth0_captcha:
                logging.info("🔍 检测到 Auth0 V2 Captcha")
                
                # 尝试访问 shadow DOM
                try:
                    challenge_check = (
                        auth0_captcha
                        .child()
                        .shadow_root.ele("tag:iframe")
                        .ele("tag:body")
                        .sr("tag:input")
                    )
                    
                    if challenge_check:
                        logging.info("✅ 成功访问 Turnstile shadow DOM")
                        
                        # 检查验证状态
                        challenge_value = challenge_check.attr("value")
                        logging.info(f"🔍 Challenge 值: {challenge_value}")
                        
                        if challenge_value:
                            logging.info("🎉 Turnstile 验证可能已自动完成！")
                            return True
                        else:
                            logging.info("⏳ Turnstile 验证待完成")
                            
                            # 等待自动完成
                            for i in range(30):  # 等待30秒
                                time.sleep(1)
                                try:
                                    new_value = challenge_check.attr("value")
                                    if new_value and new_value != challenge_value:
                                        logging.info(f"🎉 Turnstile 验证自动完成！新值: {new_value}")
                                        return True
                                except:
                                    pass
                                
                                if i % 5 == 0:
                                    logging.info(f"⏳ 等待验证完成... ({i+1}/30)")
                            
                            logging.warning("⚠️ Turnstile 验证未在30秒内完成")
                            return False
                    else:
                        logging.info("ℹ️ 未找到 challenge input，可能已完成验证")
                        return True
                        
                except Exception as e:
                    logging.info(f"ℹ️ Shadow DOM 访问异常: {e}")
                    logging.info("这可能意味着验证已自动完成")
                    return True
            else:
                logging.info("ℹ️ 未检测到 Turnstile 验证")
                return True
                
        except Exception as e:
            logging.error(f"❌ Turnstile 检查失败: {e}")
            return False
        
    except Exception as e:
        logging.error(f"❌ Cursor 浏览器配置测试失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        try:
            browser_manager.quit()
        except:
            pass

def test_turnstile_on_different_sites():
    """在不同网站测试 Turnstile"""
    test_sites = [
        {
            "name": "AugmentCode Cursor 资源",
            "url": "https://www.augmentcode.com/resources/cursor",
            "selector": "#ulp-auth0-v2-captcha"
        },
        {
            "name": "AugmentCode 登录",
            "url": "https://login.augmentcode.com/u/login/identifier",
            "selector": "#ulp-auth0-v2-captcha"
        },
        {
            "name": "Cloudflare Demo",
            "url": "https://demo.turnstile.workers.dev/",
            "selector": ".cf-turnstile"
        }
    ]
    
    try:
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        results = {}
        
        for site in test_sites:
            logging.info(f"🧪 测试网站: {site['name']}")
            
            try:
                tab.get(site['url'])
                time.sleep(5)
                
                # 截图
                screenshot_path = f"test_{site['name'].replace(' ', '_')}_{int(time.time())}.png"
                tab.get_screenshot(path=screenshot_path)
                
                # 检查 Turnstile
                turnstile_element = tab.ele(site['selector'], timeout=5)
                
                if turnstile_element:
                    logging.info(f"✅ {site['name']} 检测到 Turnstile")
                    results[site['name']] = "检测到 Turnstile"
                else:
                    logging.info(f"ℹ️ {site['name']} 未检测到 Turnstile")
                    results[site['name']] = "未检测到 Turnstile"
                    
            except Exception as e:
                logging.error(f"❌ {site['name']} 测试失败: {e}")
                results[site['name']] = f"测试失败: {e}"
        
        # 输出结果
        logging.info("\n" + "="*50)
        logging.info("测试结果汇总:")
        for site_name, result in results.items():
            logging.info(f"{site_name}: {result}")
        
        return results
        
    except Exception as e:
        logging.error(f"❌ 多站点测试失败: {e}")
        return {}
    finally:
        try:
            browser_manager.quit()
        except:
            pass

def main():
    """主测试函数"""
    logging.info("=" * 80)
    logging.info("Cursor 浏览器配置测试")
    logging.info("=" * 80)
    
    # 检查环境变量
    proxy = os.getenv("BROWSER_PROXY", "")
    if proxy:
        logging.info(f"🌐 代理配置: {proxy}")
    else:
        logging.warning("⚠️ 未配置代理，建议设置 BROWSER_PROXY=127.0.0.1:1080")
    
    browser_path = os.getenv("BROWSER_PATH", "")
    if browser_path:
        logging.info(f"🌐 浏览器路径: {browser_path}")
    else:
        logging.info("ℹ️ 使用默认浏览器路径")
    
    headless = os.getenv("BROWSER_HEADLESS", "True")
    logging.info(f"🌐 无头模式: {headless}")
    
    # 测试1: Cursor 浏览器配置
    logging.info("\n" + "="*50)
    logging.info("测试1: Cursor 浏览器配置")
    logging.info("="*50)
    
    cursor_test_result = test_cursor_browser_config()
    
    if cursor_test_result:
        logging.info("✅ Cursor 浏览器配置测试通过")
    else:
        logging.error("❌ Cursor 浏览器配置测试失败")
    
    # 测试2: 多站点 Turnstile 测试
    logging.info("\n" + "="*50)
    logging.info("测试2: 多站点 Turnstile 测试")
    logging.info("="*50)
    
    multi_site_results = test_turnstile_on_different_sites()
    
    # 总结
    logging.info("\n" + "="*80)
    logging.info("测试总结")
    logging.info("="*80)
    
    if cursor_test_result:
        logging.info("🎉 Cursor 浏览器配置可以用于 AugmentCode 注册！")
        print("\n✅ 测试成功！可以使用以下命令进行注册：")
        print("python real_browser_register.py")
    else:
        logging.warning("⚠️ Cursor 浏览器配置可能需要调整")
        print("\n❌ 测试失败！需要检查浏览器配置")
        
        # 提供建议
        print("\n💡 建议检查：")
        print("1. 确保设置了代理: set BROWSER_PROXY=127.0.0.1:1080")
        print("2. 确保 turnstilePatch 扩展存在")
        print("3. 尝试关闭无头模式: set BROWSER_HEADLESS=False")
    
    return cursor_test_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
