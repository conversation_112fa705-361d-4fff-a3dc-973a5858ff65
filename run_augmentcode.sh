#!/bin/bash

# AugmentCode 自动注册工具启动脚本

echo ""
echo "========================================"
echo "    AugmentCode 自动注册工具"
echo "========================================"
echo ""

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到 Python，请先安装 Python 3.7+"
        echo "Ubuntu/Debian: sudo apt install python3"
        echo "CentOS/RHEL: sudo yum install python3"
        echo "macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# 显示 Python 版本
echo "使用 Python: $($PYTHON_CMD --version)"

# 检查是否在正确的目录
if [ ! -f "augmentcode_register.py" ]; then
    echo "错误: 未找到 augmentcode_register.py 文件"
    echo "请确保在正确的项目目录中运行此脚本"
    exit 1
fi

# 检查 .env 文件
if [ ! -f ".env" ]; then
    echo "错误: 未找到 .env 配置文件"
    echo "请先配置 .env 文件"
    exit 1
fi

# 检查 WIPDF.pdf 文件
if [ ! -f "WIPDF.pdf" ]; then
    echo "警告: 未找到 WIPDF.pdf 文件"
    echo "请确保项目根目录下有此文件"
    read -p "按回车键继续..."
fi

# 检查依赖
echo "检查 Python 依赖..."
if [ -f "requirements.txt" ]; then
    $PYTHON_CMD -c "
import pkg_resources
import sys

try:
    with open('requirements.txt', 'r') as f:
        requirements = f.read().splitlines()
    
    for requirement in requirements:
        if requirement.strip() and not requirement.startswith('#'):
            try:
                pkg_resources.require(requirement.strip())
            except pkg_resources.DistributionNotFound:
                print(f'缺少依赖: {requirement}')
                print('请运行: pip install -r requirements.txt')
                sys.exit(1)
            except pkg_resources.VersionConflict:
                print(f'版本冲突: {requirement}')
                print('请运行: pip install -r requirements.txt --upgrade')
                sys.exit(1)
    
    print('✓ 所有依赖已安装')
except Exception as e:
    print(f'检查依赖时出错: {e}')
    sys.exit(1)
"
    
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

# 设置执行权限（如果需要）
chmod +x "$0" 2>/dev/null

# 运行注册程序
echo ""
echo "启动 AugmentCode 注册工具..."
echo ""
$PYTHON_CMD run_augmentcode_register.py

# 等待用户确认
echo ""
read -p "按回车键退出..."
