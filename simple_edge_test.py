#!/usr/bin/env python3
"""
简化的 Edge 浏览器测试
验证无痕模式和基本功能
"""

import time
import os
from logger import logging

def test_edge_basic():
    """测试 Edge 浏览器基本功能"""
    
    logging.info("=" * 80)
    logging.info("🌐 Edge 浏览器基本功能测试")
    logging.info("=" * 80)
    
    try:
        from browser_utils import BrowserManager
        
        # 初始化 Edge 浏览器
        logging.info("🚀 初始化 Edge 浏览器...")
        browser_manager = BrowserManager(browser_type="edge")
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        # 检查浏览器信息
        logging.info("📋 检查浏览器信息...")
        browser_info = tab.run_js("""
            return {
                userAgent: navigator.userAgent,
                language: navigator.language,
                languages: navigator.languages,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                platform: navigator.platform
            };
        """)
        
        logging.info(f"🌐 User-Agent: {browser_info.get('userAgent', 'N/A')}")
        logging.info(f"🌐 语言: {browser_info.get('language', 'N/A')}")
        logging.info(f"🌐 语言列表: {browser_info.get('languages', 'N/A')}")
        logging.info(f"🌐 平台: {browser_info.get('platform', 'N/A')}")
        
        # 检查是否是 Edge 浏览器
        user_agent = browser_info.get('userAgent', '')
        if 'Edg/' in user_agent:
            logging.info("✅ 确认是 Microsoft Edge 浏览器")
        else:
            logging.warning(f"⚠️ 可能不是 Edge 浏览器: {user_agent}")
        
        # 检查无痕模式特征
        logging.info("🔒 检查无痕模式特征...")
        incognito_features = tab.run_js("""
            return {
                localStorage: typeof localStorage !== 'undefined',
                sessionStorage: typeof sessionStorage !== 'undefined',
                indexedDB: typeof indexedDB !== 'undefined',
                webkitTemporaryStorage: typeof webkitTemporaryStorage !== 'undefined'
            };
        """)
        
        for feature, available in incognito_features.items():
            status = "可用" if available else "不可用"
            logging.info(f"   {feature}: {status}")
        
        # 访问测试页面
        logging.info("🌐 访问测试页面...")
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(3)
        
        # 检查页面标题
        title = tab.title
        logging.info(f"📄 页面标题: {title}")
        
        # 检查页面是否正常加载
        if "cursor" in title.lower() or "augment" in title.lower():
            logging.info("✅ 页面加载正常")
        else:
            logging.warning(f"⚠️ 页面可能未正常加载: {title}")
        
        # 检查 reCAPTCHA 语言设置
        logging.info("🔍 检查 reCAPTCHA 语言设置...")
        
        # 等待页面完全加载
        time.sleep(5)
        
        # 查找 reCAPTCHA iframe
        recaptcha_info = tab.run_js("""
            const iframes = document.querySelectorAll('iframe');
            const recaptchaUrls = [];
            
            iframes.forEach(iframe => {
                if (iframe.src && iframe.src.includes('recaptcha')) {
                    recaptchaUrls.push(iframe.src);
                }
            });
            
            return {
                iframeCount: iframes.length,
                recaptchaUrls: recaptchaUrls
            };
        """)
        
        logging.info(f"🔍 找到 {recaptcha_info.get('iframeCount', 0)} 个 iframe")
        
        recaptcha_urls = recaptcha_info.get('recaptchaUrls', [])
        if recaptcha_urls:
            for i, url in enumerate(recaptcha_urls, 1):
                logging.info(f"🔍 reCAPTCHA URL {i}: {url}")
                
                # 检查语言参数
                if 'hl=' in url:
                    import re
                    hl_match = re.search(r'hl=([^&]+)', url)
                    if hl_match:
                        hl_value = hl_match.group(1)
                        if hl_value == "ja":
                            logging.info(f"✅ reCAPTCHA 使用日语: hl={hl_value}")
                        else:
                            logging.warning(f"⚠️ reCAPTCHA 使用 {hl_value}，不是日语")
                    else:
                        logging.info("🔍 未找到 hl 参数")
                else:
                    logging.info("🔍 URL 中没有 hl 参数")
        else:
            logging.info("🔍 未找到 reCAPTCHA iframe")
        
        # 截图保存
        screenshot_path = f"edge_test_{int(time.time())}.png"
        tab.get_screenshot(path=screenshot_path)
        logging.info(f"📸 截图保存: {screenshot_path}")
        
        # 等待用户查看
        logging.info("⏳ 等待 10 秒供查看...")
        time.sleep(10)
        
        # 关闭浏览器
        browser_manager.quit()
        
        logging.info("🎉 Edge 浏览器测试完成！")
        return True
        
    except Exception as e:
        logging.error(f"❌ Edge 测试异常: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_edge_vs_chrome():
    """对比 Edge 和 Chrome 的差异"""
    
    logging.info("=" * 80)
    logging.info("🆚 Edge vs Chrome 对比测试")
    logging.info("=" * 80)
    
    browsers = ["edge", "chrome"]
    results = {}
    
    for browser_type in browsers:
        logging.info(f"\n🧪 测试 {browser_type.upper()}...")
        
        try:
            from browser_utils import BrowserManager
            
            browser_manager = BrowserManager(browser_type=browser_type)
            browser = browser_manager.init_browser()
            tab = browser.latest_tab
            
            # 获取浏览器信息
            info = tab.run_js("""
                return {
                    userAgent: navigator.userAgent,
                    language: navigator.language,
                    vendor: navigator.vendor,
                    platform: navigator.platform
                };
            """)
            
            results[browser_type] = info
            
            logging.info(f"📋 {browser_type.upper()} 信息:")
            logging.info(f"   User-Agent: {info.get('userAgent', 'N/A')[:100]}...")
            logging.info(f"   语言: {info.get('language', 'N/A')}")
            logging.info(f"   厂商: {info.get('vendor', 'N/A')}")
            logging.info(f"   平台: {info.get('platform', 'N/A')}")
            
            browser_manager.quit()
            
        except Exception as e:
            logging.error(f"❌ {browser_type.upper()} 测试失败: {e}")
            results[browser_type] = {"error": str(e)}
    
    # 对比结果
    logging.info("\n" + "=" * 80)
    logging.info("📊 对比结果")
    logging.info("=" * 80)
    
    if "edge" in results and "chrome" in results:
        edge_ua = results["edge"].get("userAgent", "")
        chrome_ua = results["chrome"].get("userAgent", "")
        
        logging.info("🔍 User-Agent 差异:")
        logging.info(f"Edge:   {edge_ua[:100]}...")
        logging.info(f"Chrome: {chrome_ua[:100]}...")
        
        if "Edg/" in edge_ua and "Chrome/" in chrome_ua:
            logging.info("✅ 两个浏览器有不同的标识")
        else:
            logging.warning("⚠️ 浏览器标识可能有问题")
    
    return results

def main():
    """主函数"""
    
    # 设置代理（如果需要）
    proxy = os.getenv("BROWSER_PROXY")
    if proxy:
        logging.info(f"🌐 使用代理: {proxy}")
    
    # 测试 1: Edge 基本功能
    basic_success = test_edge_basic()
    
    # 测试 2: Edge vs Chrome 对比
    comparison_results = test_edge_vs_chrome()
    
    # 总结
    logging.info("\n" + "=" * 80)
    logging.info("🎯 测试总结")
    logging.info("=" * 80)
    
    if basic_success:
        logging.info("✅ Edge 浏览器基本功能正常")
        logging.info("✅ 无痕模式配置生效")
        logging.info("✅ 可以正常访问目标网站")
    else:
        logging.error("❌ Edge 浏览器测试失败")
    
    if comparison_results:
        logging.info("✅ 浏览器对比测试完成")
        logging.info("✅ 不同浏览器有不同的指纹特征")
    
    return basic_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
