<html lang="en"><head><meta charset="utf-8"></meta><meta http-equiv="X-UA-Compatible" content="IE=edge"></meta><meta name="viewport" content="width=device-width, initial-scale=1"></meta><meta name="ulp-version" content="1.68.5"></meta><meta name="robots" content="noindex, nofollow"></meta><link rel="stylesheet" href="https://cdn.auth0.com/ulp/react-components/1.145.5/css/main.cdn.min.css"></link><style id="custom-styles-container">
      
        




        
          :root, .af-custom-form-container .af-form {
    --primary-color: #233A3E;
  }
        
      

        
          :root, .af-custom-form-container .af-form {
    --button-font-color: #ffffff;
  }
        
      

        
          :root {
    --secondary-button-border-color: #BBB8B4;
    --social-button-border-color: #BBB8B4;
    --radio-button-border-color: #BBB8B4;
  }
        
      

        
          :root {
    --secondary-button-text-color: #3C3934;
  }
        
      

        
          :root {
    --link-color: #736D63;
  }
        
      

        
          :root {
    --title-font-color: #221F1B;
  }
        
      

        
          :root {
    --font-default-color: #736D63;
  }
        
      

        
          :root {
    --widget-background-color: #ffffff;
  }
        
      

        
          :root {
    --box-border-color: #BBB8B4;
  }
        
      

        
          :root {
    --font-light-color: #65676e;
  }
        
      

        
          :root {
    --input-text-color: #3C3934;
  }
        
      

        
          :root {
    --input-border-color: #BBB8B4;
    --border-default-color: #BBB8B4;
  }
        
      

        
          :root {
    --input-background-color: #ffffff;
  }
        
      

        
          :root {
    --icon-default-color: #3C3934;
  }
        
      

        
          :root {
    --error-color: #d03c38;
    --error-text-color: #ffffff;
  }
        
      

        
          :root {
    --success-color: #13a688;
  }
        
      

        
          :root {
    --base-focus-color: #000000;
    --transparency-focus-color: rgba(0,0,0, 0.15);
  }
        
      

        
          :root {
    --base-hover-color: #2f92db;
    --transparency-hover-color: rgba(47,146,219, var(--hover-transparency-value));
  }
        
      

        
          :root {
    --ulp-captcha-widget-theme: light;
  }
        
      




        
          
        
      

        
          html, :root, .af-custom-form-container .af-form {
    font-size: 16px;
    --default-font-size: 16px;
  }
        
      

        
          body {
    --title-font-size: 1.5rem;
    --title-font-weight: var(--font-default-weight);
  }
        
      

        
          .c1decd0a0 {
    font-size: 0.875rem;
    font-weight: var(--font-default-weight);
  }
        
      

        
          .c7a57b5ef {
    font-size: 0.875rem;
    font-weight: var(--font-default-weight);
  }
  .ulp-passkey-benefit-heading {
    font-size: 1.025rem;
  }
        
      

        
          .cdee2a1c5, .ca146a5ea {
    font-size: 1rem;
    font-weight: var(--font-default-weight);
  }
        
      

        
          body {
    --ulp-label-font-size: 1rem;
    --ulp-label-font-weight: var(--font-default-weight);
  }
        
      

        
          .c81021c63, .cf58a1401, [id^='ulp-container-'] a {
    font-size: 0.875rem;
    font-weight: var(--font-bold-weight) !important;
  }
        
      

        
          
        
      




        
          :root {
    --button-border-width: 1px;
    --social-button-border-width: 1px;
    --radio-border-width: 1px;
  }
        
      

        
          body {
    --button-border-radius: 10px;
    --radio-border-radius: 10px;
  }
        
      

        
          :root {
    --input-border-width: 1px;
  }
        
      

        
          body {
    --input-border-radius: 10px;
  }

  .af-custom-form-container .af-form {
    --border-radius: 10px;
  }
        
      

        
          :root {
    --border-radius-outer: 30px;
  }
        
      

        
          :root {
    --box-border-width: 0px;
  }
        
      

        
          body {
    --shadow-component-outer: none;
  }
        
      




        
          
      .c093f7789 {
        display: none;
      }

      body {
        --header-title-spacing: 0;
      }
    
        
      

        
          
    .c093f7789 {
      content: url('https://www.augmentcode.com/android-chrome-512x512.png');
    }
  
        
      

        
          body {
    --logo-height: 52px;
  }
  .c093f7789 {
    height: var(--logo-height);
  }
  
        
      

        
          
    body {
      --header-alignment: center;
    }
  
        
      

        
          .ce355479a {
    display: flex;
    flex-direction: column;
  }
  .ce355479a form, .ce355479a > .cf48cdfb9 {
    margin: 0;
    order: 3;
  }
  .ce355479a ._alternate-action, .ce355479a #ulp-container-form-footer-start, .ce355479a #ulp-container-form-footer-end {
    order: 4
  }
  .ce355479a > .c54b35e2f {
    order: 2;
    margin-bottom: 24px;
  }
  .ce355479a .ca50f90b6 {
    order: 1;
    margin-top: 0;
  }
  .ce355479a > .c1f457822 {
    margin-bottom: 12px;
  }
        
      




        
          .c3e345b1a {
    --page-background-alignment: center;
  }
        
      

        
          body {
    --page-background-color: #F5F5F4;
  }
        
      

        
          
        
      




      
    </style><style>
    /* By default, hide features for javascript-disabled browsing */
    /* We use !important to override any css with higher specificity */
    /* It is also overriden by the styles in <noscript> in the header file */
    .no-js {
      clip: rect(0 0 0 0);
      clip-path: inset(50%);
      height: 1px;
      overflow: hidden;
      position: absolute;
      white-space: nowrap;
      width: 1px;
    }
  </style><noscript>
    <style>
      /* We use !important to override the default for js enabled */
      /* If the display should be other than block, it should be defined specifically here */
      .js-required { display: none !important; }
      .no-js {
        clip: auto;
        clip-path: none;
        height: auto;
        overflow: auto;
        position: static;
        white-space: normal;
        width: 100%;
      }
      .no-js-container {
        width: var(--prompt-width);
      }
    </style>
  </noscript><title>Sign up - Augment Code</title><meta charset="UTF-8"></meta><meta name="viewport" content="width=device-width, initial-scale=1.0"></meta><link rel="icon" href="https://www.augmentcode.com/favicon.ico"></link><script type="text/javascript" crossorigin="anonymous" async="" src="https://us.i.posthog.com/static/array.js"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz" async="" status="loaded"></script><script type="text/javascript" src="https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.3/twitter-ads.dynamic.js.gz" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js" async="" status="loaded"></script><script type="text/javascript" async="" data-global-segment-analytics-key="analytics" src="https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js"></script><script>
      (function handleGlobalUTMs() {
        const utmParams = [
          "utm_source",
          "utm_medium",
          "utm_campaign",
          "utm_term",
          "utm_content",
        ];

        const utmData = (function (cookieName) {
          const cookies = document.cookie.split("; ");
          for (const cookie of cookies) {
            const [key, value] = cookie.split("=");
            if (key === cookieName) {
              try {
                return JSON.parse(decodeURIComponent(value));
              } catch (e) {
                console.error("Failed to parse cookie value:", e);
              }
            }
          }
          return null;
        })("cookieGlobalUTMs");

        if (utmData) {
          const currentUrl = new URL(window.location.href);

          utmParams.forEach((param) => {
            if (utmData[param] && !currentUrl.searchParams.has(param)) {
              currentUrl.searchParams.set(param, utmData[param]);
            }
          });

          if (currentUrl.toString() !== window.location.href) {
            window.history.replaceState(null, "", currentUrl.toString());
          }
        }
      })();
    </script><script>
      // Load PostHog JS
      !(function (t, e) {
        var o, n, p, r;
        e.__SV ||
          ((window.posthog = e),
          (e._i = []),
          (e.init = function (i, s, a) {
            function g(t, e) {
              var o = e.split(".");
              2 == o.length && ((t = t[o[0]]), (e = o[1])),
                (t[e] = function () {
                  t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
                });
            }
            ((p = t.createElement("script")).type = "text/javascript"),
              (p.crossOrigin = "anonymous"),
              (p.async = !0),
              (p.src = s.api_host + "/static/array.js"),
              (r = t.getElementsByTagName("script")[0]).parentNode.insertBefore(
                p,
                r,
              );
            var u = e;
            for (
              void 0 !== a ? (u = e[a] = []) : (a = "posthog"),
                u.people = u.people || [],
                u.toString = function (t) {
                  var e = "posthog";
                  return (
                    "posthog" !== a && (e += "." + a), t || (e += " (stub)"), e
                  );
                },
                u.people.toString = function () {
                  return u.toString(1) + ".people (stub)";
                },
                o =
                  "capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep".split(
                    " ",
                  ),
                n = 0;
              n < o.length;
              n++
            )
              g(u, o[n]);
            e._i.push([i, s, a]);
          }),
          (e.__SV = 1));
      })(document, window.posthog || []);

      !(function () {
        var i = "analytics",
          analytics = (window[i] = window[i] || []);
        if (!analytics.initialize)
          if (analytics.invoked)
            window.console &&
              console.error &&
              console.error("Segment snippet included twice.");
          else {
            analytics.invoked = !0;
            analytics.methods = [
              "trackSubmit",
              "trackClick",
              "trackLink",
              "trackForm",
              "pageview",
              "identify",
              "reset",
              "group",
              "track",
              "ready",
              "alias",
              "debug",
              "page",
              "screen",
              "once",
              "off",
              "on",
              "addSourceMiddleware",
              "addIntegrationMiddleware",
              "setAnonymousId",
              "addDestinationMiddleware",
              "register",
            ];
            analytics.factory = function (e) {
              return function () {
                if (window[i].initialized)
                  return window[i][e].apply(window[i], arguments);
                var n = Array.prototype.slice.call(arguments);
                if (
                  [
                    "track",
                    "screen",
                    "alias",
                    "group",
                    "page",
                    "identify",
                  ].indexOf(e) > -1
                ) {
                  var c = document.querySelector("link[rel='canonical']");
                  n.push({
                    __t: "bpc",
                    c: (c && c.getAttribute("href")) || void 0,
                    p: location.pathname,
                    u: location.href,
                    s: location.search,
                    t: document.title,
                    r: document.referrer,
                  });
                }
                n.unshift(e);
                analytics.push(n);
                return analytics;
              };
            };
            for (var n = 0; n < analytics.methods.length; n++) {
              var key = analytics.methods[n];
              analytics[key] = analytics.factory(key);
            }
            analytics.load = function (key, n) {
              var t = document.createElement("script");
              t.type = "text/javascript";
              t.async = !0;
              t.setAttribute("data-global-segment-analytics-key", i);
              t.src =
                "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js";
              var r = document.getElementsByTagName("script")[0];
              r.parentNode.insertBefore(t, r);
              analytics._loadOptions = n;
            };
            analytics._cdn = "https://evs.grdt.augmentcode.com";

            analytics._writeKey = "ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg";
            analytics.SNIPPET_VERSION = "5.2.0";
            analytics.load(analytics._writeKey);

            analytics.ready(() => {
              window.posthog.init(
                "phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW",
                {
                  api_host: "https://us.i.posthog.com",
                  segment: window.analytics,
                  capture_pageview: false,
                  capture_pageleave: true,

                  loaded: (posthog) => {
                    // When the posthog library has loaded, call `analytics.page()` explicitly.
                    
                    analytics.page("Signup Page Visited");
                    
                  },
                },
              );
            });
          }
      })();
    </script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY"></script><script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      additionalParams = {};
      
      additionalParams["signup"] = "true";
      
      gtag("config", "G-F6GPDJDCJY", additionalParams);
    </script><style type="text/css">
      *,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/*! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com*/*,:after,:before{box-sizing:border-box;border:0 solid #e5e7eb}:after,:before{--tw-content:&#34;&#34;}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0}fieldset,legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.mb-12{margin-bottom:3rem}.flex{display:flex}.hidden{display:none}.h-full{height:100%}.w-12{width:3rem}.w-64{width:16rem}.w-96{width:24rem}.w-\[20px\]{width:20px}.w-full{width:100%}.max-w-xl{max-width:36rem}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-center{justify-content:center}.gap-1{gap:.25rem}.gap-2{gap:.5rem}.gap-24{gap:6rem}.gap-4{gap:1rem}.gap-8{gap:2rem}.text-pretty{text-wrap:pretty}.rounded-xl{border-radius:.75rem}.bg-neutral-100{--tw-bg-opacity:1;background-color:rgb(245 245 245/var(--tw-bg-opacity,1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}.text-center{text-align:center}.text-3xl{font-size:1.875rem;line-height:2.25rem}.text-sm{font-size:.875rem;line-height:1.25rem}.text-\[var\(--aug-green\)\]{color:var(--aug-green)}.text-\[var\(--aug-grey\)\]{color:var(--aug-grey)}.underline{text-decoration-line:underline}.underline-offset-2{text-underline-offset:2px}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}:root{--aug-grey:#736d63;--aug-green:#3d855e}header{padding:0!important;margin-bottom:.5rem}@media (min-width:1024px){.lg\:flex{display:flex}.lg\:w-1\/2{width:50%}.lg\:p-4{padding:1rem}}
    </style><script type="text/javascript" async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/16732971011/?random=1753689170843&cv=11&fst=1753689170843&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je57o0h2v9191852910za200zd9191852910&gcd=13l3l3l3l1l1&dma=0&tag_exp=101509157~102015666~103116026~103200004~103233427~104684208~104684211~104948813&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBSR2tlQ3pFelFGckljcG9pN1Z2S1dENE5yWjR5OUt4MaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEluX0ZXRUJmSWVMaHBfZjNYUXEwbnpEckUxT0k2UFJqo2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g&ref=https%3A%2F%2Fwww.augmentcode.com%2F&hn=www.googleadservices.com&frm=0&tiba=Sign%20up%20-%20Augment%20Code&npa=0&pscdl=noapi&auid=940730522.1753689171&uaa=&uab=&uafvl=&uamb=0&uam=&uap=Windows&uapv=&uaw=0&fledge=1&data=event%3Dgtag.config%3Bsignup%3Dtrue&rfmt=3&fmt=4"></script>    
    
    
    
      
    
    
    
    
    
    
    
    
    
  
    

    
    

    
    
    

    

    
    

    
    
    

    
  </head><body><div class="flex h-full w-full gap-8 items-center justify-center bg-neutral-100 lg:p-4"><aside class="lg:flex lg:w-1/2 items-center justify-center hidden"><div class="flex flex-col max-w-xl h-full gap-24"><div class="flex flex-col gap-2"><h1 class="text-3xl text-pretty">
              Create an account to start your 14-day trial of Augment
              Developer
            </h1><p class="text-[var(--aug-grey)]">
              Agent, completions, chat, and instructions powered by Augment's
              cutting-edge context engine for the most accurate code assistance.
            </p>
            
            
            
            
          </div><ul class="flex flex-col gap-2"><li class="flex gap-1"><div width="w-[20px]"><svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="#736d63"><path d="m576-192-51-51 129-129H240v-444h72v372h342L525-573l51-51 216 216-216 216Z"></path>
                  
                </svg>
                
              </div><span class="text-[var(--aug-grey)]">No credit card required</span>
              
              
            </li><li class="flex gap-1"><div width="w-[20px]"><svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="#736d63"><path d="m576-192-51-51 129-129H240v-444h72v372h342L525-573l51-51 216 216-216 216Z"></path>
                  
                </svg>
                
              </div><span class="text-[var(--aug-grey)]">Use the full power of Augment, no limits or restrictions</span>
              
              
            </li><li class="flex gap-1"><div width="w-[20px]"><svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="#736d63"><path d="m576-192-51-51 129-129H240v-444h72v372h342L525-573l51-51 216 216-216 216Z"></path>
                  
                </svg>
                
              </div><span class="text-[var(--aug-grey)]">Get help, share feedback, and join the community on
                Discord</span>
              
              
            </li><li class="flex gap-1"><div width="w-[20px]"><svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="#736d63"><path d="m576-192-51-51 129-129H240v-444h72v372h342L525-573l51-51 216 216-216 216Z"></path>
                  
                </svg>
                
              </div><span class="text-[var(--aug-grey)]">We don't train on your data</span>
              
              
            </li>
            
            
            
            
            
            
          </ul><div class="flex flex-col gap-4"><p class="text-[var(--aug-grey)] text-sm">
              Available in your favorite IDE, no switching required.
            </p><div class="flex gap-4"><div class="w-12"><svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="100" height="100"><path fill-rule="evenodd" clip-rule="evenodd" d="M70.9119 99.3171C72.4869 99.9307 74.2828 99.8914 75.8725 99.1264L96.4608 89.2197C98.6242 88.1787 100 85.9892 100 83.5872V16.4133C100 14.0113 98.6243 11.8218 96.4609 10.7808L75.8725 0.873756C73.7862 -0.130129 71.3446 0.11576 69.5135 1.44695C69.252 1.63711 69.0028 1.84943 68.769 2.08341L29.3551 38.0415L12.1872 25.0096C10.589 23.7965 8.35363 23.8959 6.86933 25.2461L1.36303 30.2549C-0.452552 31.9064 -0.454633 34.7627 1.35853 36.417L16.2471 50.0001L1.35853 63.5832C-0.454633 65.2374 -0.452552 68.0938 1.36303 69.7453L6.86933 74.7541C8.35363 76.1043 10.589 76.2037 12.1872 74.9905L29.3551 61.9587L68.769 97.9167C69.3925 98.5406 70.1246 99.0104 70.9119 99.3171ZM75.0152 27.2989L45.1091 50.0001L75.0152 72.7012V27.2989Z" fill="white"></path>
                    
                  </mask><g mask="url(#mask0)"><path d="M96.4614 10.7962L75.8569 0.875542C73.4719 -0.272773 70.6217 0.211611 68.75 2.08333L1.29858 63.5832C-0.515693 65.2373 -0.513607 68.0937 1.30308 69.7452L6.81272 74.754C8.29793 76.1042 10.5347 76.2036 12.1338 74.9905L93.3609 13.3699C96.086 11.3026 100 13.2462 100 16.6667V16.4275C100 14.0265 98.6246 11.8378 96.4614 10.7962Z" fill="#0065A9"></path><g filter="url(#filter0_d)"><path d="M96.4614 89.2038L75.8569 99.1245C73.4719 100.273 70.6217 99.7884 68.75 97.9167L1.29858 36.4169C-0.515693 34.7627 -0.513607 31.9063 1.30308 30.2548L6.81272 25.246C8.29793 23.8958 10.5347 23.7964 12.1338 25.0095L93.3609 86.6301C96.086 88.6974 100 86.7538 100 83.3334V83.5726C100 85.9735 98.6246 88.1622 96.4614 89.2038Z" fill="#007ACC"></path>
                      
                    </g><g filter="url(#filter1_d)"><path d="M75.8578 99.1263C73.4721 100.274 70.6219 99.7885 68.75 97.9166C71.0564 100.223 75 98.5895 75 95.3278V4.67213C75 1.41039 71.0564 -0.223106 68.75 2.08329C70.6219 0.211402 73.4721 -0.273666 75.8578 0.873633L96.4587 10.7807C98.6234 11.8217 100 14.0112 100 16.4132V83.5871C100 85.9891 98.6234 88.1786 96.4586 89.2196L75.8578 99.1263Z" fill="#1F9CF0"></path>
                      
                    </g><g style="mix-blend-mode: overlay" opacity="0.25"><path fill-rule="evenodd" clip-rule="evenodd" d="M70.8511 99.3171C72.4261 99.9306 74.2221 99.8913 75.8117 99.1264L96.4 89.2197C98.5634 88.1787 99.9392 85.9892 99.9392 83.5871V16.4133C99.9392 14.0112 98.5635 11.8217 96.4001 10.7807L75.8117 0.873695C73.7255 -0.13019 71.2838 0.115699 69.4527 1.44688C69.1912 1.63705 68.942 1.84937 68.7082 2.08335L29.2943 38.0414L12.1264 25.0096C10.5283 23.7964 8.29285 23.8959 6.80855 25.246L1.30225 30.2548C-0.513334 31.9064 -0.515415 34.7627 1.29775 36.4169L16.1863 50L1.29775 63.5832C-0.515415 65.2374 -0.513334 68.0937 1.30225 69.7452L6.80855 74.754C8.29285 76.1042 10.5283 76.2036 12.1264 74.9905L29.2943 61.9586L68.7082 97.9167C69.3317 98.5405 70.0638 99.0104 70.8511 99.3171ZM74.9544 27.2989L45.0483 50L74.9544 72.7012V27.2989Z" fill="url(#paint0_linear)"></path>
                      
                    </g>
                    
                    
                    
                    
                  </g><defs><filter id="filter0_d" x="-8.39411" y="15.8291" width="116.727" height="92.2456" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feflood flood-opacity="0" result="BackgroundImageFix"></feflood><fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></fecolormatrix><feoffset></feoffset><fegaussianblur stdDeviation="4.16667"></fegaussianblur><fecolormatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"></fecolormatrix><feblend mode="overlay" in2="BackgroundImageFix" result="effect1_dropShadow"></feblend><feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feblend>
                      
                      
                      
                      
                      
                      
                      
                    </filter><filter id="filter1_d" x="60.4167" y="-8.07558" width="47.9167" height="116.151" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feflood flood-opacity="0" result="BackgroundImageFix"></feflood><fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></fecolormatrix><feoffset></feoffset><fegaussianblur stdDeviation="4.16667"></fegaussianblur><fecolormatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"></fecolormatrix><feblend mode="overlay" in2="BackgroundImageFix" result="effect1_dropShadow"></feblend><feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feblend>
                      
                      
                      
                      
                      
                      
                      
                    </filter><lineargradient id="paint0_linear" x1="49.9392" y1="0.257812" x2="49.9392" y2="99.7423" gradientUnits="userSpaceOnUse"><stop stop-color="white"></stop><stop offset="1" stop-color="white" stop-opacity="0"></stop>
                      
                      
                    </lineargradient>
                    
                    
                    
                  </defs>
                  
                  
                  
                </svg>
                
              </div><div class="w-12"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 64 64"><defs><lineargradient id="a" x1=".85" x2="62.62" y1="62.72" y2="1.81" gradientUnits="userSpaceOnUse"><stop stop-color="#FF9419"></stop><stop offset=".43" stop-color="#FF021D"></stop><stop offset=".99" stop-color="#E600FF"></stop>
                      
                      
                      
                    </lineargradient>
                    
                  </defs><path fill="url(#a)" d="M20.34 3.66 3.66 20.34A12.504 12.504 0 0 0 0 29.18V59c0 2.76 2.24 5 5 5h29.82c3.32 0 6.49-1.32 8.84-3.66l16.68-16.68c2.34-2.34 3.66-5.52 3.66-8.84V5c0-2.76-2.24-5-5-5H29.18c-3.32 0-6.49 1.32-8.84 3.66Z"></path><path fill="#000" d="M48 16H8v40h40V16Z"></path><path fill="#fff" d="M30 47H13v4h17v-4Z"></path>
                  
                  
                  
                  
                </svg>
                
              </div><div class="w-12"><svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="none"><g clip-path="url(#a)"><mask id="b" width="48" height="48" x="0" y="0" maskUnits="userSpaceOnUse" style="mask-type: luminance"><path fill="#fff" d="M48 0H0v48h48V0Z"></path>
                      
                    </mask><g fill-rule="evenodd" clip-rule="evenodd" mask="url(#b)"><path fill="#fff" d="M4.234 10.081 14.502-.29v48.224L4.234 37.682v-27.6ZM43.91 10.175 33.514-.29l.21 48.224L43.98 37.68l-.07-27.506Z"></path><path fill="#fff" d="m14.5-.224 26.696 40.751L33.726 48 7.016 7.34 14.5-.225Z"></path><path fill="url(#c)" d="M4.234 10.081 14.502-.29v48.224L4.234 37.682v-27.6Z"></path><path fill="url(#d)" d="M43.91 10.175 33.514-.29l.21 48.224L43.98 37.68l-.07-27.506Z"></path><path fill="url(#e)" d="m14.5-.224 26.696 40.751L33.726 48 7.016 7.34 14.5-.225Z"></path><path fill="#000" fill-opacity=".13" d="m14.5 18.578-.015 1.6-8.2-12.14.76-.776 7.454 11.316Z"></path>
                      
                      
                      
                      
                      
                      
                    </g>
                    
                    
                  </g><defs><lineargradient id="c" x1="517.606" x2="517.606" y1="-.29" y2="4822.08" gradientUnits="userSpaceOnUse"><stop stop-color="#16B0ED" stop-opacity=".8"></stop><stop offset="1" stop-color="#0F59B2" stop-opacity=".837"></stop>
                      
                      
                    </lineargradient><lineargradient id="d" x1="-479.327" x2="-479.327" y1="-.29" y2="4822.08" gradientUnits="userSpaceOnUse"><stop stop-color="#7DB643"></stop><stop offset="1" stop-color="#367533"></stop>
                      
                      
                    </lineargradient><lineargradient id="e" x1="1716.04" x2="1716.04" y1="-.224" y2="4822.16" gradientUnits="userSpaceOnUse"><stop stop-color="#88C649" stop-opacity=".8"></stop><stop offset="1" stop-color="#439240" stop-opacity=".84"></stop>
                      
                      
                    </lineargradient><clippath id="a"><path fill="#fff" d="M0 0h48v48H0z"></path>
                      
                    </clippath>
                    
                    
                    
                    
                  </defs>
                  
                  
                </svg>
                
              </div><div class="w-12"><svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="none"><g clip-path="url(#a)"><path fill="#019833" d="M24.227 2.307 2.498 23.665 24.075 44.95 45.804 23.59 24.227 2.307Z"></path><path fill="#000" d="M24.075 45.023 2.422 23.665 24.226 2.233 45.88 23.59 24.151 45.023h-.076ZM2.573 23.665l21.578 21.21L45.804 23.59 24.226 2.38 2.573 23.665Z"></path><path fill="#66FE98" d="M24.077 2.381V.521L.531 23.74H2.5L24.077 2.38Z"></path><path fill="#000" d="M2.499 23.814H.379L24.152.447v2.009L2.499 23.814ZM.606 23.74h1.893L24.076 2.38V.67L.606 23.74Z"></path><path fill="#45FE02" d="M24.076 2.381V.521L47.698 23.74h-1.969L24.076 2.38Z"></path><path fill="#000" d="M47.773 23.814h-2.12L24 2.456V.446l23.773 23.368Zm-2.12-.074h1.893L24.076.67V2.38L45.653 23.74Z"></path><path fill="#017D17" d="M24.077 45.098v1.86L.531 23.74H2.5l21.577 21.358Z"></path><path fill="#000" d="M24.152 47.033.455 23.665h2.12l21.653 21.358v2.01h-.076ZM.606 23.74l23.47 23.07v-1.712L2.5 23.74H.606Z"></path><path fill="#000" d="M12.265 42.493H7.723l-1.817-1.042V8.186H4.467l-1.211-1.19V3.348l1.287-1.34h15.975l1.438 1.414v3.423L20.745 8.26h-1.212v11.014L30.89 8.26h-.606l-1.287-1.414V3.2l1.287-1.19H46.41l1.288 1.264v3.498l-.228.223-35.205 35.498Zm-4.164-1.712h3.331L45.805 6.177V4.093l-.227-.223H30.89l-.227.223v2.233l.378.372h4.089L17.868 23.516V6.623h2.12l.303-.297V4.167l-.455-.446H5.3l-.303.298v2.307l.227.223h2.423v34.01l.53.297-.076-.075Z"></path><path fill="#005D04" d="M24.076 45.098v1.86L47.698 23.74h-1.969L24.152 45.098h-.076Z"></path><path fill="#000" d="M24 47.033v-2.01l21.653-21.358h2.12L24 47.033Zm.076-1.935v1.711l23.47-23.07h-1.893L24.076 45.099Z"></path><path fill="#000" d="M23.924 47.256 0 23.666 24.076 0 48 23.59 23.924 47.257ZM.757 23.666 24 46.511 47.319 23.59 24.076.744.757 23.665Z"></path><path fill="#FEFEFE" d="m20.29 4.316.833-.446-.909-.893H4.922l-.757.744v2.902l.833.819.378-.819-.53-.52v-2.01l.379-.372h14.612l.379.595h.075Z"></path><path fill="#000" d="m4.996 7.59-.908-.892V3.795l.833-.818H20.29l.908.893-.984.52-.378-.595H5.298l-.378.298v2.01l.53.52-.455.968Zm-.832-.892.832.818.379-.744-.53-.52V4.166l.454-.372h14.688l.379.596.757-.372-.833-.82H4.92l-.756.745v2.902-.148Z"></path><path fill="#000" d="M11.129 41.079H8.252l-.53-.52V6.697H5.223l-.53-.521V4.093l.53-.52H19.76l.53.446v2.009l-.606.595h-2.726v18.233L33.463 8.409V6.55h-2.498l-.53-.521v-2.01l.605-.595h14.31l.605.596V5.58L11.13 41.08Zm-2.802-.074h2.802L45.879 5.73V4.242l-.53-.521H31.116l-.53.52v1.936l.454.446h2.499v1.935l-16.656 16.67V6.623h2.8l.53-.52V4.166l-.453-.372H5.299l-.454.447v2.01l.454.446h2.574v33.934l.454.447v-.074Z"></path><path fill="#FEFEFE" d="M7.8 6.623v34.01l.453.52-.378.67-.984-.967V7.516l.908-.893Z"></path><path fill="#000" d="m7.874 41.823-1.06-1.042V7.441l.985-.967v34.084l.454.521-.379.744Zm-.984-1.042.909.893.303-.595-.455-.52V6.697l-.833.818v33.265h.076Z"></path><path fill="gray" d="m5.3 6.623-.228.819H6.89l1.06-.819H5.3Z"></path><path fill="#000" d="M6.889 7.442H4.996l.303-.893H8.1l-1.135.893h-.076Zm-1.817-.075h1.817l.908-.744H5.3l-.227.744Z"></path><path fill="#FEFEFE" d="m30.738 7.516.379-.818-.606-.521v-1.86l.681-.67H45.35l.53.744.757-.521-.757-.744H30.662l-.757.744v2.902l.758.744m-12.19 13.991-1.514 3.498L33.615 8.558V6.623L18.397 21.507h.076Z"></path><path fill="#000" d="m17.035 25.08 1.514-3.573L33.843 6.549v2.084L17.187 25.079h-.152Zm1.439-3.499-1.363 3.35L33.616 8.632v-1.86L18.474 21.58ZM30.739 7.516l-.833-.744V3.795l.757-.744H45.88l.832.819-.832.595-.53-.744H31.269l-.682.67v1.786l.606.52-.454.82Zm-.758-.744.758.67.378-.744-.605-.521v-1.86l.68-.67h14.159l.53.67.681-.447-.757-.744H30.663l-.757.744v2.902h.076Z"></path><path fill="gray" d="m20.215 4.242.757-.447v2.828l-.833.819h-1.666v14.14l-1.514 3.497V6.623h2.726l.53-.372v-2.01Z"></path><path fill="#000" d="M17.035 25.079h-.076V6.549h2.801l.454-.372v-2.01l.833-.52v2.976l-.908.893h-1.59v14.065l-1.514 3.498Zm0-18.456v18.158l1.438-3.274V7.367h1.666l.833-.818V3.795l-.682.447v2.01l-.53.446h-2.725v-.075Z"></path><path fill="#CCC" d="m31.117 3.646-.53.521v1.935l.454.447h2.499v1.86L16.96 24.93V6.623h2.725l.53-.52v-2.01l-.53-.372H5.3l-.454.446v2.01l.454.446h2.498v33.935l.455.447h2.877L45.88 5.73V4.167l-.53-.52H31.117Z"></path><path fill="#000" d="M11.129 41.079H8.252l-.53-.52V6.697H5.223l-.53-.521V4.093l.53-.52H19.76l.53.446v2.009l-.606.595h-2.726v18.233L33.463 8.409V6.55h-2.498l-.53-.521v-2.01l.605-.595h14.31l.605.596V5.58L11.13 41.08Zm-2.802-.074h2.802L45.879 5.73V4.242l-.53-.521H31.116l-.53.52v1.936l.454.446h2.499v1.935l-16.656 16.67V6.623h2.8l.53-.52V4.166l-.453-.372H5.299l-.454.447v2.01l.454.446h2.574v33.934l.454.447v-.074Z"></path><path fill="gray" d="m45.956 4.242.757-.447V6.55l-34.978 35.2h-3.71l.379-.67h2.877l34.6-35.274V4.242h.075Z"></path><path fill="#000" d="M11.735 41.823H7.949l.379-.744h2.877L45.88 5.805V4.242l.908-.521v2.828L11.735 41.823Zm-3.71-.074h3.634l34.978-35.2v-2.68l-.681.373v1.563L11.205 41.079H8.328l-.303.595v.075Z"></path><path fill="gray" d="m33.616 6.698-.757.818h-2.045l.455-.818h2.347Z"></path><path fill="#000" d="M32.782 7.516h-2.12l.53-.893h2.498l-.832.893h-.076Zm-1.968-.074h1.968l.681-.744h-2.195l-.454.744Z"></path><path fill="#CCC" d="m25.21 26.493-.68.595-.682 1.86v.15l.53.52H26.573l.53-.52.606-1.935v-.149l-.454-.447h-1.969l-.076-.074Zm-3.785 5.284s-.076 0-.151.149l-.227.967c0 .075 0 .149.15.223h1.212L19.381 41.6c0 .074 0 .149.076.223h4.39s.152 0 .152-.074l.303-.893c0-.075 0-.149-.076-.223h-.984L26.27 32c0-.074 0-.149-.075-.223h-4.77Zm14.006 0h-.075l-.909 1.042H33.01l-.985-.968H28.466s-.151 0-.151.075l-.303.967c0 .075 0 .149.075.223h.909l-2.877 8.41c0 .074 0 .148.076.223h3.71s.15 0 .15-.075l.304-.818c0-.075 0-.149-.076-.223h-.681l1.817-5.73h3.33l-2.119 6.623c0 .074 0 .148.076.223h3.558s.152 0 .152-.075l.302-.744c0-.074 0-.149-.075-.223h-.682l1.893-5.805h3.18l-2.12 6.698c0 .074 0 .149.076.223h3.937s.151 0 .151-.074l.303-.819c0-.074 0-.149-.076-.223h-.757l2.347-7.59v-.15L44.138 32H41.034l-.909.968h-1.514L37.703 32H35.28l.151-.223Z"></path><path fill="#000" d="M23.924 42.121h-4.542c-.303-.074-.455-.372-.303-.67l2.877-8.037H21.123c-.303 0-.454-.372-.379-.595l.228-1.042c0-.224.227-.372.378-.372h4.846c.302.074.454.372.302.67l-2.8 8.111h.605c.303.075.454.372.378.67l-.302.893c0 .223-.303.372-.53.372h.075ZM22.41 32.67h.53l-3.104 8.707h3.937l.151-.521h-1.211l3.104-8.782h-4.164l-.151.596h.984-.076Zm16.808 9.377h-.152c-.303-.075-.454-.372-.378-.67l1.968-6.251h-2.422l-1.666 5.135h.378c.303.074.379.372.303.67l-.303.744c-.075.223-.302.297-.454.297h-3.71c-.302-.074-.454-.372-.378-.67l1.968-6.176h-2.574l-1.59 4.986h.303c.303.074.454.372.303.67l-.303.818a.569.569 0 0 1-.53.372H26.12c-.303-.074-.454-.372-.303-.67l2.726-7.962h-.606c-.303-.075-.454-.373-.303-.67l.303-.968c0-.223.303-.372.454-.372h3.483c.151 0 .303 0 .378.15l.833.892h1.136l.833-.893c0-.074.151-.149.302-.149h2.347c.152 0 .303 0 .38.15l.832.892h1.211l.757-.819a.584.584 0 0 1 .379-.148h2.801c.152 0 .303 0 .454.223l.758.967c.075.15.15.298.075.447l-2.195 7.07H43.608c.304.074.38.372.304.67l-.303.818c-.076.223-.303.298-.454.298h-3.937v.149ZM29.073 32.67h.53l-2.953 8.632h3.255l.152-.372h-.833l2.12-6.474h4.088l-2.195 6.846h3.18l.15-.372h-.832l2.12-6.549h3.937l-2.196 6.921h3.483l.151-.372h-.984l2.498-7.962-.605-.82h-2.65l-.908.968h-1.893l-.985-1.041h-2.12l-.984 1.041h-1.817l-.984-1.041h-3.255l-.152.52h.606l.076.075Zm-4.619-2.53c-.151 0-.227 0-.378-.15l-.53-.52c-.152-.149-.152-.372-.152-.521l.682-1.86c0-.075.075-.15.151-.224l.682-.595s.151-.075.227-.149h1.968c.152 0 .227 0 .379.149l.454.446c.151.15.151.298.151.521l-.605 1.935c0 .075 0 .149-.152.223l-.53.521a.584.584 0 0 1-.378.15h-2.044l.075.074Zm-.303-1.117.379.372h1.893l.454-.446.53-1.786-.303-.298h-1.741l-.53.447-.606 1.711h-.076Z"></path><path fill="#CCC" d="m25.287 26.642-.681.595-.682 1.86.53.522h2.044l.53-.521.606-1.935-.454-.447h-1.893v-.074Zm-3.861 5.284-.227.967h1.438L19.533 41.6h4.391l.303-.893h-1.211l3.104-8.781h-4.694Zm14.082 0-.984 1.041h-1.59l-.984-1.041h-3.483l-.303.967H29.3l-2.953 8.633h3.71l.303-.819h-.833l1.968-6.028h3.71l-2.195 6.847h3.558l.303-.745h-.833l1.968-6.102h3.483L39.294 41.6h3.936l.303-.819h-.984l2.423-7.74-.757-.967h-2.802l-.908.968h-1.666L37.855 32h-2.271l-.076-.074Z"></path><path fill="#000" d="M24 41.674h-4.543l3.104-8.707h-1.438l.302-1.116h4.77l-3.028 8.782h1.211l-.303 1.041H24Zm-4.392-.074h4.316l.227-.819H22.94L26.044 32H21.5l-.227.893h1.439L19.608 41.6Zm23.546-.074h-4.012l2.195-6.921H37.93l-1.968 6.028h.833l-.379.818h-3.634l2.196-6.846h-3.559l-1.893 5.953h.833l-.303.893h-3.785l2.953-8.632h-1.136l.378-1.042h3.559l.984 1.042h1.59l.984-1.042h2.272l.984 1.042h1.665l.909-.968h2.877l.757 1.042-2.423 7.74h.984l-.378.893h-.076Zm-3.861-.075h3.861l.303-.744h-.984l2.422-7.814-.68-.967h-2.802l-.909.967h-1.741l-.984-1.042h-2.196l-.984 1.042h-1.666l-.984-1.042h-3.407l-.303.819h1.136l-2.953 8.632h3.634l.227-.744h-.832l1.968-6.102h3.786l-2.196 6.846h3.483l.303-.67h-.833l2.044-6.25h3.634l-2.196 6.92-.151.15ZM26.498 29.62h-2.12l-.53-.521.682-1.86.681-.596h1.968l.455.446-.606 2.01-.53.595v-.074Zm-2.044-.075h2.044l.53-.52.53-1.936-.379-.372h-1.892l-.682.521-.681 1.786.454.447.076.074Z"></path>
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                  </g><defs><clippath id="a"><path fill="#fff" d="M0 0h48v48H0z"></path>
                      
                    </clippath>
                    
                  </defs>
                  
                  
                </svg>
                
              </div>
              
              
              
              
            </div>
            
            
          </div>
          
          
          
        </div>
        
      </aside><main class="flex flex-col lg:w-1/2 h-full items-center justify-center rounded-xl bg-white"><div class="flex flex-col text-center mb-12"><div class="w-64"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 233 32"><path fill="currentColor" fill-rule="evenodd" d="M211.967 2.78h2.887v23h-2.887v-2.569c-1.047 1.809-2.856 2.982-5.584 2.982-3.902 0-7.297-3.236-7.297-8.883 0-5.615 3.395-8.883 7.297-8.883 2.728 0 4.537 1.174 5.584 2.982V2.78Zm-4.854 8.122c-2.951 0-4.886 2.348-4.886 6.408 0 4.061 1.935 6.409 4.886 6.409 2.569 0 4.98-1.904 4.98-6.409s-2.411-6.408-4.98-6.408Zm12.722 7.297c.127 3.68 2.728 5.456 5.266 5.456s3.934-1.142 4.664-2.823h2.982c-.793 2.95-3.49 5.361-7.646 5.361-5.393 0-8.375-3.87-8.375-8.914 0-5.394 3.616-8.852 8.28-8.852 5.203 0 8.344 4.378 7.963 9.772h-13.134Zm.032-2.475h10.056c-.063-2.411-1.871-4.822-4.917-4.822-2.57 0-4.886 1.396-5.139 4.822Zm-29.986 10.47c3.966 0 8.185-2.697 8.185-8.884 0-6.186-4.219-8.883-8.185-8.883s-8.185 2.697-8.185 8.883c0 6.187 4.219 8.883 8.185 8.883Zm5.108-8.884c0 4.378-2.507 6.345-5.108 6.345-2.601 0-5.108-1.808-5.108-6.345 0-4.663 2.507-6.345 5.108-6.345 2.601 0 5.108 1.809 5.108 6.345Zm-14.277-2.887h-2.919c-.412-2.03-2.189-3.458-4.41-3.458-2.569 0-4.917 2-4.917 6.282 0 4.346 2.379 6.408 4.917 6.408 2.411 0 4.125-1.618 4.537-3.426h2.982c-.571 3.204-3.648 5.964-7.614 5.964-4.885 0-7.963-3.712-7.963-8.914 0-5.076 3.204-8.852 8.249-8.852 4.124 0 6.757 3.11 7.138 5.996ZM155.38 4.43h-2.887v4.283h-2.697v2.316h2.697v10.47c0 3.552.761 4.282 4.029 4.282h2.094V23.37h-1.46c-1.649 0-1.776-.444-1.776-2.22V11.028h3.236V8.713h-3.236V4.43Zm-17.659 6.853c.983-1.618 2.633-2.856 5.52-2.856 4.124 0 5.615 2.665 5.615 6.568V25.78h-2.887v-9.93c0-2.633-.444-4.917-3.743-4.917-2.792 0-4.505 1.935-4.505 5.583v9.264h-2.887V8.713h2.887v2.57Zm-12.613 12.372c-2.538 0-5.139-1.776-5.266-5.456h13.134c.381-5.394-2.76-9.772-7.963-9.772-4.663 0-8.28 3.458-8.28 8.852 0 5.044 2.982 8.914 8.375 8.914 4.156 0 6.853-2.41 7.646-5.361h-2.982c-.73 1.681-2.126 2.823-4.664 2.823Zm4.823-7.93h-10.057c.254-3.427 2.569-4.823 5.139-4.823 3.046 0 4.854 2.411 4.918 4.822ZM93.767 25.78H90.88V8.713h2.887v2.57c.983-1.618 2.57-2.856 5.14-2.856 2.601 0 4.155 1.143 4.917 3.014 1.364-2.094 3.362-3.014 5.742-3.014 3.965 0 5.52 2.665 5.52 6.568V25.78h-2.887v-9.93c0-2.633-.508-4.917-3.648-4.917-2.475 0-4.125 1.935-4.125 5.583v9.264h-2.887v-9.93c0-2.633-.507-4.917-3.648-4.917-2.475 0-4.124 1.935-4.124 5.583v9.264ZM87.829 8.713h-2.887v2.697c-1.047-1.809-2.697-2.983-5.425-2.983-3.776 0-7.234 3.078-7.234 8.534 0 5.489 3.458 8.534 7.234 8.534 2.728 0 4.378-1.142 5.425-2.95v1.618c0 2.316-.476 3.109-1.11 3.775-.762.825-1.936 1.27-3.49 1.27-2.665 0-3.585-1.175-3.966-2.697h-3.078c.54 3.458 3.141 5.234 7.012 5.234 2.538 0 4.663-.825 5.9-2.252.984-1.079 1.619-2.506 1.619-5.996V8.713ZM75.424 16.96c0-3.87 1.999-6.06 4.822-6.06 2.57 0 4.823 1.746 4.823 6.06 0 4.347-2.253 6.092-4.823 6.092-2.823 0-4.822-2.19-4.822-6.092Zm-7.927 6.378c-.983 1.618-2.538 2.855-5.361 2.855-3.966 0-5.457-2.665-5.457-6.567V8.713h2.887V18.77c0 2.634.444 4.918 3.585 4.918 2.728 0 4.346-1.936 4.346-5.584v-9.39h2.887v17.068h-2.887v-2.443Zm-16.246-8.06c-7.01.794-11.548 1.968-11.548 5.997 0 3.14 2.665 4.917 5.87 4.917 3.013 0 4.79-1.015 5.837-2.76.032 1.015.127 1.713.222 2.348h2.919c-.318-1.618-.476-3.585-.445-6.44l.032-3.934c.032-4.79-2.094-7.043-6.789-7.043-3.331 0-6.567 2.062-6.852 5.742h2.982c.127-2.094 1.523-3.395 3.902-3.395 2.125 0 3.87 1.047 3.87 4.156v.413Zm-8.343 5.933c0-2.347 3.33-3.109 8.565-3.648v1.079c0 4.029-2.57 5.266-5.266 5.266-2.062 0-3.3-1.079-3.3-2.697ZM14.316.185c.184-.101.433-.14.722-.14s.54.04.725.147a.593.593 0 0 1 .313.536v.008l-.159 4.22a.494.494 0 0 1-.278.446c-.157.083-.366.113-.601.113s-.444-.03-.601-.113a.494.494 0 0 1-.279-.447c-.04-1.05-.072-1.864-.098-2.441v-.005a47.416 47.416 0 0 0-.04-1.257c-.013-.248-.02-.406-.02-.465V.708c0-.108.025-.213.082-.307a.612.612 0 0 1 .234-.216Zm3.354 0c.184-.101.434-.14.722-.14.29 0 .54.04.725.147a.592.592 0 0 1 .313.536v.008l-.158 4.22a.494.494 0 0 1-.28.446c-.156.083-.365.113-.6.113s-.444-.03-.6-.113a.493.493 0 0 1-.279-.447 332.95 332.95 0 0 0-.099-2.441v-.005a46.976 46.976 0 0 0-.04-1.257c-.012-.248-.02-.406-.02-.465V.708c0-.108.026-.213.083-.307a.611.611 0 0 1 .233-.216Zm2.782 6.572c.2-.18.446-.264.727-.264h6.267c.805 0 1.452.215 1.91.672.46.458.673 1.115.673 1.933v5.332c0 .622.127 1.033.335 1.273.201.231.585.383 1.221.398h.01c.254.019.47.118.634.304a.99.99 0 0 1 .24.68c0 .25-.076.475-.233.67a.81.81 0 0 1-.653.313c-.634.015-1.018.167-1.22.398-.209.241-.334.656-.334 1.297v5.332c0 .541-.094 1.013-.293 1.41-.2.402-.498.703-.89.905h-.001c-.39.198-.86.292-1.399.292h-6.058v.005h-.21c-.284 0-.531-.092-.73-.28a.916.916 0 0 1-.301-.68c0-.251.084-.479.251-.668a.88.88 0 0 1 .686-.294h5.82c.296 0 .484-.075.603-.196.119-.119.198-.32.198-.653v-5.38c0-.5.1-.957.304-1.372.2-.413.474-.742.82-.985a1.97 1.97 0 0 1 .176-.11 1.994 1.994 0 0 1-.176-.11 2.518 2.518 0 0 1-.82-.987 3.082 3.082 0 0 1-.304-1.37V9.264c0-.33-.079-.533-.198-.653-.12-.12-.31-.197-.603-.197h-5.82a.886.886 0 0 1-.685-.294.976.976 0 0 1-.252-.669c0-.275.1-.512.305-.695Zm0 0v.001l.14.155-.14-.156ZM3.904 7.17c.457-.457 1.105-.672 1.91-.672h6.267c.282 0 .527.087.727.264v.001c.202.182.306.42.306.694 0 .255-.085.48-.252.668a.88.88 0 0 1-.686.295h-5.82c-.295 0-.483.077-.603.197-.118.118-.198.32-.198.653v5.357c0 .498-.1.956-.303 1.37a2.538 2.538 0 0 1-.82.986 1.99 1.99 0 0 1-.177.11c.06.034.12.07.176.11.346.242.62.573.82.987.203.414.304.872.304 1.37v5.38c0 .333.08.535.198.654.12.121.31.197.603.197h5.82c.272 0 .507.096.685.292.17.19.252.417.252.67a.909.909 0 0 1-.3.679c-.2.19-.448.28-.732.28H5.605v-.01c-.453-.022-.851-.115-1.19-.287a1.982 1.982 0 0 1-.89-.904c-.197-.396-.294-.87-.294-1.411v-5.332c0-.637-.127-1.055-.335-1.297-.2-.23-.584-.382-1.219-.397a.802.802 0 0 1-.653-.315 1.044 1.044 0 0 1-.233-.67.99.99 0 0 1 .24-.679.912.912 0 0 1 .633-.303l.01-.001c.637-.016 1.022-.167 1.222-.398.21-.241.335-.65.335-1.273V9.103c0-.818.215-1.475.673-1.933Zm18.622 7.617a2.276 2.276 0 1 0 0 4.552 2.276 2.276 0 0 0 0-4.552ZM8.939 17.063a2.276 2.276 0 1 1 4.552 0 2.276 2.276 0 0 1-4.552 0Z" clip-rule="evenodd"></path>
              
            </svg>
            
          </div>
          
        </div><main class="_widget login-id"><section class="c2fbc8d4b _prompt-box-outer c1476f1d9"><div class="c939d047e c50d98004"><div class="c018809a1"><header class="c7b5fd7fc c4ed47fa8"><div title="augment" id="custom-prompt-logo" style="width: auto !important; height: 60px !important; position: static !important; margin: auto !important; padding: 0 !important; background-color: transparent !important; background-position: center !important; background-size: contain !important; background-repeat: no-repeat !important"></div><img class="c093f7789 c01932319" id="prompt-logo-center" src="https://www.augmentcode.com/android-chrome-512x512.png" alt="augment"></img><h1 class="cdecd2126 cb92d7579"> </h1><div class="c1decd0a0 c9b6c2f9e"><p class="cefd65b17 c8f0b75da"> </p>
            
          </div>
          
        
          
        
          
            
          
        
          
        </header><div class="c7a57b5ef ce355479a"><div class="c24a23196 cf48cdfb9"><div class="c82ce03b3"><form method="POST" class="cc5743ba0 _form-login-id" data-form-primary="true"><input type="hidden" name="state" value="hKFo2SBSR2tlQ3pFelFGckljcG9pN1Z2S1dENE5yWjR5OUt4MaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEluX0ZXRUJmSWVMaHBfZjNYUXEwbnpEckUxT0k2UFJqo2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g"></input><div class="c24a23196 cf48cdfb9"><div class="c82ce03b3"><div class="input-wrapper _input-wrapper"><div class="c64610e35 c49debcbb text ccf50b817 cd93cac30" data-action-text="" data-alternate-action-text=""><label class="c7e19a173 no-js c5c6f3edf ce1debc33" for="username">
                                  Email address
                                </label><input class="input cf114962b c97fe70f9" inputmode="email" name="username" id="username" type="text" value="" required="" autocomplete="email" autocapitalize="none" spellcheck="false" autofocus=""></input><div class="c7e19a173 js-required c5c6f3edf ce1debc33" data-dynamic-label-for="username" aria-hidden="true">
                                  Email address*
                                </div>
                                
                              
                                
                              
                                
                              </div>
                              
                            
                              
                            </div><div data-captcha-provider="auth0_v2" data-captcha-sitekey="0x4AAAAAAAQFNSW6xordsuIq" data-captcha-lang="en" class="ulp-captcha-container"><div class="ulp-auth0-v2-captcha ulp-captcha" id="ulp-auth0-v2-captcha"><div></div></div><input type="hidden" name="captcha" value=""></input>
                              
                                
                                  
                                    
                                      
                                        
                                      
                                    
                                  
                                
                              
                            
                              
                                
                              
                            </div><span id="error-element-third-party-captcha" class="ulp-input-error-message ulp-captcha-client-error hide"><span class="ulp-input-error-icon" role="img" aria-label="Error"></span>
                              
                            We couldn’t load the security challenge. Please try again. (Error code: #{errorCode})</span>
                        
                      
                        
                          
                            
                          
                        
                      
                        
                      
                        
                          
                            
                          
                            
                          
                        
                      </div>
                      
                    </div><input class="hide" type="password" autocomplete="off" tabindex="-1" aria-hidden="true"></input><input type="hidden" id="js-available" name="js-available" value="true"></input><input type="hidden" id="webauthn-available" name="webauthn-available" value="true"></input><input type="hidden" id="is-brave" name="is-brave" value="false"></input><input type="hidden" id="webauthn-platform-available" name="webauthn-platform-available" value="false"></input><div class="cbc7f6475"><button type="submit" name="action" value="default" class="cdee2a1c5 c0acda5b0 c6536841f c3e77fae7 _button-login-id" data-action-button-primary="true">Continue</button>
                      
                        
                      
                    </div>
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  </form>
                
              
                
                  
                
              </div>
              
            </div><div class="ulp-alternate-action  _alternate-action __s16nu9"><p class="cefd65b17 c8f0b75da ce29338d9"><a class="cf58a1401 ccf031abb" href="/u/signup/identifier?state=hKFo2SBSR2tlQ3pFelFGckljcG9pN1Z2S1dENE5yWjR5OUt4MaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEluX0ZXRUJmSWVMaHBfZjNYUXEwbnpEckUxT0k2UFJqo2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g"></a>
                
              </p>
              
            </div><div class="c844b2599 c54b35e2f"><span>Or</span>
              
            </div><div class="cf967bae7 ca50f90b6"><form method="post" data-provider="google" class="ca146a5ea ce6234270 c9040cc45" data-form-secondary="true"><input type="hidden" name="state" value="hKFo2SBSR2tlQ3pFelFGckljcG9pN1Z2S1dENE5yWjR5OUt4MaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEluX0ZXRUJmSWVMaHBfZjNYUXEwbnpEckUxT0k2UFJqo2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g"></input><input type="hidden" name="connection" value="google-oauth2"></input><button type="submit" class="c8765d084 cc86e836f c6cbae097" data-provider="google" data-action-button-secondary="true"><span class="cf7baab3b cc8ab8afc" data-provider="google"></span><span class="ce361c46e">Continue with Google</span>
                    
                      
                    
                  
                    
                  </button>
                  
                
                  
                
                  
                </form><form method="post" data-provider="oauth2" class="ca146a5ea ce6234270 _social-button-container-oauth2" data-form-secondary="true"><input type="hidden" name="state" value="hKFo2SBSR2tlQ3pFelFGckljcG9pN1Z2S1dENE5yWjR5OUt4MaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEluX0ZXRUJmSWVMaHBfZjNYUXEwbnpEckUxT0k2UFJqo2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g"></input><input type="hidden" name="connection" value="mscommon"></input><button type="submit" class="c8765d084 cc86e836f _social-button-oauth2" data-provider="oauth2" data-action-button-secondary="true"><img class="cf7baab3b c1f2d33b4" src="https://storage.googleapis.com/augment-bazel-data/public/ms-symbollockup_mssymbol_19.svg" alt="Connection icon"></img><span class="ce361c46e">Continue with Microsoft</span>
                    
                      
                    
                  
                    
                  </button>
                  
                
                  
                
                  
                </form><form method="post" data-provider="github" class="ca146a5ea ce6234270 cc43b4d31" data-form-secondary="true"><input type="hidden" name="state" value="hKFo2SBSR2tlQ3pFelFGckljcG9pN1Z2S1dENE5yWjR5OUt4MaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEluX0ZXRUJmSWVMaHBfZjNYUXEwbnpEckUxT0k2UFJqo2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g"></input><input type="hidden" name="connection" value="github"></input><button type="submit" class="c8765d084 cc86e836f c25c717ec" data-provider="github" data-action-button-secondary="true"><span class="cf7baab3b cc8ab8afc" data-provider="github"></span><span class="ce361c46e">Continue with GitHub</span>
                    
                      
                    
                  
                    
                  </button>
                  
                
                  
                
                  
                </form>
              
            
              
                
              
                
              
                
              
            
              
            </div>
          
        
          
            
          
        
          
        
          
            
          
        
          
        
          
            
          
        
          
        
          
            
          
            
          
        </div>
        
      
        
      </div>
      
    
      
    
      
    </div>
    
  
    
  </section>
  
</main><script id="client-scripts">
window.ulpFlags = {"enable_ulp_wcag_compliance":false,"enable_ulp_rtl_support":false};!function(){var e,t,C,S,k,F,T,P,n,L,N,j,q,R,O,r,a,i={exports:function(e,t){return"object"==typeof e.ulpFlags&&null!==e.ulpFlags?e.ulpFlags:{}}}.exports(window,document),o=((e={}).exports=function(n,a){var r={},i={};function c(e,t){if(e.classList)return e.classList.add(t);var n=e.className.split(" ");-1===n.indexOf(t)&&(n.push(t),e.className=n.join(" "))}function o(e,t,n,r){return e.addEventListener(t,n,r)}function u(e){return"string"==typeof e}function l(e,t){return u(e)?a.querySelector(e):e.querySelector(t)}function s(e,t){if(e.classList)return e.classList.remove(t);var n=e.className.split(" "),r=n.indexOf(t);-1!==r&&(n.splice(r,1),e.className=n.join(" "))}function f(e,t){return e.getAttribute(t)}function d(e,t,n){return e.setAttribute(t,n)}function p(e){return e.remove()}var e=["text","number","email","password","tel","url"],t="select,textarea,"+e.map(function(e){return'input[type="'+e+'"]'}).join(",");return{addClass:c,toggleClass:function(e,t,n){if(!0===n||!1===n)return r=e,a=t,!0!==n?s(r,a):c(r,a);var r,a;if(e.classList)return e.classList.toggle(t);var i=e.className.split(" "),o=i.indexOf(t);-1!==o?i.splice(o,1):i.push(t),e.className=i.join(" ")},hasClass:function(e,t){return e.classList?e.classList.contains(t):-1!==e.className.split(" ").indexOf(t)},addClickListener:function(e,t){return o(e,"click",t)},addEventListener:o,getAttribute:f,hasAttribute:function(e,t){return e.hasAttribute(t)},getElementById:function(e){return a.getElementById(e)},getParent:function(e){return e.parentNode},isString:u,loadScript:function(e,t){var n=a.createElement("script");for(var r in t)r.startsWith("data-")?n.dataset[r.replace("data-","")]=t[r]:n[r]=t[r];n.src=e,a.body.appendChild(n)},removeScript:function(e){a.querySelectorAll('script[src="'+e+'"]').forEach(function(e){e.remove()})},poll:function(e){var i=e.interval||2e3,t=e.url||n.location.href,o=e.condition||function(){return!0},c=e.onSuccess||function(){},u=e.onError||function(){};return setTimeout(function r(){var a=new XMLHttpRequest;return a.open("GET",t),a.setRequestHeader("Accept","application/json"),a.onload=function(){if(200===a.status){var e="application/json"===a.getResponseHeader("Content-Type").split(";")[0]?JSON.parse(a.responseText):a.responseText;return o(e)?c():setTimeout(r,i)}if(429!==a.status)return u({status:a.status,responseText:a.responseText});var t=1e3*Number.parseInt(a.getResponseHeader("X-RateLimit-Reset")),n=t-(new Date).getTime();return setTimeout(r,i<n?n:i)},a.send()},i)},querySelector:l,querySelectorAll:function(e,t){var n=u(e)?a.querySelectorAll(e):e.querySelectorAll(t);return Array.prototype.slice.call(n)},removeClass:s,removeElement:p,setAttribute:d,removeAttribute:function(e,t){return e.removeAttribute(t)},swapAttributes:function(e,t,n){var r=f(e,t),a=f(e,n);d(e,n,r),d(e,t,a)},setGlobalFlag:function(e,t){r[e]=!!t},getGlobalFlag:function(e){return!!r[e]},setSubmittedForm:function(e,t){i[e]=t},getSubmittedForm:function(e){return i[e]},preventFormSubmit:function(e){e.stopPropagation(),e.preventDefault()},matchMedia:function(e){return"function"!=typeof n.matchMedia&&n.matchMedia(e).matches},dispatchEvent:function(e,t,n){var r;"function"!=typeof Event?(r=a.createEvent("Event")).initCustomEvent(t,n,!1):r=new Event(t,{bubbles:n}),e.dispatchEvent(r)},timeoutPromise:function(e,a){return new Promise(function(t,n){var r=setTimeout(function(){n(new Error("timeoutPromise: promise timed out"))},e);a.then(function(e){clearTimeout(r),t(e)},function(e){clearTimeout(r),n(e)})})},createMutationObserver:function(e){return"undefined"==typeof MutationObserver?null:new MutationObserver(e)},consoleWarn:function(){(console.warn||console.log).apply(console,arguments)},getConfigJson:function(e){try{var t=l(e);if(!t)return null;var n=t.value;return n?JSON.parse(n):null}catch(e){return null}},getCSSVariable:function(e){return getComputedStyle(a.documentElement).getPropertyValue(e)},removeAndTrimString:function(e,t){var n=new RegExp(t,"g"),r=e.replace(n,"");return r=r.replace(/\s+/g,"  ").trim()},htmlEncode:function(e){var t=a.createTextNode(e),n=a.createElement("span");return n.appendChild(t),n.innerHTML||""},cleanServerErrorMessage:function(e,t){0<e.length&&0<t.length&&t.forEach(function(e){p(e)})},setTimeout:setTimeout,globalWindow:n,SUPPORTED_INPUT_TYPES:e,ELEMENT_TYPE_SELECTOR:t,RUN_INIT:!0}},e.exports)(window,document),c=function(){var e={};function h(e){if(!("string"==typeof e||e instanceof String)){var t=typeof e;throw null===e?t="null":"object"===t&&(t=e.constructor.name),new TypeError("Expected a string but received a "+t)}}function v(e,t){var n,r;h(e),r="object"==typeof t?(n=t.min||0,t.max):(n=t,arguments[2]);var a=encodeURI(e).split(/%..|./).length-1;return n<=a&&(void 0===r||a<=r)}function m(e,t){for(var n in void 0===e&&(e={}),t)void 0===e[n]&&(e[n]=t[n]);return e}var g={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1},t="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",n="("+t+"[.]){3}"+t,r=new RegExp("^"+n+"$"),a="(?:[0-9a-fA-F]{1,4})",i=new RegExp("^((?:"+a+":){7}(?:"+a+"|:)|(?:"+a+":){6}(?:"+n+"|:"+a+"|:)|(?:"+a+":){5}(?::"+n+"|(:"+a+"){1,2}|:)|(?:"+a+":){4}(?:(:"+a+"){0,1}:"+n+"|(:"+a+"){1,3}|:)|(?:"+a+":){3}(?:(:"+a+"){0,2}:"+n+"|(:"+a+"){1,4}|:)|(?:"+a+":){2}(?:(:"+a+"){0,3}:"+n+"|(:"+a+"){1,5}|:)|(?:"+a+":){1}(?:(:"+a+"){0,4}:"+n+"|(:"+a+"){1,6}|:)|(?::((?::"+a+"){0,5}:"+n+"|(?::"+a+"){1,7}|:)))(%[0-9a-zA-Z-.:]{1,})?$");function b(e,t){return void 0===t&&(t=""),h(e),(t=String(t))?"4"===t?r.test(e):"6"===t&&i.test(e):b(e,4)||b(e,6)}var w={allow_display_name:!1,allow_underscores:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0,blacklisted_chars:"",ignore_max_length:!1,host_blacklist:[],host_whitelist:[]},y=/^([^\x00-\x1F\x7F-\x9F\cX]+)</i,_=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i,x=/^[a-z\d]+$/,E=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i,A=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A1-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i,C=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i,S=254;function o(e,t){if(h(e),(t=m(t,w)).require_display_name||t.allow_display_name){var n=e.match(y);if(n){var r=n[1];if(e=e.replace(r,"").replace(/(^<|>$)/g,""),r.endsWith(" ")&&(r=r.slice(0,-1)),!function(e){var t=e.replace(/^"(.+)"$/,"$1");if(!t.trim())return!1;if(/[\.";<>]/.test(t)){if(t===e)return!1;if(t.split('"').length!==t.split('\\"').length)return!1}return!0}(r))return!1}else if(t.require_display_name)return!1}if(!t.ignore_max_length&&e.length>S)return!1;var a=e.split("@"),i=a.pop(),o=i.toLowerCase();if(t.host_blacklist.includes(o))return!1;if(0<t.host_whitelist.length&&!t.host_whitelist.includes(o))return!1;var c=a.join("@");if(t.domain_specific_validation&&("gmail.com"===o||"googlemail.com"===o)){var u=(c=c.toLowerCase()).split("+")[0];if(!v(u.replace(/\./g,""),{min:6,max:30}))return!1;for(var l=u.split("."),s=0;s<l.length;s++)if(!x.test(l[s]))return!1}if(!(!1!==t.ignore_max_length||v(c,{max:64})&&v(i,{max:254})))return!1;if(!function(e,t){h(e),(t=m(t,g)).allow_trailing_dot&&"."===e[e.length-1]&&(e=e.substring(0,e.length-1)),!0===t.allow_wildcard&&0===e.indexOf("*.")&&(e=e.substring(2));var n=e.split("."),r=n[n.length-1];if(t.require_tld){if(n.length<2)return!1;if(!t.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(r))return!1;if(/\s/.test(r))return!1}return!(!t.allow_numeric_tld&&/^\d+$/.test(r))&&n.every(function(e){return!(63<e.length&&!t.ignore_max_length||!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(e)||/[\uff01-\uff5e]/.test(e)||/^-|-$/.test(e)||!t.allow_underscores&&/_/.test(e))})}(i,{require_tld:t.require_tld,ignore_max_length:t.ignore_max_length,allow_underscores:t.allow_underscores})){if(!t.allow_ip_domain)return!1;if(!b(i)){if(!i.startsWith("[")||!i.endsWith("]"))return!1;var f=i.slice(1,-1);if(0===f.length||!b(f))return!1}}if('"'===c[0])return c=c.slice(1,c.length-1),t.allow_utf8_local_part?C.test(c):E.test(c);for(var d=t.allow_utf8_local_part?A:_,p=(l=c.split("."),0);p<l.length;p++)if(!d.test(l[p]))return!1;return!t.blacklisted_chars||-1===c.search(new RegExp("["+t.blacklisted_chars+"]+","g"))}return e.exports=function(e,t){return{ulpRequiredFunction:function(e,t){return!t||!!e.value},ulpEmailValidationFunction:function(e,t){return!t||!e.value||!!o(e.value)},ulpPatternCheckFunction:function(e,t){return!t||!e.value||function(e){if("password"===e.name)return!0;var t=e.getAttribute("pattern");return!t||null!==e.value.match(t)}(e)}}},e.exports}()(window,document),u={exports:function(e,t){for(var i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",s=new Uint8Array(256),o=0;o<i.length;o++)s[i.charCodeAt(o)]=o;function c(e){var t,n=new Uint8Array(e),r=n.length,a="";for(t=0;t<r;t+=3)a+=i[n[t]>>2],a+=i[(3&n[t])<<4|n[t+1]>>4],a+=i[(15&n[t+1])<<2|n[t+2]>>6],a+=i[63&n[t+2]];return r%3==2?a=a.substring(0,a.length-1):r%3==1&&(a=a.substring(0,a.length-2)),a}function n(){return navigator&&navigator.credentials&&"undefined"!=typeof PublicKeyCredential}return{base64URLEncode:c,base64URLDecode:function(e){var t,n,r,a,i,o=.75*e.length,c=e.length,u=0,l=new Uint8Array(o);for(t=0;t<c;t+=4)n=s[e.charCodeAt(t)],r=s[e.charCodeAt(t+1)],a=s[e.charCodeAt(t+2)],i=s[e.charCodeAt(t+3)],l[u++]=n<<2|r>>4,l[u++]=(15&r)<<4|a>>2,l[u++]=(3&a)<<6|63&i;return l.buffer},publicKeyCredentialToJSON:function e(t){if(t instanceof Array){var n=[];for(o=0;o<t.length;o+=1)n.push(e(t[o]));return n}if(t instanceof ArrayBuffer)return c(t);if(t instanceof Object){var r={};for(var a in t)r[a]=e(t[a]);return r}return t},str2ab:function(e){for(var t=new ArrayBuffer(e.length),n=new Uint8Array(t),r=0,a=e.length;r<a;r++)n[r]=e.charCodeAt(r);return t},isWebAuthnAvailable:n,isWebauthnPlatformAuthenticatorAvailableAsync:function(e){return n()?e(1e3,PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()):Promise.resolve(!1)}}}}.exports(window,document);({exports:function(e,r,a,t){var n=e(".cd4da03e9"),i=e("#alert-trigger"),o=e(".c4672eae6"),c=e(".c2622867e"),u=!1;i&&c&&n&&t(n,function(e){var t=e.target===i,n=c.contains(e.target);return t&&!u?(r(o,"show"),void(u=!0)):t&&u||u&&!n?(a(o,"show"),void(u=!1)):void 0})}}).exports(o.querySelector,o.addClass,o.removeClass,o.addClickListener),(C="recaptcha_v2",S="recaptcha_enterprise",k="hcaptcha",F="friendly_captcha",T="arkose",P="auth0_v2",(t={}).exports=function(i,o,a,c,u,l,s,f,e){if(!e.enable_ulp_rtl_support){var d=500,p=3,h=0,v=a("div[data-captcha-sitekey]"),m=a(".ulp-captcha-client-error"),t=a("div[data-captcha-sitekey] input");v&&function(){var e="captchaCallback_"+Math.floor(1000001*Math.random()),t=y(),n={async:!0,defer:!0},r=function(e,t,n,r){switch(y()){case C:return"https://www.recaptcha.net/recaptcha/api.js?render=explicit&hl="+e+"&onload="+t;case S:return"https://www.recaptcha.net/recaptcha/enterprise.js?render=explicit&hl="+e+"&onload="+t;case k:return"https://js.hcaptcha.com/1/api.js?render=explicit&hl="+e+"&onload="+t;case F:return"https://cdn.jsdelivr.net/npm/friendly-challenge@0.9.14/widget.min.js";case T:return"https://"+n+".arkoselabs.com/v2/"+r+"/api.js";case P:return"https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit&onload="+t}}(w(),e,v.getAttribute("data-captcha-client-subdomain"),_());if(t===T||t===P){n["data-callback"]=e,n.onerror=function(){if(h<p)return o(r),i(r,n),void h++;o(r),E("BYPASS_CAPTCHA")};var a=function(e){var t,n;t=e,n=function(e){setTimeout(function(){t.run()},d),e.preventDefault()},x().addEventListener("submit",n),t.setConfig({onCompleted:function(e){E(e.token),x().submit()},onError:function(e){return fetch("https://status.arkoselabs.com/api/v2/status.json").then(function(e){return e.json()}).then(function(e){var t=e.status.indicator;return"none"===t}).catch(function(e){return!1}).then(function(e){if(e&&h<p)return t.reset(),new Promise(function(e){setTimeout(function(){e(t.run())},d),h++});E("BYPASS_CAPTCHA"),x().removeEventListener("submit",n)})}})};t===P&&(a=function(){A()}),window[e]=a}else window[e]=function(){delete window[e],A()},t===F&&(c(b(),"frc-captcha"),s(b(),"data-sitekey",_()),n.onload=window[e]);i(r,n)}()}function g(){switch(y()){case C:return window.grecaptcha;case S:return window.grecaptcha.enterprise;case k:return window.hcaptcha;case F:return window.friendlyChallenge;case T:return window.arkose;case P:return window.turnstile}}function b(){return a(function(){switch(y()){case C:case S:return"#ulp-recaptcha";case k:return"#ulp-hcaptcha";case F:return"#ulp-friendly-captcha";case T:return"#ulp-arkose";case P:return"#ulp-auth0-v2-captcha"}}())}function w(){return v.getAttribute("data-captcha-lang")}function y(){return v.getAttribute("data-captcha-provider")}function _(){return v.getAttribute("data-captcha-sitekey")}function x(){return a('form[data-form-primary="true"]')}function E(e){return t.value=e}function A(){var e=g(),t=l("--ulp-captcha-widget-theme")||"light";if(y()===F)"dark"===t&&c(a(".frc-captcha"),"dark"),(r=e.autoWidget).opts.language=w(),r.opts.doneCallback=function(e){E(e)};else{var n={sitekey:_(),theme:t,"expired-callback":function(){E(""),c(v,"c996d3024"),e.reset(r)},callback:function(e){E(e),u(v,"c996d3024")}};y()===P&&(n.language=w(),n.retry="never",n.size="flexible",n["response-field"]=!1,n["error-callback"]=function(e){return console.error("ERROR: Auth Challenge Error Code",e),E(""),h<p?(h++,g().reset(r)):(m.innerHTML=m.innerHTML.replace("#{errorCode}",f(e)),c(v,"c996d3024"),u(m,"hide")),!0});var r=e.render(b(),n)}}},t.exports)(o.loadScript,o.removeScript,o.querySelector,o.addClass,o.removeClass,o.getCSSVariable,o.setAttribute,o.htmlEncode,i),(L="recaptcha_v2",N="recaptcha_enterprise",j="hcaptcha",q="friendly_captcha",R="arkose",O="auth0_v2",(n={}).exports=function(i,o,a,c,u,l,s,f,e){if(e.enable_ulp_rtl_support){var d=500,p=3,h=0,v=a("div[data-captcha-sitekey]"),m=a(".ulp-captcha-client-error"),t=a("div[data-captcha-sitekey] input");v&&function(){var e="captchaCallback_"+Math.floor(1000001*Math.random()),t=y(),n={async:!0,defer:!0},r=function(e,t,n,r){switch(y()){case L:return"https://www.recaptcha.net/recaptcha/api.js?render=explicit&hl="+e+"&onload="+t;case N:return"https://www.recaptcha.net/recaptcha/enterprise.js?render=explicit&hl="+e+"&onload="+t;case j:return"https://js.hcaptcha.com/1/api.js?render=explicit&hl="+e+"&onload="+t;case q:return"https://cdn.jsdelivr.net/npm/friendly-challenge@0.9.14/widget.min.js";case R:return"https://"+n+".arkoselabs.com/v2/"+r+"/api.js";case O:return"https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit&onload="+t}}(w(),e,v.getAttribute("data-captcha-client-subdomain"),_());if(t===R||t===O){n["data-callback"]=e,n.onerror=function(){if(h<p)return o(r),i(r,n),void h++;o(r),E("BYPASS_CAPTCHA")};var a=function(e){var t,n;t=e,n=function(e){setTimeout(function(){t.run()},d),e.preventDefault()},x().addEventListener("submit",n),t.setConfig({language:w(),onCompleted:function(e){E(e.token),x().submit()},onError:function(e){return fetch("https://status.arkoselabs.com/api/v2/status.json").then(function(e){return e.json()}).then(function(e){var t=e.status.indicator;return"none"===t}).catch(function(e){return!1}).then(function(e){if(e&&h<p)return t.reset(),new Promise(function(e){setTimeout(function(){e(t.run())},d),h++});E("BYPASS_CAPTCHA"),x().removeEventListener("submit",n)})}})};t===O&&(a=function(){A()}),window[e]=a}else window[e]=function(){delete window[e],A()},t===q&&(c(b(),"frc-captcha"),s(b(),"data-sitekey",_()),s(b(),"data-lang",w()),n.onload=window[e]);i(r,n)}()}function g(){switch(y()){case L:return window.grecaptcha;case N:return window.grecaptcha.enterprise;case j:return window.hcaptcha;case q:return window.friendlyChallenge;case R:return window.arkose;case O:return window.turnstile}}function b(){return a(function(){switch(y()){case L:case N:return"#ulp-recaptcha";case j:return"#ulp-hcaptcha";case q:return"#ulp-friendly-captcha";case R:return"#ulp-arkose";case O:return"#ulp-auth0-v2-captcha"}}())}function w(){return v.getAttribute("data-captcha-lang")}function y(){return v.getAttribute("data-captcha-provider")}function _(){return v.getAttribute("data-captcha-sitekey")}function x(){return a('form[data-form-primary="true"]')}function E(e){return t.value=e}function A(){var e=g(),t=l("--ulp-captcha-widget-theme")||"light";if(y()===q)"dark"===t&&c(a(".frc-captcha"),"dark"),(r=e.autoWidget).opts.language=w(),r.opts.doneCallback=function(e){E(e)};else{var n={sitekey:_(),theme:t,"expired-callback":function(){E(""),c(v,"c996d3024"),e.reset(r)},callback:function(e){E(e),u(v,"c996d3024")}};y()===O&&(n.language=w(),n.retry="never",n.size="flexible",n["response-field"]=!1,n["error-callback"]=function(e){return console.error("ERROR: Auth Challenge Error Code",e),E(""),h<p?(h++,g().reset(r)):(m.innerHTML=m.innerHTML.replace("#{errorCode}",f(e)),c(v,"c996d3024"),u(m,"hide")),!0});var r=e.render(b(),n)}}},n.exports)(o.loadScript,o.removeScript,o.querySelector,o.addClass,o.removeClass,o.getCSSVariable,o.setAttribute,o.htmlEncode,i),((r={}).exports=function(r,e,a,i,o,c,u,l,n,s,t){if(!t.enable_ulp_wcag_compliance){if(r("body._simple-labels"))return e(".c7e19a173.no-js").forEach(function(e){o(e,"no-js")}),void e(".c7e19a173.js-required").forEach(function(e){i(e,"hide")});e(".c64610e35:not(.c194cc8a2):not(disabled)").forEach(function(e){i(e,"cd93cac30");var t,n=r(e,".input");n.value&&i(e,"c35971c3e"),a(e,"change",f),a(n,"blur",f),a(n,"animationstart",d),t=n,u(function(){t.value&&l(t,"change",!0)},100)})}function f(e){var t=e.target,n=c(t);t.value||s(t,"data-autofilled")?i(n,"c35971c3e"):o(n,"c35971c3e")}function d(e){var t=e.target;"onAutoFillStart"===e.animationName&&(n(t,"data-autofilled",!0),l(e.target,"change",!0),a(t,"keyup",p,{once:!0}))}function p(e){var t=e.target;n(t,"data-autofilled","")}},r.exports)(o.querySelector,o.querySelectorAll,o.addEventListener,o.addClass,o.removeClass,o.getParent,o.setTimeout,o.dispatchEvent,o.setAttribute,o.getAttribute,i),{exports:function(e,t,r,a,i,o,c,u){function n(e){var t=r("submitted"),n=i("submittedForm");a("submitted",!0),o("submittedForm",e.currentTarget),t&&n&&n===e.currentTarget?c(e):"apple"===u(e.target,"data-provider")&&setTimeout(function(){a("submitted",!1)},2e3)}var l=e("form");l&&l.forEach(function(e){t(e,"submit",n)})}}.exports(o.querySelectorAll,o.addEventListener,o.getGlobalFlag,o.setGlobalFlag,o.getSubmittedForm,o.setSubmittedForm,o.preventFormSubmit,o.getAttribute),{exports:function(t,e,n){var r=t("form._form-detect-browser-capabilities"),a=t("main.login-id"),i=t("main.signup-with-passkeys"),o=t("div#passkey-detect-browser-capabilities");if(r||a||i||o){var c=e.isWebAuthnAvailable();t("#webauthn-available").value=c?"true":"false",t("#js-available").value="true",navigator.brave?navigator.brave.isBrave().then(function(e){t("#is-brave").value=e,u()}):u()}function u(){c?e.isWebauthnPlatformAuthenticatorAvailableAsync(n).then(function(e){t("#webauthn-platform-available").value=e?"true":"false",r&&r.submit()}).catch(function(e){t("#webauthn-platform-available").value="false",r&&r.submit()}):(t("#webauthn-platform-available").value="false",r&&r.submit())}}}.exports(o.querySelector,u,o.timeoutPromise),function(){var e={};function c(e,n,r,t,a,i){var o="user-initiated-passkey-challenge",c=n("#allow-passkeys");if(c&&c.value){var u=a("#passkey-config-json");if(u){var l=n(".passkey-begin-button"),s=r.isWebauthnPlatformAuthenticatorAvailableAsync(t),f=n("#username"),d=u,p=new AbortController;d.publicKey.challenge=r.base64URLDecode(d.publicKey.challenge),l&&e(l,function(e){l.disabled=!0,p.abort(o),navigator.credentials.get(d).then(function(e){var t=g(e);s.then(function(e){t.isUserVerifyingPlatformAuthenticatorAvailable=e,m(JSON.stringify(t)),h()})}).catch(function(e){v(e)})}),window.PublicKeyCredential&&window.PublicKeyCredential.isConditionalMediationAvailable&&window.PublicKeyCredential.isConditionalMediationAvailable().then(function(e){if(e){var t=Object.assign({},d,{mediation:"conditional",signal:p.signal});navigator.credentials.get(t).then(function(e){f.blur();var t=g(e);s.then(function(e){t.isUserVerifyingPlatformAuthenticatorAvailable=e,m(JSON.stringify(t)),h()})}).catch(function(e){e!==o&&"AbortError"!==e.name&&i("autofill UI error",e)})}}).catch(function(){})}}function h(){n("form._form-passkey-challenge").submit()}function v(e){var t;n("#action").value="showError::"+(t=e,JSON.stringify({name:t.name,message:t.message,stack:t.stack})),n("form._form-passkey-challenge").submit()}function m(e){n("#passkey").value=e}function g(e){var t={id:e.id,rawId:r.base64URLEncode(e.rawId),type:e.type,response:{clientDataJSON:r.base64URLEncode(e.response.clientDataJSON),authenticatorData:r.base64URLEncode(e.response.authenticatorData),signature:r.base64URLEncode(e.response.signature),userHandle:e.response.userHandle?r.base64URLEncode(e.response.userHandle):null}};return e.authenticatorAttachment&&(t.authenticatorAttachment=e.authenticatorAttachment),t}}return e.exports=function(e,t,n,r,a,i,o){return e(window,"load",function(e){c(t,n,r,a,i,o)}),{configurePasskeys:c}},e.exports}()(o.addEventListener,o.addClickListener,o.querySelector,u,o.timeoutPromise,o.getConfigJson,o.consoleWarn),{exports:function(t,e,n){var r=e(".passkey-hidden-ui"),a=navigator.userAgent.toLowerCase(),i=!!window.safari||a.match(/safari/)&&!a.match(/chrome/)&&!a.match(/chromium/);!r||r.length<=0||(i?r.forEach(function(e){t(e,"passkey-hidden-ui")}):window.PublicKeyCredential&&window.PublicKeyCredential.isConditionalMediationAvailable&&window.PublicKeyCredential.isConditionalMediationAvailable().then(function(e){e?r.forEach(function(e){n(e)}):r.forEach(function(e){t(e,"passkey-hidden-ui")})}).catch(function(e){r.forEach(function(e){t(e,"passkey-hidden-ui")})}))}}.exports(o.removeClass,o.querySelectorAll,o.removeElement),{exports:function(n,e,r,a,i,o,c,u,l,t,s,f){if(f.enable_ulp_wcag_compliance){var d=e("[id^='ulp-container-']");if(d&&d.length){var p=t(_);if(p)for(var h=0;h<d.length;h++)p.observe(d[h],{childList:!0,subtree:!0})}_()}function v(e){var t=e.target,n=o(t);t.value||u(t,"data-autofilled")?a(n,"c35971c3e"):i(n,"c35971c3e")}function m(e){var t=e.target,n=o(t);a(n,"focus"),y(t,n)}function g(e){var t=e.target,n=o(t);i(n,"focus"),v(e),y(t,n)}function b(e){var t=e.target;l(t,"data-autofilled","")}function w(e){var t=e.target;"onAutoFillStart"===e.animationName&&(l(t,"data-autofilled",!0),dispatchEvent(e.target,"change",!0),r(t,"keyup",b,{once:!0}))}function y(e,t){e.value?a(t,"c35971c3e"):i(t,"c35971c3e")}function _(){e(".ulp-field").forEach(function(e){if(!c(e,"cd93cac30")){var t=n(e,s);t&&(a(e,"cd93cac30"),y(t,e),setTimeout(function(){y(t,e)},50),t===document.activeElement&&a(e,"focus"),r(t,"change",v),r(t,"focus",m),r(t,"blur",g),r(t,"animationstart",w))}})}}}.exports(o.querySelector,o.querySelectorAll,o.addEventListener,o.addClass,o.removeClass,o.getParent,o.hasClass,o.getAttribute,o.setAttribute,o.createMutationObserver,o.ELEMENT_TYPE_SELECTOR,i),{exports:function(n,e,r,a,i,o,c,t,u,l){if(!l.enable_ulp_wcag_compliance){var s=e("[id^='ulp-container-']");if(s&&s.length){var f=t(g);if(f)for(var d=0;d<s.length;d++)f.observe(s[d],{childList:!0,subtree:!0});g()}}function p(e){var t=e.target,n=o(t);t.value?a(n,"c35971c3e"):i(n,"c35971c3e")}function h(e){var t=e.target,n=o(t);a(n,"focus"),m(t,n)}function v(e){var t=e.target,n=o(t);i(n,"focus"),m(t,n)}function m(e,t){e.value?a(t,"c35971c3e"):i(t,"c35971c3e")}function g(){e("[id^='ulp-container-'] .ulp-field").forEach(function(e){if(!c(e,"cd93cac30")){var t=n(e,u);t&&(a(e,"cd93cac30"),m(t,e),setTimeout(function(){m(t,e)},50),t===document.activeElement&&a(e,"focus"),r(t,"change",p),r(t,"focus",h),r(t,"blur",v))}})}}}.exports(o.querySelector,o.querySelectorAll,o.addEventListener,o.addClass,o.removeClass,o.getParent,o.hasClass,o.createMutationObserver,o.ELEMENT_TYPE_SELECTOR,i),{exports:function(r,o,a,i,c,u,l,s,f,d,p,h,t,v,m,e,n,g){if(g.enable_ulp_wcag_compliance){var b=!1,w=e+',input[type="checkbox"]';return k(),[w,y,_,x,E,A,C,S,k]}function y(e){var t=c(e,"data-ulp-validation-function"),n=i(e);return{functionName:t,element:r(n,w),parent:n}}function _(e){var a=[],i=[];return o(e,"[data-ulp-validation-function]").forEach(function(e){var t=y(e),n=[];if(t.element){if("input"===t.element.tagName.toLowerCase()){var r=c(t.element,"type");"checkbox"!==r&&-1===m.indexOf(r)&&n.push("Unsupported input type: "+r)}}else n.push("Could not find element");h[t.functionName]||n.push("Could not find function with name: "+t.functionName),n.length?i=i.concat(n):a.push(e)}),i.length&&t(i.join("\r\n")),a}function x(e,t,n){var r=y(e),a=(0,h[r.functionName])(r.element,t,n);a?s(e,"ulp-validator-error")&&(d(e,"ulp-validator-error"),l(e,"data-is-error")):s(e,"ulp-validator-error")||(f(e,"ulp-validator-error"),u(e,"data-is-error",!0));var i=o(r.parent,".ulp-validator-error");return p(r.parent,"ulp-error",!!i.length),a}function E(t){var n=y(t),e=(c(t,"data-ulp-validation-event-listeners")||"").replace(/\s/g,"").split(",").filter(function(e){return!!e});e.length&&e.forEach(function(e){a(n.element,e,function(){x(t,b,e)})})}function A(e,t,n){b=!0;var r=n.filter(function(e){return!x(e,b,"submit")});if(!r.length)return t.submitter&&"default"==c(t.submitter,"value")&&u(t.submitter,"disabled",!0),void e.submit();v(t);var a=y(r[0]);a.element.focus({preventScroll:!0}),a.parent.scrollIntoView({behavior:"smooth"})}function C(){var t=r('form[data-form-primary="true"]'),n=_(t);0!==n.length&&(n.forEach(function(e){E(e)}),a(t,"submit",function(e){A(t,e,n)}))}function S(){if(n)for(var e in n)n.hasOwnProperty(e)&&(h[e]=n[e])}function k(){var e=r("form[data-disable-html-validations]");e&&(S(),u(e,"novalidate",""),C())}}}.exports(o.querySelector,o.querySelectorAll,o.addEventListener,o.getParent,o.getAttribute,o.setAttribute,o.removeAttribute,o.hasClass,o.addClass,o.removeClass,o.toggleClass,o.globalWindow,o.consoleWarn,o.preventFormSubmit,o.SUPPORTED_INPUT_TYPES,o.ELEMENT_TYPE_SELECTOR,c,i),{exports:function(r,o,a,i,c,u,l,t,s,f,e,n){if(!n.enable_ulp_wcag_compliance){var d=!1,p=e+',input[type="checkbox"]';return y(),[p,h,v,m,g,b,w,y]}function h(e){var t=c(e,"data-ulp-validation-function"),n=i(e);return{functionName:t,element:r(n,p),parent:n}}function v(e){var a=[],i=[];return o(e,"[data-ulp-validation-function]").forEach(function(e){var t=h(e),n=[];if(t.element){if("input"===t.element.tagName.toLowerCase()){var r=c(t.element,"type");"checkbox"!==r&&-1===f.indexOf(r)&&n.push("Unsupported input type: "+r)}}else n.push("Could not find element");l[t.functionName]||n.push("Could not find function with name: "+t.functionName),n.length?i=i.concat(n):a.push(e)}),i.length&&t(i.join("\r\n")),a}function m(e,t,n){var r=h(e),a=(0,l[r.functionName])(r.element,t,n);u(e,"ulp-validator-error",!a);var i=o(r.parent,".ulp-validator-error");return u(r.parent,"ulp-error",!!i.length),a}function g(t){var n=h(t),e=(c(t,"data-ulp-validation-event-listeners")||"").replace(/\s/g,"").split(",").filter(function(e){return!!e});e.length&&e.forEach(function(e){a(n.element,e,function(){m(t,d,e)})})}function b(e,t,n){d=!0;var r=n.filter(function(e){return!m(e,d,"submit")});if(r.length){s(t);var a=h(r[0]);a.element.focus({preventScroll:!0}),a.parent.scrollIntoView({behavior:"smooth"})}else e.submit()}function w(){var t=r('form[data-form-primary="true"]'),n=v(t);0!==n.length&&(n.forEach(function(e){g(e)}),a(t,"submit",function(e){b(t,e,n)}))}function y(){var e=o("[id^='ulp-container-']");e&&e.length&&w()}}}.exports(o.querySelector,o.querySelectorAll,o.addEventListener,o.getParent,o.getAttribute,o.toggleClass,o.globalWindow,o.consoleWarn,o.preventFormSubmit,o.SUPPORTED_INPUT_TYPES,o.ELEMENT_TYPE_SELECTOR,i),{exports:function(e,t,n){function r(n){t(n,"click",function(e){e.preventDefault();var t=document.createElement("input");t.name="action",t.type="hidden",t.value=n.value,n.form.appendChild(t),n.form.submit(),n.form.removeChild(t)})}function a(){e('form button[type="submit"][formnovalidate]').forEach(function(e){r(e)})}return n&&a(),[a,r]}}.exports(o.querySelectorAll,o.addEventListener,o.RUN_INIT),((a={}).exports=function(o,e,c,u,l,s,t,f,n){if(n.enable_ulp_wcag_compliance){var r=e('[class*="aria-error-check"]');if(r&&r.length){var a=t(function(e){e&&e.length&&e.map(function(e){if(e.target&&c(e.target,"aria-error-check")){var t=o('[id="'+u(e.target,"data-ulp-validation-target")+'"');if(t){var n=u(t,"aria-describedby");u(e.target,"data-is-error")?(r=t,a=n,i=e.target.id,a&&-1!==a.search(i)||l(r,"aria-describedby",a?a+" "+i:i),l(r,"aria-invalid",!0)):function(e,t,n){if(t){var r=f(t,n);r.length?l(e,"aria-describedby",r):(s(e,"aria-invalid"),s(e,"aria-describedby"))}else s(e,"aria-invalid"),s(e,"aria-describedby")}(t,n,e.target.id)}}var r,a,i})});a&&r.map(function(e){a.observe(e,{attributes:!0,attributeFilter:["class","data-is-error"]})})}}},a.exports)(o.querySelector,o.querySelectorAll,o.hasClass,o.getAttribute,o.setAttribute,o.removeAttribute,o.createMutationObserver,o.removeAndTrimString,i)}();
</script><div class="flex flex-col w-96 gap-8"><p class="text-[var(--aug-grey)] text-center"><br></br><a href="https://www.augmentcode.com/terms-of-service/enterprise" class="text-[var(--aug-green)]" target="_blank">Enterprise Terms of Service</a><a href="https://www.augmentcode.com/privacy-policy" class="text-[var(--aug-green)]" target="_blank">Privacy Policy</a>
             By creating
            an account, you agree to the 
            
             and
            
          </p>
          
        </div>
        


      </main>
      
      
      
      
    </div><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/web-vitals.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/surveys.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/dead-clicks-autocapture.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/recorder.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/array/phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW/config.js"></script><script async="" defer="" data-callback="captchaCallback_652825" src="https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit&onload=captchaCallback_652825"></script><iframe allow="join-ad-interest-group" data-tagging-id="AW-16732971011" data-load-time="1753689170861" height="0" width="0" src="https://td.doubleclick.net/td/rul/16732971011?random=1753689170843&cv=11&fst=1753689170843&fmt=3&bg=ffffff&guid=ON&async=1&en=gtag.config&gtm=45je57o0h2v9191852910za200zd9191852910&gcd=13l3l3l3l1l1&dma=0&tag_exp=101509157~102015666~103116026~103200004~103233427~104684208~104684211~104948813&u_w=1920&u_h=1080&url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBSR2tlQ3pFelFGckljcG9pN1Z2S1dENE5yWjR5OUt4MaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEluX0ZXRUJmSWVMaHBfZjNYUXEwbnpEckUxT0k2UFJqo2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g&ref=https%3A%2F%2Fwww.augmentcode.com%2F&hn=www.googleadservices.com&frm=0&tiba=Sign%20up%20-%20Augment%20Code&npa=0&pscdl=noapi&auid=940730522.1753689171&uaa=&uab=&uafvl=&uamb=0&uam=&uap=Windows&uapv=&uaw=0&fledge=1&data=event%3Dgtag.config%3Bsignup%3Dtrue" style="display: none; visibility: hidden;"></iframe><iframe height="0" width="0" style="display: none; visibility: hidden;"></iframe>
    
  

</body>
  </html>