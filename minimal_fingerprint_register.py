#!/usr/bin/env python3
"""
最小指纹伪装注册脚本
仅伪装地理位置，其他保持原样，避免资源加载问题
"""

import time
import random
from logger import logging
from browser_utils import BrowserManager
from augmentcode_register import generate_email_with_timestamp


class MinimalFingerprintRegister:
    """最小指纹伪装注册器 - 仅地理位置伪装"""

    def __init__(self, email: str):
        self.email = email
        self.browser_manager = None
        self.browser = None
        self.tab = None
    
    def init_browser(self):
        """初始化浏览器"""
        try:
            logging.info("🚀 初始化最小指纹伪装浏览器...")
            
            # 使用基础浏览器管理器
            self.browser_manager = BrowserManager()
            self.browser = self.browser_manager.init_browser()
            
            if not self.browser:
                logging.error("❌ 浏览器初始化失败")
                return False
            
            self.tab = self.browser.latest_tab
            
            # 仅注入地理位置伪装
            self._inject_minimal_scripts()
            
            logging.info("✅ 最小指纹伪装浏览器初始化成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 浏览器初始化失败: {e}")
            return False
    
    def _inject_minimal_scripts(self):
        """注入最小指纹伪装脚本 - 仅地理位置"""
        try:
            minimal_script = """
            // 最小指纹伪装 - 仅地理位置和语言
            (function() {
                // 地理位置伪装 - 美国中心
                const mockPosition = {
                    coords: {
                        latitude: 39.8283,
                        longitude: -98.5795,
                        accuracy: 20,
                        altitude: null,
                        altitudeAccuracy: null,
                        heading: null,
                        speed: null
                    },
                    timestamp: Date.now()
                };

                if (navigator.geolocation) {
                    const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
                    const originalWatchPosition = navigator.geolocation.watchPosition;

                    navigator.geolocation.getCurrentPosition = function(success, error, options) {
                        setTimeout(() => success(mockPosition), 100);
                    };

                    navigator.geolocation.watchPosition = function(success, error, options) {
                        setTimeout(() => success(mockPosition), 100);
                        return 1;
                    };
                }

                // 语言和时区伪装
                Object.defineProperty(navigator, 'language', {
                    get: function() { return 'en-US'; }
                });

                Object.defineProperty(navigator, 'languages', {
                    get: function() { return ['en-US', 'en']; }
                });

                console.log('✅ 最小指纹伪装已设置: 地理位置 + 语言');
            })();
            """
            
            self.tab.run_js(minimal_script)
            logging.info("✅ 最小指纹伪装脚本注入成功")
            
        except Exception as e:
            logging.error(f"❌ 最小指纹伪装脚本注入失败: {e}")

    def _handle_terms_agreement(self) -> bool:
        """
        处理条款同意页面 (https://auth.augmentcode.com/terms-accept)

        Returns:
            bool: 是否处理成功
        """
        try:
            logging.info("📋 检查是否跳转到条款同意页面...")

            # 等待页面跳转
            time.sleep(3)

            current_url = self.tab.url
            logging.info(f"🔍 当前 URL: {current_url}")

            # 检查是否在条款同意页面
            if "terms-accept" not in current_url:
                logging.info("ℹ️ 未跳转到条款同意页面，继续流程")
                return True

            logging.info("🔍 检测到条款同意页面 (terms-accept)")

            # 等待页面完全加载
            time.sleep(2)

            # 查找条款复选框
            terms_checkbox = self.tab.ele("#terms-of-service-checkbox", timeout=5)
            if not terms_checkbox:
                logging.warning("⚠️ 未找到条款同意复选框")
                return False

            # 勾选条款复选框
            is_checked = terms_checkbox.attr("checked") is not None
            logging.info(f"📋 条款复选框当前状态: {'已勾选' if is_checked else '未勾选'}")

            if not is_checked:
                logging.info("☑️ 勾选条款同意复选框...")
                terms_checkbox.click()
                time.sleep(1)

            # 点击注册按钮
            signup_button = self.tab.ele("#signup-button", timeout=3)
            if signup_button:
                # 检查按钮是否可用
                disabled_attr = signup_button.attr("disabled")
                is_disabled = disabled_attr is not None
                logging.info(f"🔍 注册按钮状态: {'禁用' if is_disabled else '启用'}")

                if not is_disabled:
                    logging.info("🖱️ 点击注册按钮...")
                    signup_button.click()
                    time.sleep(3)
                    logging.info("✅ 注册按钮点击成功")
                    return True
                else:
                    logging.error("❌ 注册按钮仍然禁用，无法点击")
                    return False
            else:
                logging.error("❌ 未找到注册按钮")
                return False

        except Exception as e:
            logging.error(f"❌ 处理条款同意失败: {e}")
            return False

    def step1_visit_promotion_page(self) -> bool:
        """步骤1: 访问推广页面"""
        try:
            logging.info("🌐 步骤1: 访问AugmentCode推广页面...")
            
            # 访问推广页面
            self.tab.get("https://www.augmentcode.com/resources/cursor")
            time.sleep(3)
            
            # 重新注入脚本（页面跳转后）
            self._inject_minimal_scripts()
            
            # 检查页面是否正确加载
            page_title = self.tab.title
            logging.info(f"📄 页面标题: {page_title}")
            
            if "cursor" in page_title.lower() or "augment" in page_title.lower():
                logging.info("✅ 推广页面访问成功")
                return True
            else:
                logging.warning(f"⚠️ 页面标题异常: {page_title}")
                return True  # 继续尝试
                
        except Exception as e:
            logging.error(f"❌ 访问推广页面失败: {e}")
            return False
    
    def step2_click_register_button(self) -> bool:
        """步骤2: 点击注册按钮"""
        try:
            logging.info("🖱️ 步骤2: 查找并点击注册按钮...")
            
            # 可能的按钮文本
            button_texts = [
                "Get your free month",
                "Claim offer", 
                "Get started",
                "Continue",
                "Sign up"
            ]
            
            button_found = False
            for button_text in button_texts:
                try:
                    logging.info(f"🔍 查找按钮: {button_text}")
                    button = self.tab.ele(f"@text()={button_text}", timeout=3)
                    if button:
                        logging.info(f"✅ 找到按钮: {button_text}")
                        button.click()
                        logging.info("🖱️ 点击按钮成功")
                        button_found = True
                        break
                except Exception as btn_e:
                    logging.debug(f"按钮 '{button_text}' 未找到: {btn_e}")
                    continue
            
            if not button_found:
                # 尝试通过链接查找
                try:
                    link = self.tab.ele("@href*=register", timeout=3)
                    if link:
                        logging.info("✅ 找到注册链接")
                        link.click()
                        button_found = True
                except:
                    pass
            
            if button_found:
                time.sleep(3)
                # 重新注入脚本
                self._inject_minimal_scripts()
                logging.info("✅ 注册按钮点击成功")
                return True
            else:
                logging.error("❌ 未找到注册按钮")
                return False
                
        except Exception as e:
            logging.error(f"❌ 点击注册按钮失败: {e}")
            return False
    
    def step3_handle_turnstile(self) -> bool:
        """步骤3: 处理Turnstile验证"""
        try:
            logging.info("🔐 步骤3: 处理Turnstile验证...")
            
            # 等待Turnstile加载
            time.sleep(5)
            
            # 查找Turnstile iframe
            turnstile_found = False
            try:
                # 查找Turnstile相关元素
                turnstile_elements = [
                    "iframe[src*='turnstile']",
                    "iframe[src*='cloudflare']",
                    ".cf-turnstile",
                    "[data-sitekey]"
                ]
                
                for selector in turnstile_elements:
                    try:
                        element = self.tab.ele(selector, timeout=2)
                        if element:
                            logging.info(f"✅ 找到Turnstile元素: {selector}")
                            turnstile_found = True
                            break
                    except:
                        continue
                
                if turnstile_found:
                    logging.info("⏳ 等待Turnstile自动完成...")
                    # 等待更长时间让Turnstile自动完成
                    time.sleep(10)
                    
                    # 检查是否有继续按钮出现
                    continue_buttons = ["Continue", "Next", "Proceed", "Submit"]
                    for btn_text in continue_buttons:
                        try:
                            btn = self.tab.ele(f"@text()={btn_text}", timeout=2)
                            if btn and btn.states.is_enabled:
                                logging.info(f"✅ Turnstile完成，找到继续按钮: {btn_text}")
                                return True
                        except:
                            continue
                    
                    logging.info("✅ Turnstile处理完成（无需手动操作）")
                    return True
                else:
                    logging.info("ℹ️ 未检测到Turnstile，可能已跳过")
                    return True
                    
            except Exception as turnstile_e:
                logging.warning(f"⚠️ Turnstile检测异常: {turnstile_e}")
                return True  # 继续流程
                
        except Exception as e:
            logging.error(f"❌ Turnstile处理失败: {e}")
            return False
    
    def step4_input_email(self) -> bool:
        """步骤4: 输入邮箱（基于原始cursor_style逻辑）"""
        try:
            logging.info("📧 步骤4: 处理条款同意页面和输入邮箱...")

            # 首先检查是否需要处理条款同意页面
            current_url = self.tab.url
            logging.info(f"🔍 当前 URL: {current_url}")

            if "terms-accept" in current_url:
                logging.info("🔍 检测到条款同意页面，先处理条款同意...")
                if not self._handle_terms_agreement():
                    logging.error("❌ 条款同意处理失败")
                    return False

                # 等待页面跳转
                logging.info("⏳ 等待页面跳转到邮箱输入页面...")
                time.sleep(5)

                # 更新当前 URL
                current_url = self.tab.url
                logging.info(f"🔍 跳转后 URL: {current_url}")

            # 使用传入的邮箱
            email = self.email

            # 查找邮箱输入框（使用原始脚本中成功的选择器）
            email_input = None

            # 尝试多种选择器（按成功概率排序）
            email_selectors = [
                "#username",  # 原始脚本中成功的选择器
                "input[name=username]",
                "input[type=email]",
                "@type=email",
                "@placeholder*=email",
                "@name=email",
                "@id*=email"
            ]

            for selector in email_selectors:
                try:
                    if selector.startswith("#") or selector.startswith("input["):
                        email_input = self.tab.ele(selector, timeout=3)
                    else:
                        email_input = self.tab.ele(selector, timeout=3)

                    if email_input:
                        logging.info(f"✅ 找到邮箱输入框: {selector}")
                        break
                except:
                    continue

            if not email_input:
                logging.error("❌ 未找到邮箱输入框")
                return False

            # 输入邮箱
            email_input.clear()
            email_input.input(email)

            logging.info(f"✅ 邮箱输入成功: {email}")

            # 等待输入完成
            time.sleep(2)

            # 截图记录
            self.tab.get_screenshot(path=f"step4_email_input_{int(time.time())}.png")

            logging.info("✅ 第四步完成：邮箱输入成功")
            return True

        except Exception as e:
            logging.error(f"❌ 邮箱输入失败: {e}")
            return False
    
    def step5_click_continue(self) -> bool:
        """步骤5: 点击继续按钮"""
        try:
            logging.info("➡️ 步骤5: 点击继续按钮...")
            
            continue_buttons = [
                "Continue",
                "Next", 
                "Proceed",
                "Submit",
                "Sign up",
                "Create account"
            ]
            
            for btn_text in continue_buttons:
                try:
                    button = self.tab.ele(f"@text()={btn_text}", timeout=3)
                    if button and button.states.is_enabled:
                        button.click()
                        logging.info(f"✅ 点击继续按钮成功: {btn_text}")
                        time.sleep(3)
                        return True
                except:
                    continue
            
            # 尝试通过类型查找
            try:
                submit_btn = self.tab.ele("input[type='submit']", timeout=3)
                if submit_btn:
                    submit_btn.click()
                    logging.info("✅ 点击提交按钮成功")
                    time.sleep(3)
                    return True
            except:
                pass
            
            logging.error("❌ 未找到继续按钮")
            return False
            
        except Exception as e:
            logging.error(f"❌ 点击继续按钮失败: {e}")
            return False

    def step6_handle_verification_code(self) -> bool:
        """步骤6: 处理验证码"""
        try:
            logging.info("📋 步骤6: 处理验证码...")

            # 等待邮件发送
            logging.info("⏳ 等待验证码邮件发送...")
            time.sleep(10)

            # 获取验证码
            try:
                from augmentcode_register import AugmentCodeRegister
                temp_register = AugmentCodeRegister(self.email)
                verification_code = temp_register.email_handler.get_augmentcode_verification_code(max_retries=5, retry_interval=30)

                if not verification_code:
                    logging.error("❌ 未获取到验证码")
                    return False

                logging.info(f"✅ 获取到验证码: {verification_code}")

            except Exception as e:
                logging.error(f"❌ 获取验证码失败: {e}")
                return False

            # 查找验证码输入框
            code_input = None
            code_selectors = [
                "#code",  # 原始脚本中成功的选择器
                "input[id=code]",
                "input[name=code]",
                "@type=text",
                "@placeholder*=code",
                "@placeholder*=verification",
                "@name*=code",
                "@id*=code"
            ]

            for selector in code_selectors:
                try:
                    if selector.startswith("#") or selector.startswith("input["):
                        code_input = self.tab.ele(selector, timeout=3)
                    else:
                        code_input = self.tab.ele(selector, timeout=3)

                    if code_input:
                        logging.info(f"✅ 找到验证码输入框: {selector}")
                        break
                except:
                    continue

            if not code_input:
                logging.error("❌ 未找到验证码输入框")
                return False

            # 输入验证码
            code_input.clear()
            code_input.input(verification_code)

            logging.info("✅ 验证码输入成功")
            time.sleep(2)

            # 提交验证码
            submit_button = None
            submit_texts = ["Continue", "Submit", "Verify", "Confirm"]

            for text in submit_texts:
                try:
                    submit_button = self.tab.ele(f"@text()={text}", timeout=3)
                    if submit_button:
                        logging.info(f"✅ 找到提交按钮: {text}")
                        break
                except:
                    continue

            if submit_button:
                submit_button.click()
                logging.info("✅ 验证码提交成功")
                time.sleep(5)
            else:
                logging.warning("⚠️ 未找到提交按钮，可能自动提交")

            # 截图记录
            self.tab.get_screenshot(path=f"step6_verification_complete_{int(time.time())}.png")

            logging.info("✅ 步骤6完成：验证码处理成功")
            return True

        except Exception as e:
            logging.error(f"❌ 步骤6失败: {e}")
            return False

    def step7_final_terms_agreement(self) -> bool:
        """步骤7: 最终条款同意（验证码后）"""
        try:
            logging.info("📋 步骤7: 最终条款同意...")

            # 检查当前URL
            current_url = self.tab.url
            logging.info(f"🔍 当前URL: {current_url}")

            # 如果不在terms-accept页面，说明可能已经完成
            if "terms-accept" not in current_url:
                logging.info("ℹ️ 不在条款同意页面，可能已完成注册")
                return True

            logging.info("🔍 检测到最终条款同意页面")

            # 等待页面完全加载
            time.sleep(2)

            # 调用条款同意处理方法
            if self._handle_terms_agreement():
                logging.info("✅ 最终条款同意处理成功")

                # 等待页面跳转
                time.sleep(5)

                # 检查是否跳转成功
                final_url = self.tab.url
                logging.info(f"🔍 处理后URL: {final_url}")

                if "terms-accept" not in final_url:
                    logging.info("🎉 成功跳转，注册流程完成！")
                    return True
                else:
                    logging.warning("⚠️ 仍在条款同意页面，可能需要手动检查")
                    return True  # 保守地认为成功
            else:
                logging.error("❌ 最终条款同意处理失败")
                return False

        except Exception as e:
            logging.error(f"❌ 步骤7失败: {e}")
            return False

    def run_registration(self):
        """运行完整注册流程"""
        try:
            logging.info("🚀 开始最小指纹伪装注册流程...")
            
            # 初始化浏览器
            if not self.init_browser():
                return False
            
            # 执行注册步骤
            steps = [
                ("访问推广页面", self.step1_visit_promotion_page),
                ("点击注册按钮", self.step2_click_register_button),
                ("处理Turnstile验证", self.step3_handle_turnstile),
                ("输入邮箱", self.step4_input_email),
                ("点击继续", self.step5_click_continue),
                ("处理验证码", self.step6_handle_verification_code),
                ("最终条款同意", self.step7_final_terms_agreement)
            ]
            
            for step_name, step_func in steps:
                logging.info(f"\n{'='*50}")
                logging.info(f"执行: {step_name}")
                logging.info('='*50)
                
                if not step_func():
                    logging.error(f"❌ {step_name} 失败，停止流程")
                    return False
                
                logging.info(f"✅ {step_name} 完成")
                time.sleep(2)
            
            logging.info("\n🎉 最小指纹伪装注册流程完成！")
            return True
            
        except Exception as e:
            logging.error(f"❌ 注册流程失败: {e}")
            return False
        finally:
            input("按 Enter 键关闭浏览器...")
            if self.browser_manager:
                self.browser_manager.quit()


def main():
    """主函数"""
    try:
        logging.info("=" * 60)
        logging.info("最小指纹伪装注册测试")
        logging.info("=" * 60)
        
        # 生成邮箱
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱: {email}")

        register = MinimalFingerprintRegister(email)
        success = register.run_registration()
        
        if success:
            logging.info("🎉 最小指纹伪装注册测试成功！")
            print("\n✅ 最小指纹伪装注册完成！")
        else:
            logging.error("😞 最小指纹伪装注册测试失败！")
            print("\n❌ 最小指纹伪装注册失败！")
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 程序执行失败: {e}")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
