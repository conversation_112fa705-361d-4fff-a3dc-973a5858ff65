#!/usr/bin/env python3
"""
使用 Edge 浏览器测试 AugmentCode 注册
"""

import time
import os
from logger import logging

def test_edge_registration():
    """使用 Edge 浏览器测试注册"""
    try:
        logging.info("🌐 使用 Microsoft Edge 浏览器测试注册...")
        
        # 生成测试邮箱
        from augmentcode_register import generate_email_with_timestamp
        test_email = generate_email_with_timestamp()
        
        logging.info(f"📧 测试邮箱: {test_email}")
        
        # 使用 Edge 浏览器初始化注册器
        from cursor_style_register import CursorStyleRegister
        
        register = CursorStyleRegister(email=test_email, browser_type="edge")
        
        # 初始化浏览器
        if not register.init_browser():
            logging.error("❌ Edge 浏览器初始化失败")
            return False
        
        logging.info("✅ Edge 浏览器初始化成功")
        
        # 执行注册流程
        logging.info("🚀 开始注册流程...")
        
        # Step 1: 访问促销页面
        if not register.step1_visit_promotion_page():
            logging.error("❌ Step 1 失败")
            return False

        # Step 2: 点击注册按钮
        if not register.step2_click_register_button():
            logging.error("❌ Step 2 失败")
            return False

        # Step 3: 处理 Turnstile 验证
        if not register.step3_handle_turnstile():
            logging.error("❌ Step 3 失败")
            return False

        # Step 4: 输入邮箱
        if not register.step4_input_email():
            logging.error("❌ Step 4 失败")
            return False

        # Step 5: 点击 Continue
        if not register.step5_click_continue():
            logging.error("❌ Step 5 失败")
            return False

        # Step 6: 处理验证码
        if not register.step6_handle_verification_code():
            logging.error("❌ Step 6 失败")
            return False

        # Step 7: 最终条款同意
        if not register.step7_final_terms_agreement():
            logging.error("❌ Step 7 失败")
            return False
        
        logging.info("🎉 Edge 浏览器注册测试成功！")
        
        # 等待用户查看结果
        input("按 Enter 键关闭浏览器...")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ Edge 注册测试异常: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    
    finally:
        # 清理资源
        try:
            if 'register' in locals() and register.browser_manager:
                register.browser_manager.quit()
        except:
            pass

def main():
    """主函数"""
    logging.info("=" * 80)
    logging.info("🧪 Microsoft Edge 浏览器注册测试")
    logging.info("=" * 80)
    
    # 设置代理（如果需要）
    proxy = os.getenv("BROWSER_PROXY")
    if proxy:
        logging.info(f"🌐 使用代理: {proxy}")
    
    success = test_edge_registration()
    
    if success:
        logging.info("🎉 Edge 浏览器测试完成！")
    else:
        logging.error("❌ Edge 浏览器测试失败！")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
