@echo off
chcp 65001 >nul
title AugmentCode 自动注册工具

echo.
echo ========================================
echo    AugmentCode 自动注册工具
echo ========================================
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 Python，请先安装 Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查是否在正确的目录
if not exist "augmentcode_register.py" (
    echo 错误: 未找到 augmentcode_register.py 文件
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

REM 检查 .env 文件
if not exist ".env" (
    echo 错误: 未找到 .env 配置文件
    echo 请先配置 .env 文件
    pause
    exit /b 1
)

REM 检查 WIPDF.pdf 文件
if not exist "WIPDF.pdf" (
    echo 警告: 未找到 WIPDF.pdf 文件
    echo 请确保项目根目录下有此文件
    pause
)

REM 运行注册程序
echo 启动 AugmentCode 注册工具...
echo.
python run_augmentcode_register.py

REM 等待用户确认
echo.
pause
