#!/usr/bin/env python3
"""
详细调试 Turnstile 验证过程
专门用于分析人机验证的点击和状态变化
"""

import os
import sys
import time
from logger import logging
from augmentcode_register import AugmentCodeRegister, generate_email_with_timestamp

def main():
    """详细调试 Turnstile 验证过程"""
    try:
        logging.info("=" * 80)
        logging.info("详细调试 Turnstile 验证过程")
        logging.info("=" * 80)
        
        # 检查环境配置
        proxy = os.getenv("BROWSER_PROXY", "").strip()
        if proxy:
            logging.info(f"✅ 代理配置: {proxy}")
        else:
            logging.warning("⚠️  未配置代理，建议设置 BROWSER_PROXY=http://127.0.0.1:1080")
        
        # 生成邮箱地址
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱地址: {email}")
        
        # 创建注册器
        register = AugmentCodeRegister(email)
        
        # 初始化浏览器
        logging.info("🚀 初始化浏览器...")
        register.browser_manager = register.browser_manager or register.__class__.__dict__.get('BrowserManager', lambda: None)()
        
        from browser_utils import BrowserManager
        register.browser_manager = BrowserManager()
        
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6876.4 Safari/537.36"
        browser = register.browser_manager.init_browser(user_agent)
        register.tab = browser.latest_tab
        
        # 应用美国地区设置
        register._apply_us_region_settings()
        
        # 执行第一步：访问页面
        logging.info("📋 执行第一步：访问页面...")
        if not register.step1_click_get_free_month():
            logging.error("第一步失败")
            return False
        
        # 专门调试第二步：Turnstile 验证
        logging.info("🔍 开始详细调试 Turnstile 验证...")
        
        # 保存初始状态
        register.save_screenshot("debug_before_turnstile")
        register.save_shadow_root_html("debug_before_turnstile")
        register._log_turnstile_debug_info("调试开始")
        
        # 等待页面稳定
        logging.info("⏳ 等待页面稳定...")
        time.sleep(5)
        
        # 再次检查状态
        register._log_turnstile_debug_info("页面稳定后")
        
        # 执行 Turnstile 验证
        logging.info("🎯 开始执行 Turnstile 验证...")
        success = register.handle_turnstile_verification(max_retries=1)
        
        if success:
            logging.info("🎉 Turnstile 验证成功！")
            
            # 继续执行后续步骤进行完整测试
            logging.info("📋 继续执行后续步骤...")
            
            # 第三步：输入邮箱
            if register.step3_input_email():
                logging.info("✅ 第三步：输入邮箱成功")
                
                # 第四步：点击继续
                if register.step4_click_continue():
                    logging.info("✅ 第四步：点击继续成功")
                    
                    # 第五步：输入验证码
                    if register.step5_input_verification_code():
                        logging.info("✅ 第五步：输入验证码成功")
                        
                        # 第六步：上传文件
                        if register.step6_upload_file():
                            logging.info("🎉 完整注册流程成功！")
                        else:
                            logging.warning("第六步失败，但前面步骤都成功了")
                    else:
                        logging.warning("第五步失败")
                else:
                    logging.warning("第四步失败")
            else:
                logging.warning("第三步失败")
        else:
            logging.error("😞 Turnstile 验证失败！")
            
            # 保存失败状态用于分析
            register.save_screenshot("debug_turnstile_failed")
            register.save_shadow_root_html("debug_turnstile_failed")
            register._log_turnstile_debug_info("验证失败")
        
        # 保持浏览器打开一段时间供手动检查
        logging.info("🔍 保持浏览器打开 30 秒供手动检查...")
        time.sleep(30)
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 调试过程中发生错误: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        # 清理资源
        try:
            if 'register' in locals() and register.browser_manager:
                register.browser_manager.quit()
        except:
            pass

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 Turnstile 调试测试成功！")
        print("✅ 人机验证问题已解决")
        sys.exit(0)
    else:
        print("\n😞 Turnstile 调试测试失败！")
        print("❌ 请查看日志分析问题")
        sys.exit(1)
