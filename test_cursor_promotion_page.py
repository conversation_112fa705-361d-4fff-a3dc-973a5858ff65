#!/usr/bin/env python3
"""
测试 AugmentCode Cursor 促销页面
https://app.augmentcode.com/promotions/cursor
"""

import os
import sys
import time
from logger import logging

def test_cursor_promotion_page():
    """测试 Cursor 促销页面"""
    try:
        logging.info("🧪 测试 AugmentCode Cursor 促销页面...")
        
        # 使用 Cursor 的 BrowserManager
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        logging.info("✅ Cursor BrowserManager 初始化成功")
        
        # 访问 Cursor 资源页面
        logging.info("🌐 访问 Cursor 资源页面...")
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(5)
        
        # 截图记录
        screenshot_path = f"cursor_promotion_page_{int(time.time())}.png"
        tab.get_screenshot(path=screenshot_path)
        logging.info(f"📸 截图已保存: {screenshot_path}")
        
        # 检查页面内容
        page_title = tab.title
        logging.info(f"📄 页面标题: {page_title}")
        
        # 检查是否有注册相关的按钮
        register_buttons = [
            "Get your free month",
            "Sign up",
            "Register", 
            "Start free trial",
            "Claim offer",
            "Get started",
            "Continue"
        ]
        
        found_buttons = []
        for button_text in register_buttons:
            try:
                button = tab.ele(f"@text()={button_text}", timeout=2)
                if button:
                    found_buttons.append(button_text)
                    logging.info(f"✅ 找到按钮: {button_text}")
            except:
                continue
        
        if found_buttons:
            logging.info(f"🎯 找到 {len(found_buttons)} 个可能的注册按钮")
        else:
            logging.warning("⚠️ 未找到明显的注册按钮")
        
        # 检查是否有 Turnstile 验证
        turnstile_selectors = [
            "#ulp-auth0-v2-captcha",
            ".cf-turnstile",
            "[data-sitekey]",
            "iframe[src*='turnstile']"
        ]
        
        found_turnstile = False
        for selector in turnstile_selectors:
            try:
                element = tab.ele(selector, timeout=2)
                if element:
                    logging.info(f"🔍 检测到 Turnstile: {selector}")
                    found_turnstile = True
                    break
            except:
                continue
        
        if not found_turnstile:
            logging.info("ℹ️ 当前页面未检测到 Turnstile 验证")
        
        # 检查是否需要登录
        login_indicators = [
            "login",
            "sign in", 
            "email",
            "password",
            "username"
        ]
        
        page_html = tab.html.lower()
        login_needed = any(indicator in page_html for indicator in login_indicators)
        
        if login_needed:
            logging.info("🔐 页面可能需要登录或注册")
        else:
            logging.info("ℹ️ 页面可能不需要登录")
        
        # 尝试点击第一个找到的按钮
        if found_buttons:
            first_button = found_buttons[0]
            logging.info(f"🖱️ 尝试点击按钮: {first_button}")
            
            try:
                button = tab.ele(f"@text()={first_button}", timeout=5)
                if button:
                    button.click()
                    logging.info(f"✅ 成功点击按钮: {first_button}")
                    
                    # 等待页面跳转
                    time.sleep(5)
                    
                    # 检查跳转后的页面
                    new_url = tab.url
                    new_title = tab.title
                    
                    logging.info(f"🌐 跳转后 URL: {new_url}")
                    logging.info(f"📄 跳转后标题: {new_title}")
                    
                    # 截图记录跳转后的页面
                    after_click_screenshot = f"after_click_{int(time.time())}.png"
                    tab.get_screenshot(path=after_click_screenshot)
                    logging.info(f"📸 跳转后截图: {after_click_screenshot}")
                    
                    # 检查跳转后是否有 Turnstile
                    for selector in turnstile_selectors:
                        try:
                            element = tab.ele(selector, timeout=3)
                            if element:
                                logging.info(f"🔍 跳转后检测到 Turnstile: {selector}")
                                
                                # 如果是 Auth0 V2 Captcha，尝试 Cursor 的方法
                                if selector == "#ulp-auth0-v2-captcha":
                                    return test_auth0_turnstile(tab, element)
                                
                                return True
                        except:
                            continue
                    
                    logging.info("ℹ️ 跳转后未检测到 Turnstile")
                    return True
                    
            except Exception as e:
                logging.error(f"❌ 点击按钮失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试 Cursor 促销页面失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        try:
            browser_manager.quit()
        except:
            pass

def test_auth0_turnstile(tab, auth0_element):
    """测试 Auth0 V2 Captcha（使用 Cursor 方法）"""
    try:
        logging.info("🧪 测试 Auth0 V2 Captcha...")
        
        # 使用 Cursor 的 shadow DOM 访问方法
        try:
            challenge_check = (
                auth0_element
                .child()
                .shadow_root.ele("tag:iframe")
                .ele("tag:body")
                .sr("tag:input")
            )
            
            if challenge_check:
                logging.info("✅ 成功访问 Turnstile shadow DOM")
                
                # 检查验证状态
                challenge_value = challenge_check.attr("value")
                logging.info(f"🔍 Challenge 初始值: {challenge_value}")
                
                if challenge_value:
                    logging.info("🎉 Turnstile 验证已自动完成！")
                    return True
                else:
                    logging.info("⏳ 等待 Turnstile 自动完成...")
                    
                    # 等待自动完成
                    for i in range(30):  # 等待30秒
                        time.sleep(1)
                        try:
                            new_value = challenge_check.attr("value")
                            if new_value and new_value != challenge_value:
                                logging.info(f"🎉 Turnstile 验证自动完成！值: {new_value[:20]}...")
                                return True
                        except:
                            pass
                        
                        if i % 5 == 0:
                            logging.info(f"⏳ 等待验证完成... ({i+1}/30)")
                    
                    logging.warning("⚠️ Turnstile 验证未在30秒内自动完成")
                    return False
            else:
                logging.info("ℹ️ 未找到 challenge input，可能已完成验证")
                return True
                
        except Exception as e:
            logging.info(f"ℹ️ Shadow DOM 访问异常: {e}")
            logging.info("这可能意味着验证已自动完成或不需要验证")
            return True
            
    except Exception as e:
        logging.error(f"❌ Auth0 Turnstile 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logging.info("=" * 80)
    logging.info("AugmentCode Cursor 促销页面测试")
    logging.info("=" * 80)
    
    # 检查环境变量
    proxy = os.getenv("BROWSER_PROXY", "")
    if proxy:
        logging.info(f"🌐 代理配置: {proxy}")
    else:
        logging.warning("⚠️ 未配置代理，建议设置 BROWSER_PROXY=127.0.0.1:1080")
        print("建议先设置代理：set BROWSER_PROXY=127.0.0.1:1080")
    
    # 执行测试
    success = test_cursor_promotion_page()
    
    # 输出结果
    logging.info("\n" + "="*80)
    logging.info("测试结果")
    logging.info("="*80)
    
    if success:
        logging.info("🎉 Cursor 促销页面测试成功！")
        print("\n✅ 测试成功！")
        print("📋 可以使用以下命令进行注册：")
        print("python real_browser_register.py")
    else:
        logging.error("❌ Cursor 促销页面测试失败！")
        print("\n❌ 测试失败！")
        print("💡 建议检查：")
        print("1. 网络连接是否正常")
        print("2. 代理设置是否正确")
        print("3. turnstilePatch 扩展是否存在")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
