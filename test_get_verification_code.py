#!/usr/bin/env python3
"""
测试获取 AugmentCode 验证码
使用真实的邮箱地址: <EMAIL>
"""

import sys
import os
import time
from augmentcode_email_handler import AugmentCodeEmailHandler

def main():
    # 使用生成的邮箱地址
    email = "<EMAIL>"
    
    print(f"正在获取邮箱 {email} 的验证码...")
    
    try:
        # 创建邮件处理器
        handler = AugmentCodeEmailHandler(email)
        
        print("开始获取验证码，最多等待 2 分钟...")
        
        # 获取验证码，最多重试 4 次，每次间隔 30 秒
        verification_code = handler.get_augmentcode_verification_code(
            max_retries=4, 
            retry_interval=30
        )
        
        if verification_code:
            print(f"✅ 成功获取验证码: {verification_code}")
            return verification_code
        else:
            print("❌ 未能获取到验证码")
            return None
            
    except Exception as e:
        print(f"❌ 获取验证码时发生错误: {e}")
        return None

if __name__ == "__main__":
    code = main()
    if code:
        print(f"\n🎉 验证码: {code}")
        # 将验证码写入文件，方便后续使用
        with open("verification_code.txt", "w") as f:
            f.write(code)
        print("验证码已保存到 verification_code.txt 文件")
    else:
        print("\n😞 获取验证码失败")
        sys.exit(1)
