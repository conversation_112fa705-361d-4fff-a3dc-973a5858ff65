#!/usr/bin/env python3
"""
测试修复后的 Turnstile 验证逻辑
"""

import os
import time
from logger import logging
from config import Config
from augmentcode_register import AugmentCodeRegister


def test_turnstile_logic():
    """测试 Turnstile 验证逻辑"""
    print("=" * 60)
    print("测试修复后的 Turnstile 验证逻辑")
    print("=" * 60)
    
    try:
        # 获取邮箱配置
        config = Config()
        
        if config.get_temp_mail() != "null":
            email = f"{config.get_temp_mail()}{config.get_temp_mail_ext()}"
        else:
            imap_config = config.get_imap()
            if imap_config:
                email = imap_config['imap_user']
            else:
                print("❌ 未配置有效的邮箱")
                return False
        
        print(f"✓ 使用邮箱: {email}")
        
        # 创建注册器
        register = AugmentCodeRegister(email)
        print("✓ 注册器创建成功")

        # 手动初始化反检测浏览器（模拟 register 方法中的逻辑）
        print("\n正在初始化反检测浏览器...")
        try:
            from anti_detection_browser import AntiDetectionBrowserManager
            register.browser_manager = AntiDetectionBrowserManager()
            browser = register.browser_manager.init_browser()
            register.tab = browser.latest_tab
            print("✓ 反检测浏览器初始化成功")
        except Exception as e:
            print(f"❌ 反检测浏览器初始化失败: {str(e)}")
            return False

        # 执行第一步：访问页面并点击按钮
        print("\n执行第一步：访问页面并点击 'Get your free month'...")
        if not register.step1_click_get_free_month():
            print("❌ 第一步失败")
            if register.browser_manager:
                register.browser_manager.quit()
            return False

        print("✓ 第一步成功")

        # 执行第二步：处理 Turnstile 验证
        print("\n执行第二步：处理 Turnstile 验证...")
        print("注意：这将使用修复后的 shadow_root 方式")

        if register.step2_handle_captcha():
            print("✓ Turnstile 验证成功！")
            print("✓ 修复生效，验证逻辑工作正常")
            result = True
        else:
            print("❌ Turnstile 验证失败")
            print("可能的原因：")
            print("  1. 网络问题")
            print("  2. Cloudflare 检测到自动化")
            print("  3. 页面结构发生变化")
            result = False

        # 清理资源
        print("\n清理浏览器资源...")
        if register.browser_manager:
            register.browser_manager.quit()
        print("✓ 清理完成")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        logging.error(f"测试出错: {str(e)}")
        return False


def test_shadow_root_logic():
    """测试 shadow_root 逻辑（不实际执行）"""
    print("\n" + "=" * 60)
    print("Shadow Root 逻辑说明")
    print("=" * 60)
    
    print("修复前的问题:")
    print("  ❌ 使用硬编码的 ID: '//*[@id=\"RInW4\"]'")
    print("  ❌ ID 是随机生成的，每次都不同")
    print("  ❌ 无法可靠地找到验证框")
    
    print("\n修复后的解决方案:")
    print("  ✓ 使用 shadow_root 方式:")
    print("    tab.ele('@id=cf-turnstile')")
    print("    .child()")
    print("    .shadow_root.ele('tag:iframe')")
    print("    .ele('tag:body')")
    print("    .sr('tag:input')")
    
    print("\n  ✓ 这种方式的优势:")
    print("    - 不依赖随机生成的 ID")
    print("    - 直接访问 shadow DOM 内部结构")
    print("    - 与 cursor_pro_keep_alive.py 保持一致")
    print("    - 更加稳定和可靠")
    
    print("\n  ✓ 成功检测逻辑:")
    print("    - 检查邮箱输入框: @type=email")
    print("    - 检查继续按钮: Continue")
    print("    - 检查邮箱输入框: @name=email")


def main():
    """主函数"""
    print("AugmentCode Turnstile 验证修复测试")
    print("=" * 60)
    
    # 显示修复说明
    test_shadow_root_logic()
    
    # 询问是否进行实际测试
    print("\n" + "=" * 60)
    choice = input("是否进行实际的 Turnstile 验证测试？(y/n): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        print("\n开始实际测试...")
        success = test_turnstile_logic()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 测试成功！Turnstile 验证修复生效")
            print("现在可以正常使用 AugmentCode 注册工具了")
        else:
            print("❌ 测试失败，可能需要进一步调试")
            print("请查看日志文件和截图了解详细情况")
        print("=" * 60)
    else:
        print("\n跳过实际测试。")
        print("如需测试，请运行完整的注册流程：")
        print("  Windows: run_augmentcode.bat")
        print("  Linux/Mac: ./run_augmentcode.sh")
        print("  手动: python run_augmentcode_register.py")


if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试程序出错: {str(e)}")
        input("\n按回车键退出...")
