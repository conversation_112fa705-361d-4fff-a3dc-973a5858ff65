#!/usr/bin/env python3
"""
测试日语语言设置是否生效
验证 reCAPTCHA 的 hl 参数是否变为 ja
"""

import time
from logger import logging

def test_language_setup():
    """测试语言设置"""
    try:
        logging.info("🧪 测试日语语言设置...")
        
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        logging.info("✅ 浏览器初始化成功")
        
        # 检查浏览器语言设置
        logging.info("🔍 检查浏览器语言设置...")
        
        language_info = tab.run_js("""
            return {
                language: navigator.language,
                languages: navigator.languages,
                userAgent: navigator.userAgent,
                acceptLanguage: document.querySelector('html').getAttribute('lang') || 'not-set'
            };
        """)
        
        logging.info(f"📋 navigator.language: {language_info.get('language')}")
        logging.info(f"📋 navigator.languages: {language_info.get('languages')}")
        logging.info(f"📋 HTML lang: {language_info.get('acceptLanguage')}")
        
        # 访问 AugmentCode 注册页面
        logging.info("🌐 访问 AugmentCode 注册页面...")
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(3)
        
        # 应用日语语言设置
        from cursor_style_register import AugmentCodeRegister
        register = AugmentCodeRegister()
        register.tab = tab
        register._setup_japanese_language()
        
        # 再次检查语言设置
        logging.info("🔍 应用日语设置后检查...")
        
        language_info_after = tab.run_js("""
            return {
                language: navigator.language,
                languages: navigator.languages,
                userAgent: navigator.userAgent
            };
        """)
        
        logging.info(f"📋 设置后 navigator.language: {language_info_after.get('language')}")
        logging.info(f"📋 设置后 navigator.languages: {language_info_after.get('languages')}")
        
        # 点击开始按钮进入注册流程
        logging.info("🖱️ 点击开始按钮...")
        start_button = tab.ele("text:Get started", timeout=5)
        if start_button:
            start_button.click()
            time.sleep(5)
            
            # 检查页面中的 reCAPTCHA URL
            logging.info("🔍 检查页面中的 reCAPTCHA URL...")
            
            # 查找所有包含 recaptcha 的 URL
            recaptcha_urls = tab.run_js("""
                const urls = [];
                
                // 检查 iframe src
                const iframes = document.querySelectorAll('iframe');
                iframes.forEach(iframe => {
                    if (iframe.src && iframe.src.includes('recaptcha')) {
                        urls.push({type: 'iframe', url: iframe.src});
                    }
                });
                
                // 检查 script src
                const scripts = document.querySelectorAll('script');
                scripts.forEach(script => {
                    if (script.src && script.src.includes('recaptcha')) {
                        urls.push({type: 'script', url: script.src});
                    }
                });
                
                // 检查网络请求（如果有的话）
                if (window.performance && window.performance.getEntriesByType) {
                    const resources = window.performance.getEntriesByType('resource');
                    resources.forEach(resource => {
                        if (resource.name.includes('recaptcha')) {
                            urls.push({type: 'resource', url: resource.name});
                        }
                    });
                }
                
                return urls;
            """)
            
            if recaptcha_urls:
                logging.info("🔍 找到 reCAPTCHA 相关 URL:")
                for item in recaptcha_urls:
                    url = item.get('url', '')
                    url_type = item.get('type', 'unknown')
                    logging.info(f"  [{url_type}] {url}")
                    
                    # 检查 hl 参数
                    if 'hl=' in url:
                        if 'hl=ja' in url:
                            logging.info("✅ 发现 hl=ja 参数，日语设置生效！")
                        elif 'hl=zh' in url:
                            logging.warning("⚠️ 仍然是 hl=zh 参数，日语设置未生效")
                        else:
                            # 提取 hl 参数值
                            import re
                            hl_match = re.search(r'hl=([^&]+)', url)
                            if hl_match:
                                hl_value = hl_match.group(1)
                                logging.info(f"🔍 发现 hl={hl_value} 参数")
            else:
                logging.info("ℹ️ 未找到 reCAPTCHA URL，可能还未加载")
            
            # 等待并再次检查
            logging.info("⏳ 等待 reCAPTCHA 加载...")
            time.sleep(5)
            
            # 再次检查
            recaptcha_urls_final = tab.run_js("""
                const urls = [];
                const iframes = document.querySelectorAll('iframe');
                iframes.forEach(iframe => {
                    if (iframe.src && iframe.src.includes('recaptcha')) {
                        urls.push(iframe.src);
                    }
                });
                return urls;
            """)
            
            if recaptcha_urls_final:
                logging.info("🔍 最终检查 reCAPTCHA URL:")
                for url in recaptcha_urls_final:
                    logging.info(f"  {url}")
                    if 'hl=ja' in url:
                        logging.info("🎉 确认：reCAPTCHA 使用日语 (hl=ja)！")
                        return True
                    elif 'hl=' in url:
                        import re
                        hl_match = re.search(r'hl=([^&]+)', url)
                        if hl_match:
                            hl_value = hl_match.group(1)
                            logging.warning(f"⚠️ reCAPTCHA 使用语言: hl={hl_value}")
        
        # 截图记录最终状态
        tab.get_screenshot(path=f"language_test_{int(time.time())}.png")
        
        input("按 Enter 键关闭浏览器...")
        browser_manager.quit()
        return True
        
    except Exception as e:
        logging.error(f"❌ 语言设置测试失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logging.info("=" * 80)
    logging.info("日语语言设置测试")
    logging.info("=" * 80)
    
    success = test_language_setup()
    
    if success:
        logging.info("🎉 语言设置测试完成！")
    else:
        logging.error("❌ 语言设置测试失败！")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
