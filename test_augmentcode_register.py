#!/usr/bin/env python3
"""
测试 AugmentCode 注册流程
使用验证成功的邮箱生成和验证码获取逻辑
"""

import os
import sys
import time
from logger import logging
from augmentcode_register import AugmentCodeRegister, generate_email_with_timestamp

def main():
    """测试 AugmentCode 注册流程"""
    try:
        logging.info("=" * 60)
        logging.info("开始测试 AugmentCode 注册流程")
        logging.info("=" * 60)
        
        # 检查环境配置
        proxy = os.getenv("BROWSER_PROXY", "").strip()
        if proxy:
            logging.info(f"✅ 代理配置: {proxy}")
        else:
            logging.warning("⚠️  未配置代理，建议设置 BROWSER_PROXY=http://127.0.0.1:1080")
        
        domain = os.getenv("DOMAIN", "").strip()
        if domain:
            logging.info(f"✅ 域名配置: {domain}")
        else:
            logging.error("❌ 未配置域名，请设置 DOMAIN")
            return False
        
        temp_mail = os.getenv("TEMP_MAIL", "").strip()
        if temp_mail and temp_mail != "null":
            logging.info(f"✅ 临时邮箱配置: {temp_mail}")
        else:
            logging.error("❌ 未配置临时邮箱，请设置 TEMP_MAIL")
            return False
        
        # 生成邮箱地址（使用验证成功的逻辑）
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱地址: {email}")
        
        # 创建注册器
        register = AugmentCodeRegister(email)
        
        # 执行注册流程
        logging.info("🚀 开始执行注册流程...")
        success = register.register()
        
        if success:
            logging.info("🎉 AugmentCode 注册成功！")
            logging.info(f"📧 注册邮箱: {email}")
        else:
            logging.error("😞 AugmentCode 注册失败！")
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 注册测试成功！")
        sys.exit(0)
    else:
        print("\n😞 注册测试失败！")
        sys.exit(1)
