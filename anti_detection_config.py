#!/usr/bin/env python3
"""
反检测配置管理
"""

import random
import json


class AntiDetectionConfig:
    """反检测配置管理器"""
    
    def __init__(self):
        # 最新的美国用户代理字符串（2025年1月）- 更真实的配置
        self.us_user_agents = [
            # Windows Chrome - 最新版本
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6876.4 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.110 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.139 Safari/537.36",

            # macOS Chrome - 最新版本
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6876.4 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.110 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.139 Safari/537.36",

            # Windows Firefox - 最新版本
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0",

            # Windows Edge - 最新版本
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6876.4 Safari/537.36 Edg/133.0.2792.12",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.110 Safari/537.36 Edg/132.0.2957.107",
        ]
        
        # 美国时区
        self.us_timezones = [
            "America/New_York",      # Eastern Time (EST/EDT)
            "America/Chicago",       # Central Time (CST/CDT)
            "America/Denver",        # Mountain Time (MST/MDT)
            "America/Los_Angeles",   # Pacific Time (PST/PDT)
            "America/Phoenix",       # Arizona Time (MST, no DST)
            "America/Anchorage",     # Alaska Time (AKST/AKDT)
            "Pacific/Honolulu",      # Hawaii Time (HST)
        ]
        
        # 美国常用屏幕分辨率
        self.us_screen_resolutions = [
            {"width": 1920, "height": 1080, "ratio": "16:9"},   # Full HD
            {"width": 1366, "height": 768, "ratio": "16:9"},    # HD
            {"width": 1536, "height": 864, "ratio": "16:9"},    # HD+
            {"width": 1440, "height": 900, "ratio": "16:10"},   # WXGA+
            {"width": 2560, "height": 1440, "ratio": "16:9"},   # QHD
            {"width": 1280, "height": 720, "ratio": "16:9"},    # HD
            {"width": 1600, "height": 900, "ratio": "16:9"},    # HD+
            {"width": 3840, "height": 2160, "ratio": "16:9"},   # 4K UHD
            {"width": 2560, "height": 1600, "ratio": "16:10"},  # WQXGA
        ]
        
        # WebGL 渲染器（美国常用显卡）- 更真实的配置
        self.us_webgl_renderers = [
            # AMD 显卡 - 匹配用户提供的配置
            "Google Inc. (AMD) ANGLE (AMD, AMD Radeon(TM) R5 Graphics Direct3D9Ex vs_3_0 ps_3_0, aticfx64.dll)",
            "Google Inc. (AMD) ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, aticfx64.dll)",
            "Google Inc. (AMD) ANGLE (AMD, AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0, aticfx64.dll)",
            "Google Inc. (AMD) ANGLE (AMD, AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0, aticfx64.dll)",

            # NVIDIA 显卡
            "Google Inc. (NVIDIA) ANGLE (NVIDIA, NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0, nvoglv64.dll)",
            "Google Inc. (NVIDIA) ANGLE (NVIDIA, NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0, nvoglv64.dll)",
            "Google Inc. (NVIDIA) ANGLE (NVIDIA, NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0, nvoglv64.dll)",
            "Google Inc. (NVIDIA) ANGLE (NVIDIA, NVIDIA GeForce RTX 2070 Direct3D11 vs_5_0 ps_5_0, nvoglv64.dll)",

            # Intel 集成显卡
            "Google Inc. (Intel) ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0, igdumdim64.dll)",
            "Google Inc. (Intel) ANGLE (Intel, Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0, igdumdim64.dll)",
        ]
        
        # 美国常用的硬件配置
        self.us_hardware_configs = [
            {"cores": 4, "memory": 8},   # 入门级
            {"cores": 6, "memory": 16},  # 中端
            {"cores": 8, "memory": 16},  # 高端
            {"cores": 8, "memory": 32},  # 专业级
            {"cores": 12, "memory": 32}, # 工作站
            {"cores": 16, "memory": 64}, # 高端工作站
        ]
        
        # 美国常用的操作系统平台
        self.us_platforms = [
            {"platform": "Win32", "weight": 70},      # Windows 主导
            {"platform": "MacIntel", "weight": 25},   # macOS 较多
            {"platform": "Linux x86_64", "weight": 5} # Linux 较少
        ]
        
        # Canvas 指纹噪声参数
        self.canvas_noise_params = {
            "noise_level": 0.1,
            "color_variation": 2,
            "position_variation": 1
        }
        
        # Audio 指纹噪声参数
        self.audio_noise_params = {
            "frequency_variation": 0.001,
            "amplitude_variation": 0.001
        }
    
    def generate_random_fingerprint(self):
        """生成随机的美国用户指纹"""
        # 选择硬件配置
        hardware = random.choice(self.us_hardware_configs)
        
        # 选择平台（按权重）
        platform_choice = random.choices(
            [p["platform"] for p in self.us_platforms],
            weights=[p["weight"] for p in self.us_platforms]
        )[0]
        
        # 选择屏幕分辨率
        resolution = random.choice(self.us_screen_resolutions)
        
        # 生成指纹
        fingerprint = {
            "user_agent": random.choice(self.us_user_agents),
            "language": "en-US,en;q=0.9",
            "languages": ["en-US", "en"],
            "timezone": random.choice(self.us_timezones),
            "screen_width": resolution["width"],
            "screen_height": resolution["height"],
            "screen_ratio": resolution["ratio"],
            "webgl_renderer": random.choice(self.us_webgl_renderers),
            "platform": platform_choice,
            "hardware_concurrency": hardware["cores"],
            "device_memory": hardware["memory"],
            "color_depth": 24,
            "pixel_depth": 24,
            "canvas_noise": self.canvas_noise_params,
            "audio_noise": self.audio_noise_params,
        }
        
        return fingerprint
    
    def get_timezone_offset(self, timezone):
        """获取时区偏移量（分钟）"""
        # 注意：这里使用标准时间偏移，实际应用中可能需要考虑夏令时
        timezone_offsets = {
            "America/New_York": 300,      # UTC-5 (EST) / UTC-4 (EDT)
            "America/Chicago": 360,       # UTC-6 (CST) / UTC-5 (CDT)
            "America/Denver": 420,        # UTC-7 (MST) / UTC-6 (MDT)
            "America/Los_Angeles": 480,   # UTC-8 (PST) / UTC-7 (PDT)
            "America/Phoenix": 420,       # UTC-7 (MST, no DST)
            "America/Anchorage": 540,     # UTC-9 (AKST) / UTC-8 (AKDT)
            "Pacific/Honolulu": 600,      # UTC-10 (HST)
        }
        return timezone_offsets.get(timezone, 300)
    
    def get_chrome_args(self, fingerprint):
        """获取 Chrome 启动参数"""
        args = [
            # 基础反检测参数
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-blink-features=AutomationControlled",
            "--disable-features=VizDisplayCompositor",
            "--disable-ipc-flooding-protection",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-client-side-phishing-detection",
            "--disable-sync",
            "--disable-default-apps",
            "--disable-extensions-file-access-check",
            "--disable-extensions-http-throttling",
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--disable-features=TranslateUI",
            "--disable-component-extensions-with-background-pages",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-field-trial-config",
            "--disable-back-forward-cache",
            "--disable-background-networking",
            "--disable-breakpad",
            "--disable-client-side-phishing-detection",
            "--disable-component-update",
            "--disable-default-apps",
            "--disable-dev-shm-usage",
            "--disable-domain-reliability",
            "--disable-features=AudioServiceOutOfProcess",
            "--disable-hang-monitor",
            "--disable-ipc-flooding-protection",
            "--disable-popup-blocking",
            "--disable-prompt-on-repost",
            "--disable-renderer-backgrounding",
            "--disable-sync",
            "--force-color-profile=srgb",
            "--metrics-recording-only",
            "--no-crash-upload",
            "--no-first-run",
            "--no-pings",
            "--password-store=basic",
            "--use-mock-keychain",
            
            # 语言和地区设置
            f"--lang={fingerprint['language'].split(',')[0]}",
            f"--timezone={fingerprint['timezone']}",
            
            # 窗口大小
            f"--window-size={fingerprint['screen_width']},{fingerprint['screen_height']}",
            
            # 用户代理
            f"--user-agent={fingerprint['user_agent']}",
        ]
        
        return args
    
    def get_prefs(self, fingerprint):
        """获取 Chrome 首选项"""
        prefs = {
            # 基础设置
            "credentials_enable_service": False,
            "profile.password_manager_enabled": False,
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
            
            # 语言设置
            "intl.accept_languages": fingerprint["language"],
            "intl.charset_default": "UTF-8",
            
            # 自动化检测
            "excludeSwitches": ["enable-automation"],
            "useAutomationExtension": False,
            
            # 地理位置（美国）
            "profile.default_content_setting_values.geolocation": 2,
            "profile.content_settings.exceptions.geolocation": {
                "https://*,*": {
                    "last_modified": "13312000000000000",
                    "setting": 2
                }
            },
            
            # 时区设置
            "profile.default_content_setting_values.timezone": fingerprint["timezone"],
        }
        
        return prefs
    
    def export_fingerprint(self, fingerprint, filename="fingerprint.json"):
        """导出指纹配置到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(fingerprint, f, indent=2, ensure_ascii=False)
    
    def import_fingerprint(self, filename="fingerprint.json"):
        """从文件导入指纹配置"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return None
