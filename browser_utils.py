from DrissionPage import ChromiumOptions, Chromium
import sys
import os
import logging
from dotenv import load_dotenv

load_dotenv()


class BrowserManager:
    def __init__(self, browser_type="chrome"):
        """
        初始化浏览器管理器

        Args:
            browser_type: 浏览器类型 ("chrome", "edge", "chromium")
        """
        self.browser = None
        self.browser_type = browser_type.lower()
        self.temp_user_data_dir = None  # 记录临时目录用于清理

    def init_browser(self, user_agent=None):
        """初始化浏览器"""
        co = self._get_browser_options(user_agent)
        self.browser = Chromium(co)
        return self.browser

    def _get_browser_options(self, user_agent=None):
        """获取浏览器配置"""
        co = ChromiumOptions()

        # 设置浏览器路径
        browser_path = self._get_browser_path()
        if browser_path:
            co.set_paths(browser_path=browser_path)
            logging.info(f"🌐 使用浏览器: {self.browser_type} -> {browser_path}")

        try:
            extension_path = self._get_extension_path("turnstilePatch")
            co.add_extension(extension_path)
        except FileNotFoundError as e:
            logging.warning(f"警告: {e}")

        # 🔒 无痕模式和环境隔离配置
        self._setup_incognito_mode(co)

        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")
        proxy = os.getenv("BROWSER_PROXY")
        if proxy:
            co.set_proxy(proxy)

        co.auto_port()
        if user_agent:
            co.set_user_agent(user_agent)

        # 设置日语语言环境（解决 reCAPTCHA 验证问题）
        # 根据用户发现：hl=ja 比 hl=zh 更容易通过验证
        co.set_argument("--lang=ja")
        co.set_argument("--accept-lang=ja-JP,ja;q=0.9,en;q=0.8")
        co.set_pref("intl.accept_languages", "ja-JP,ja,en")

        # 设置地理位置为日本（配合语言设置）
        co.set_pref("profile.default_content_setting_values.geolocation", 1)
        co.set_argument("--disable-geolocation")

        co.headless(
            os.getenv("BROWSER_HEADLESS", "True").lower() == "true"
        )  # 生产环境使用无头模式

        # Mac 系统特殊处理
        if sys.platform == "darwin":
            co.set_argument("--no-sandbox")
            co.set_argument("--disable-gpu")

        return co

    def _setup_incognito_mode(self, co):
        """
        设置无痕模式和环境隔离
        确保每次启动都是干净的环境，不会互相污染
        """
        import tempfile
        import uuid

        logging.info("🔒 配置无痕模式和环境隔离...")

        # 1. 无痕模式（根据浏览器类型选择参数）
        if self.browser_type == "edge":
            co.set_argument("--inprivate")  # Edge 使用 --inprivate
        else:
            co.set_argument("--incognito")  # Chrome/Chromium 使用 --incognito

        # 2. 临时用户数据目录（每次都是全新的）
        temp_dir = tempfile.mkdtemp(prefix=f"browser_{self.browser_type}_{uuid.uuid4().hex[:8]}_")
        self.temp_user_data_dir = temp_dir  # 记录用于后续清理
        co.set_user_data_path(temp_dir)
        logging.info(f"📁 临时用户数据目录: {temp_dir}")

        # 3. 禁用各种持久化功能
        co.set_argument("--no-first-run")  # 跳过首次运行设置
        co.set_argument("--no-default-browser-check")  # 不检查默认浏览器
        co.set_argument("--disable-default-apps")  # 禁用默认应用
        co.set_argument("--disable-background-mode")  # 禁用后台模式
        co.set_argument("--disable-background-timer-throttling")  # 禁用后台定时器限制

        # 4. 禁用缓存和存储
        co.set_argument("--disable-application-cache")  # 禁用应用缓存
        co.set_argument("--disable-local-storage")  # 禁用本地存储
        co.set_argument("--disable-session-storage")  # 禁用会话存储
        co.set_argument("--disable-databases")  # 禁用数据库
        co.set_argument("--disable-web-sql")  # 禁用 Web SQL

        # 5. 禁用同步和登录
        co.set_argument("--disable-sync")  # 禁用同步
        co.set_argument("--disable-signin-scoped-device-id")  # 禁用登录设备ID
        co.set_argument("--disable-signin-frame-client-certs")  # 禁用登录证书

        # 6. 禁用扩展（除了我们需要的）
        co.set_argument("--disable-extensions")  # 先禁用所有扩展
        # 注意：turnstilePatch 扩展会通过 add_extension 单独添加

        # 7. 禁用各种检测和跟踪
        co.set_argument("--disable-features=VizDisplayCompositor")  # 禁用某些检测
        co.set_argument("--disable-ipc-flooding-protection")  # 禁用IPC保护
        co.set_argument("--disable-renderer-backgrounding")  # 禁用渲染器后台化
        co.set_argument("--disable-backgrounding-occluded-windows")  # 禁用窗口遮挡检测

        # 8. 内存和性能优化（避免留下痕迹）
        co.set_argument("--memory-pressure-off")  # 关闭内存压力检测
        co.set_argument("--max_old_space_size=4096")  # 限制内存使用

        # 9. 禁用网络相关的持久化
        co.set_argument("--disable-background-networking")  # 禁用后台网络
        co.set_argument("--disable-domain-reliability")  # 禁用域名可靠性报告
        co.set_argument("--disable-component-update")  # 禁用组件更新

        # 10. 安全和隐私设置
        co.set_argument("--disable-client-side-phishing-detection")  # 禁用钓鱼检测
        co.set_argument("--disable-component-extensions-with-background-pages")  # 禁用后台扩展
        co.set_argument("--disable-permissions-api")  # 禁用权限API

        # 11. 设置临时下载目录
        temp_download_dir = os.path.join(temp_dir, "downloads")
        os.makedirs(temp_download_dir, exist_ok=True)
        co.set_pref("download.default_directory", temp_download_dir)

        logging.info("✅ 无痕模式配置完成 - 每次都是全新环境")

    def _get_browser_path(self):
        """根据浏览器类型获取浏览器路径"""
        # 优先使用环境变量指定的路径
        env_path = os.getenv("BROWSER_PATH")
        if env_path:
            return env_path

        # 根据浏览器类型自动检测路径
        if self.browser_type == "edge":
            return self._find_edge_path()
        elif self.browser_type == "chrome":
            return self._find_chrome_path()
        elif self.browser_type == "chromium":
            return self._find_chromium_path()
        else:
            logging.warning(f"⚠️ 未知的浏览器类型: {self.browser_type}")
            return None

    def _find_edge_path(self):
        """查找 Microsoft Edge 浏览器路径"""
        possible_paths = []

        if sys.platform == "win32":
            # Windows 系统的 Edge 路径
            possible_paths = [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                os.path.expanduser(r"~\AppData\Local\Microsoft\Edge\Application\msedge.exe"),
            ]
        elif sys.platform == "darwin":
            # macOS 系统的 Edge 路径
            possible_paths = [
                "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
            ]
        else:
            # Linux 系统的 Edge 路径
            possible_paths = [
                "/usr/bin/microsoft-edge",
                "/usr/bin/microsoft-edge-stable",
                "/opt/microsoft/msedge/msedge",
            ]

        for path in possible_paths:
            if os.path.exists(path):
                logging.info(f"✅ 找到 Edge 浏览器: {path}")
                return path

        logging.warning("⚠️ 未找到 Microsoft Edge 浏览器")
        return None

    def _find_chrome_path(self):
        """查找 Google Chrome 浏览器路径"""
        possible_paths = []

        if sys.platform == "win32":
            # Windows 系统的 Chrome 路径
            possible_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
            ]
        elif sys.platform == "darwin":
            # macOS 系统的 Chrome 路径
            possible_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            ]
        else:
            # Linux 系统的 Chrome 路径
            possible_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
            ]

        for path in possible_paths:
            if os.path.exists(path):
                logging.info(f"✅ 找到 Chrome 浏览器: {path}")
                return path

        logging.warning("⚠️ 未找到 Google Chrome 浏览器")
        return None

    def _find_chromium_path(self):
        """查找 Chromium 浏览器路径"""
        possible_paths = []

        if sys.platform == "win32":
            # Windows 系统的 Chromium 路径
            possible_paths = [
                r"C:\Program Files\Chromium\Application\chrome.exe",
                r"C:\Program Files (x86)\Chromium\Application\chrome.exe",
            ]
        elif sys.platform == "darwin":
            # macOS 系统的 Chromium 路径
            possible_paths = [
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
            ]
        else:
            # Linux 系统的 Chromium 路径
            possible_paths = [
                "/usr/bin/chromium",
                "/usr/bin/chromium-browser",
            ]

        for path in possible_paths:
            if os.path.exists(path):
                logging.info(f"✅ 找到 Chromium 浏览器: {path}")
                return path

        logging.warning("⚠️ 未找到 Chromium 浏览器")
        return None

    def _get_extension_path(self,exname='turnstilePatch'):
        """获取插件路径"""
        root_dir = os.getcwd()
        extension_path = os.path.join(root_dir, exname)

        if hasattr(sys, "_MEIPASS"):
            extension_path = os.path.join(sys._MEIPASS, exname)

        if not os.path.exists(extension_path):
            raise FileNotFoundError(f"插件不存在: {extension_path}")

        return extension_path

    def quit(self):
        """关闭浏览器并清理临时文件"""
        if self.browser:
            try:
                self.browser.quit()
            except:
                pass

        # 清理临时用户数据目录
        self._cleanup_temp_directory()

    def _cleanup_temp_directory(self):
        """清理临时用户数据目录"""
        if self.temp_user_data_dir and os.path.exists(self.temp_user_data_dir):
            try:
                import shutil
                import time

                # 等待一下确保浏览器完全关闭
                time.sleep(2)

                logging.info(f"🧹 清理临时目录: {self.temp_user_data_dir}")
                shutil.rmtree(self.temp_user_data_dir, ignore_errors=True)
                logging.info("✅ 临时目录清理完成")

            except Exception as e:
                logging.warning(f"⚠️ 清理临时目录失败: {e}")
            finally:
                self.temp_user_data_dir = None
