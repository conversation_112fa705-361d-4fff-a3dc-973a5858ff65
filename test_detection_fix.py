#!/usr/bin/env python3
"""
测试修复后的检测逻辑
验证 Turnstile success 检测和条款复选框/按钮状态检测
"""

import time
from logger import logging

def test_turnstile_detection():
    """测试 Turnstile success 检测"""
    try:
        logging.info("🧪 测试 Turnstile success 检测...")
        
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        # 访问注册页面
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(3)
        
        # 点击开始按钮
        start_button = tab.ele("text:Get started", timeout=5)
        if start_button:
            start_button.click()
            time.sleep(3)
            
            # 检查是否有 Turnstile 验证
            from cursor_style_register import AugmentCodeRegister
            register = AugmentCodeRegister()
            register.tab = tab
            
            # 测试 success 检测
            success = register._check_turnstile_success()
            logging.info(f"🔍 Turnstile success 检测结果: {success}")
            
            if success:
                logging.info("✅ 成功检测到 Turnstile 验证完成")
            else:
                logging.info("ℹ️ 未检测到 Turnstile 验证完成")
        
        browser_manager.quit()
        return True
        
    except Exception as e:
        logging.error(f"❌ Turnstile 检测测试失败: {e}")
        return False

def test_terms_detection():
    """测试条款页面检测逻辑"""
    try:
        logging.info("🧪 测试条款页面检测逻辑...")
        
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        # 直接访问条款同意页面
        terms_url = "https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https%3A%2F%2Fapp.augmentcode.com%2Fauth%2Fcallback&state=test&code_challenge=test&code_challenge_method=S256"
        
        tab.get(terms_url)
        time.sleep(5)
        
        # 查找条款复选框
        terms_checkbox = tab.ele("#terms-of-service-checkbox", timeout=5)
        if terms_checkbox:
            # 测试复选框状态检测
            checked_attr = terms_checkbox.attr("checked")
            is_checked = checked_attr is not None
            
            logging.info(f"📋 复选框 checked 属性: {checked_attr}")
            logging.info(f"📋 复选框状态: {'已勾选' if is_checked else '未勾选'}")
            
            # 查找注册按钮
            signup_button = tab.ele("#signup-button", timeout=3)
            if signup_button:
                # 测试按钮状态检测
                disabled_attr = signup_button.attr("disabled")
                is_disabled = disabled_attr is not None
                
                logging.info(f"🔍 按钮 disabled 属性: {disabled_attr}")
                logging.info(f"🔍 按钮状态: {'禁用' if is_disabled else '启用'}")
                
                # 如果复选框未勾选，尝试勾选
                if not is_checked:
                    logging.info("☑️ 尝试勾选复选框...")
                    terms_checkbox.click()
                    time.sleep(2)
                    
                    # 重新检测状态
                    checked_attr_after = terms_checkbox.attr("checked")
                    is_checked_after = checked_attr_after is not None
                    
                    disabled_attr_after = signup_button.attr("disabled")
                    is_disabled_after = disabled_attr_after is not None
                    
                    logging.info(f"📋 勾选后复选框 checked 属性: {checked_attr_after}")
                    logging.info(f"📋 勾选后复选框状态: {'已勾选' if is_checked_after else '未勾选'}")
                    logging.info(f"🔍 勾选后按钮 disabled 属性: {disabled_attr_after}")
                    logging.info(f"🔍 勾选后按钮状态: {'禁用' if is_disabled_after else '启用'}")
                    
                    if is_checked_after and not is_disabled_after:
                        logging.info("✅ 复选框勾选和按钮启用都成功")
                    elif is_checked_after:
                        logging.info("✅ 复选框勾选成功，但按钮仍然禁用")
                    else:
                        logging.warning("⚠️ 复选框勾选失败")
                else:
                    logging.info("ℹ️ 复选框已经勾选")
                    
                    if not is_disabled:
                        logging.info("✅ 按钮已启用")
                    else:
                        logging.warning("⚠️ 复选框已勾选但按钮仍然禁用")
            else:
                logging.error("❌ 未找到注册按钮")
        else:
            logging.error("❌ 未找到条款复选框")
        
        browser_manager.quit()
        return True
        
    except Exception as e:
        logging.error(f"❌ 条款检测测试失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("=" * 80)
    logging.info("检测逻辑修复验证测试")
    logging.info("=" * 80)
    
    # 测试1: Turnstile success 检测
    logging.info("\n" + "=" * 50)
    logging.info("测试1: Turnstile Success 检测")
    logging.info("=" * 50)
    
    turnstile_success = test_turnstile_detection()
    
    # 测试2: 条款页面检测
    logging.info("\n" + "=" * 50)
    logging.info("测试2: 条款页面检测逻辑")
    logging.info("=" * 50)
    
    terms_success = test_terms_detection()
    
    # 总结
    logging.info("\n" + "=" * 50)
    logging.info("测试结果总结")
    logging.info("=" * 50)
    
    if turnstile_success:
        logging.info("✅ Turnstile 检测测试通过")
    else:
        logging.error("❌ Turnstile 检测测试失败")
    
    if terms_success:
        logging.info("✅ 条款检测测试通过")
    else:
        logging.error("❌ 条款检测测试失败")
    
    overall_success = turnstile_success and terms_success
    
    if overall_success:
        logging.info("🎉 所有检测逻辑修复验证成功！")
    else:
        logging.error("❌ 部分检测逻辑仍有问题！")
    
    return overall_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
