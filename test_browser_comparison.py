#!/usr/bin/env python3
"""
测试不同浏览器内核的 reCAPTCHA 验证效果
比较 Chrome、Edge、Chromium 的验证通过率
"""

import time
import os
from logger import logging

def test_browser_verification(browser_type: str, test_duration: int = 30) -> dict:
    """
    测试指定浏览器的验证效果
    
    Args:
        browser_type: 浏览器类型 ("chrome", "edge", "chromium")
        test_duration: 测试持续时间（秒）
    
    Returns:
        dict: 测试结果
    """
    result = {
        "browser_type": browser_type,
        "success": False,
        "error": None,
        "verification_time": None,
        "recaptcha_language": None,
        "user_agent": None
    }
    
    try:
        logging.info(f"🧪 开始测试 {browser_type.upper()} 浏览器...")
        
        from browser_utils import BrowserManager
        
        # 初始化浏览器
        browser_manager = BrowserManager(browser_type=browser_type)
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        # 记录用户代理
        user_agent = tab.run_js("return navigator.userAgent;")
        result["user_agent"] = user_agent
        logging.info(f"📋 User-Agent: {user_agent[:100]}...")
        
        # 检查浏览器语言设置
        language_info = tab.run_js("""
            return {
                language: navigator.language,
                languages: navigator.languages
            };
        """)
        logging.info(f"🌐 浏览器语言: {language_info}")
        
        # 访问注册页面
        logging.info("🌐 访问 AugmentCode 注册页面...")
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(3)
        
        # 应用日语语言设置
        from cursor_style_register import CursorStyleRegister
        register = CursorStyleRegister("<EMAIL>", browser_type=browser_type)
        register.tab = tab
        register._setup_japanese_language()
        
        # 点击开始按钮
        logging.info("🖱️ 点击开始按钮...")
        start_button = tab.ele("text:Get started", timeout=10)
        if start_button:
            start_button.click()
            time.sleep(5)
            
            # 检查 reCAPTCHA 语言
            start_time = time.time()
            
            # 等待并检查 reCAPTCHA
            for attempt in range(test_duration):
                recaptcha_urls = tab.run_js("""
                    const urls = [];
                    const iframes = document.querySelectorAll('iframe');
                    iframes.forEach(iframe => {
                        if (iframe.src && iframe.src.includes('recaptcha')) {
                            urls.push(iframe.src);
                        }
                    });
                    return urls;
                """)
                
                if recaptcha_urls:
                    for url in recaptcha_urls:
                        logging.info(f"🔍 reCAPTCHA URL: {url}")
                        if 'hl=' in url:
                            import re
                            hl_match = re.search(r'hl=([^&]+)', url)
                            if hl_match:
                                hl_value = hl_match.group(1)
                                result["recaptcha_language"] = hl_value
                                logging.info(f"🌐 reCAPTCHA 语言: hl={hl_value}")
                                
                                if hl_value == "ja":
                                    logging.info("✅ reCAPTCHA 使用日语！")
                                else:
                                    logging.warning(f"⚠️ reCAPTCHA 使用 {hl_value}，不是日语")
                    break
                
                time.sleep(1)
            
            # 尝试验证
            logging.info("🔧 尝试 Turnstile 验证...")
            verification_start = time.time()
            
            # 使用注册器的验证方法
            verification_success = register._handle_turnstile_cursor_method()
            
            verification_time = time.time() - verification_start
            result["verification_time"] = verification_time
            
            if verification_success:
                logging.info(f"🎉 {browser_type.upper()} 验证成功！耗时: {verification_time:.2f}秒")
                result["success"] = True
            else:
                logging.warning(f"⚠️ {browser_type.upper()} 验证失败")
        
        else:
            logging.error("❌ 未找到开始按钮")
            result["error"] = "未找到开始按钮"
        
        # 截图记录
        screenshot_path = f"{browser_type}_test_{int(time.time())}.png"
        tab.get_screenshot(path=screenshot_path)
        logging.info(f"📸 截图保存: {screenshot_path}")
        
        # 关闭浏览器
        browser_manager.quit()
        
    except Exception as e:
        logging.error(f"❌ {browser_type.upper()} 测试异常: {e}")
        result["error"] = str(e)
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
    
    return result

def compare_browsers():
    """比较不同浏览器的验证效果"""
    browsers_to_test = ["chrome", "edge", "chromium"]
    results = []
    
    logging.info("=" * 80)
    logging.info("🧪 浏览器验证效果对比测试")
    logging.info("=" * 80)
    
    for browser_type in browsers_to_test:
        logging.info(f"\n{'='*20} 测试 {browser_type.upper()} {'='*20}")
        
        result = test_browser_verification(browser_type, test_duration=30)
        results.append(result)
        
        # 显示测试结果
        if result["success"]:
            logging.info(f"✅ {browser_type.upper()}: 验证成功 ({result['verification_time']:.2f}s)")
        else:
            logging.error(f"❌ {browser_type.upper()}: 验证失败 - {result.get('error', '未知错误')}")
        
        if result["recaptcha_language"]:
            logging.info(f"🌐 {browser_type.upper()}: reCAPTCHA 语言 = {result['recaptcha_language']}")
        
        # 等待一段时间再测试下一个浏览器
        time.sleep(5)
    
    # 生成对比报告
    logging.info("\n" + "=" * 80)
    logging.info("📊 测试结果汇总")
    logging.info("=" * 80)
    
    for result in results:
        browser = result["browser_type"].upper()
        success = "✅ 成功" if result["success"] else "❌ 失败"
        lang = result.get("recaptcha_language", "未检测")
        time_taken = f"{result['verification_time']:.2f}s" if result["verification_time"] else "N/A"
        
        logging.info(f"{browser:10} | {success:8} | 语言: {lang:5} | 耗时: {time_taken}")
    
    # 推荐最佳浏览器
    successful_browsers = [r for r in results if r["success"]]
    if successful_browsers:
        # 按验证时间排序
        successful_browsers.sort(key=lambda x: x.get("verification_time", float('inf')))
        best_browser = successful_browsers[0]
        
        logging.info(f"\n🏆 推荐浏览器: {best_browser['browser_type'].upper()}")
        logging.info(f"   验证成功，耗时: {best_browser['verification_time']:.2f}秒")
        logging.info(f"   reCAPTCHA 语言: {best_browser.get('recaptcha_language', '未知')}")
    else:
        logging.warning("⚠️ 所有浏览器验证都失败了")
    
    return results

def test_specific_browser(browser_type: str):
    """测试指定的浏览器"""
    logging.info("=" * 80)
    logging.info(f"🧪 测试 {browser_type.upper()} 浏览器验证效果")
    logging.info("=" * 80)
    
    result = test_browser_verification(browser_type, test_duration=60)
    
    if result["success"]:
        logging.info(f"🎉 {browser_type.upper()} 验证成功！")
        logging.info(f"   验证耗时: {result['verification_time']:.2f}秒")
        logging.info(f"   reCAPTCHA 语言: {result.get('recaptcha_language', '未知')}")
    else:
        logging.error(f"❌ {browser_type.upper()} 验证失败")
        logging.error(f"   错误信息: {result.get('error', '未知错误')}")
    
    return result

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        # 测试指定浏览器
        browser_type = sys.argv[1].lower()
        if browser_type in ["chrome", "edge", "chromium"]:
            result = test_specific_browser(browser_type)
            return result["success"]
        else:
            logging.error(f"❌ 不支持的浏览器类型: {browser_type}")
            logging.info("支持的浏览器: chrome, edge, chromium")
            return False
    else:
        # 对比所有浏览器
        results = compare_browsers()
        return any(r["success"] for r in results)

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
