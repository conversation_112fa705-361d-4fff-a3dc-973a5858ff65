#!/usr/bin/env python3
"""
AugmentCode 注册工具测试脚本
用于测试配置和基本功能
"""

import os
import sys
from logger import logging
from config import Config
from augmentcode_register import AugmentCodeRegister


def test_config():
    """测试配置文件"""
    print("=" * 50)
    print("测试配置文件...")
    print("=" * 50)
    
    try:
        config = Config()
        print("✓ 配置文件加载成功")
        
        # 打印配置信息
        config.print_config()
        
        # 检查邮箱配置
        if config.get_temp_mail() != "null":
            email = f"{config.get_temp_mail()}{config.get_temp_mail_ext()}"
            print(f"✓ 使用临时邮箱: {email}")
        else:
            imap_config = config.get_imap()
            if imap_config:
                email = imap_config['imap_user']
                print(f"✓ 使用 IMAP 邮箱: {email}")
            else:
                print("✗ 未配置有效的邮箱")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置文件测试失败: {str(e)}")
        return False


def test_pdf_file():
    """测试 PDF 文件是否存在"""
    print("\n" + "=" * 50)
    print("测试 PDF 文件...")
    print("=" * 50)
    
    pdf_file_path = os.path.join(os.getcwd(), "WIPDF.pdf")
    
    if os.path.exists(pdf_file_path):
        file_size = os.path.getsize(pdf_file_path)
        print(f"✓ 找到 PDF 文件: {pdf_file_path}")
        print(f"✓ 文件大小: {file_size} 字节")
        return True
    else:
        print(f"✗ 未找到 PDF 文件: {pdf_file_path}")
        print("请确保项目根目录下有 WIPDF.pdf 文件")
        return False


def test_browser_init():
    """测试浏览器初始化"""
    print("\n" + "=" * 50)
    print("测试浏览器初始化...")
    print("=" * 50)
    
    try:
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        
        if browser:
            print("✓ 浏览器初始化成功")
            
            # 测试基本功能
            tab = browser.latest_tab
            tab.get("https://www.google.com")
            print("✓ 页面访问测试成功")
            
            # 清理
            browser_manager.quit()
            print("✓ 浏览器关闭成功")
            return True
        else:
            print("✗ 浏览器初始化失败")
            return False
            
    except Exception as e:
        print(f"✗ 浏览器测试失败: {str(e)}")
        return False


def test_email_handler():
    """测试邮箱处理器"""
    print("\n" + "=" * 50)
    print("测试邮箱处理器...")
    print("=" * 50)
    
    try:
        config = Config()
        
        # 获取邮箱地址
        if config.get_temp_mail() != "null":
            email = f"{config.get_temp_mail()}{config.get_temp_mail_ext()}"
        else:
            imap_config = config.get_imap()
            if imap_config:
                email = imap_config['imap_user']
            else:
                print("✗ 未配置有效的邮箱")
                return False
        
        from get_email_code import EmailVerificationHandler
        
        email_handler = EmailVerificationHandler(email)
        print(f"✓ 邮箱处理器初始化成功: {email}")
        
        # 注意：这里不实际获取验证码，只是测试初始化
        print("✓ 邮箱处理器配置正确")
        return True
        
    except Exception as e:
        print(f"✗ 邮箱处理器测试失败: {str(e)}")
        return False


def run_dry_test():
    """运行干运行测试（不实际执行注册）"""
    print("\n" + "=" * 50)
    print("运行干运行测试...")
    print("=" * 50)
    
    try:
        config = Config()
        
        # 获取邮箱地址
        if config.get_temp_mail() != "null":
            email = f"{config.get_temp_mail()}{config.get_temp_mail_ext()}"
        else:
            imap_config = config.get_imap()
            if imap_config:
                email = imap_config['imap_user']
            else:
                print("✗ 未配置有效的邮箱")
                return False
        
        # 创建注册器实例
        register = AugmentCodeRegister(email)
        print(f"✓ AugmentCode 注册器创建成功")
        print(f"✓ 使用邮箱: {email}")
        print(f"✓ 起始URL: {register.start_url}")
        print(f"✓ 推广URL: {register.promotion_url}")
        
        return True
        
    except Exception as e:
        print(f"✗ 干运行测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("AugmentCode 注册工具测试")
    print("=" * 50)
    
    tests = [
        ("配置文件测试", test_config),
        ("PDF 文件测试", test_pdf_file),
        ("浏览器初始化测试", test_browser_init),
        ("邮箱处理器测试", test_email_handler),
        ("干运行测试", run_dry_test),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试异常: {test_name} - {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    if passed == total:
        print("✓ 所有测试通过！可以运行注册流程。")
        print("\n运行注册:")
        print("python augmentcode_register.py")
    else:
        print("✗ 部分测试失败，请检查配置和环境。")
        print("\n常见问题:")
        print("1. 检查 .env 文件配置")
        print("2. 确保 WIPDF.pdf 文件存在")
        print("3. 检查网络连接")
        print("4. 确保已安装所有依赖")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
