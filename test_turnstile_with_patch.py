#!/usr/bin/env python3
"""
测试带有 turnstilePatch 的 AugmentCode 注册流程
"""

import os
import sys
import time
from logger import logging
from augmentcode_register import AugmentCodeRegister, generate_email_with_timestamp

def check_turnstile_patch():
    """检查 turnstilePatch 是否存在"""
    patch_dir = "turnstilePatch"
    required_files = ["manifest.json", "script.js"]
    
    if not os.path.exists(patch_dir):
        logging.error(f"❌ turnstilePatch 目录不存在: {patch_dir}")
        return False
    
    for file in required_files:
        file_path = os.path.join(patch_dir, file)
        if not os.path.exists(file_path):
            logging.error(f"❌ turnstilePatch 文件不存在: {file_path}")
            return False
    
    logging.info(f"✅ turnstilePatch 检查通过")
    return True

def main():
    """测试带有 turnstilePatch 的 AugmentCode 注册流程"""
    try:
        logging.info("=" * 60)
        logging.info("测试带有 turnstilePatch 的 AugmentCode 注册流程")
        logging.info("=" * 60)
        
        # 1. 检查 turnstilePatch
        if not check_turnstile_patch():
            logging.error("turnstilePatch 检查失败，无法继续")
            return False
        
        # 2. 检查环境配置
        proxy = os.getenv("BROWSER_PROXY", "").strip()
        if proxy:
            logging.info(f"✅ 代理配置: {proxy}")
        else:
            logging.warning("⚠️  未配置代理，建议设置 BROWSER_PROXY=http://127.0.0.1:1080")
        
        domain = os.getenv("DOMAIN", "").strip()
        if domain:
            logging.info(f"✅ 域名配置: {domain}")
        else:
            logging.error("❌ 未配置域名，请设置 DOMAIN")
            return False
        
        temp_mail = os.getenv("TEMP_MAIL", "").strip()
        if temp_mail and temp_mail != "null":
            logging.info(f"✅ 临时邮箱配置: {temp_mail}")
        else:
            logging.error("❌ 未配置临时邮箱，请设置 TEMP_MAIL")
            return False
        
        # 3. 生成邮箱地址（使用验证成功的逻辑）
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱地址: {email}")
        
        # 4. 创建注册器
        register = AugmentCodeRegister(email)
        
        # 5. 执行注册流程
        logging.info("🚀 开始执行注册流程（使用 turnstilePatch 和 Cursor 的成功方法）...")
        success = register.register()
        
        if success:
            logging.info("🎉 AugmentCode 注册成功！")
            logging.info(f"📧 注册邮箱: {email}")
            logging.info("✅ turnstilePatch 和 Cursor 方法验证成功")
        else:
            logging.error("😞 AugmentCode 注册失败！")
            logging.error("❌ 需要进一步调试 Turnstile 验证问题")
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 带有 turnstilePatch 的注册测试成功！")
        print("✅ Turnstile 验证问题已解决")
        sys.exit(0)
    else:
        print("\n😞 带有 turnstilePatch 的注册测试失败！")
        print("❌ 需要进一步调试")
        sys.exit(1)
