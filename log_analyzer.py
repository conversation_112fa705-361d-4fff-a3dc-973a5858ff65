#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志分析器 - 用于分析行为模拟注册的结果
提供详细的执行报告和问题诊断
"""

import os
import json
import re
from pathlib import Path
from datetime import datetime
import logging

class LogAnalyzer:
    def __init__(self, log_dir="logs/behavior_simulation"):
        self.log_dir = Path(log_dir)
        self.content_dir = self.log_dir / "page_contents"
        self.analysis_results = {}
        
    def analyze_latest_run(self):
        """分析最新的运行结果"""
        print("=== 行为模拟注册日志分析 ===\n")
        
        # 1. 找到最新的日志文件
        log_files = list(self.log_dir.glob("behavior_simulation_*.log"))
        if not log_files:
            print("❌ 未找到日志文件")
            return
        
        latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
        print(f"📄 分析日志文件: {latest_log.name}")
        
        # 2. 分析日志内容
        self.analyze_log_file(latest_log)
        
        # 3. 分析页面内容
        self.analyze_page_contents()
        
        # 4. 生成综合报告
        self.generate_report()
    
    def analyze_log_file(self, log_file):
        """分析日志文件"""
        print("\n📊 日志文件分析:")
        
        with open(log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        # 统计各步骤执行情况
        steps_status = {}
        steps = [
            "步骤1: 访问推广页面",
            "步骤2: 点击注册按钮", 
            "步骤3: 处理Turnstile验证",
            "步骤4: 填写邮箱",
            "步骤5: 同意条款并提交",
            "步骤6: 获取验证码",
            "步骤7: 提交验证码"
        ]
        
        for step in steps:
            if f"=== {step} ===" in log_content:
                if f"{step[:-4]}完成:" in log_content:
                    steps_status[step] = "✅ 成功"
                elif f"{step[:-4]}失败:" in log_content:
                    steps_status[step] = "❌ 失败"
                else:
                    steps_status[step] = "⚠️  未完成"
            else:
                steps_status[step] = "❓ 未执行"
        
        # 显示步骤状态
        for step, status in steps_status.items():
            print(f"  {status} {step}")
        
        # 检查关键信息
        self.analysis_results['steps_status'] = steps_status
        
        # 提取邮箱和验证码
        email_match = re.search(r'生成邮箱: ([^\s]+)', log_content)
        if email_match:
            self.analysis_results['email'] = email_match.group(1)
            print(f"\n📧 使用邮箱: {email_match.group(1)}")
        
        code_match = re.search(r'获取到验证码: ([^\s]+)', log_content)
        if code_match:
            self.analysis_results['verification_code'] = code_match.group(1)
            print(f"🔑 验证码: {code_match.group(1)}")
        
        # 检查错误信息
        errors = re.findall(r'ERROR - (.+)', log_content)
        if errors:
            print(f"\n❌ 发现 {len(errors)} 个错误:")
            for i, error in enumerate(errors, 1):
                print(f"  {i}. {error}")
            self.analysis_results['errors'] = errors
        
        # 检查警告信息
        warnings = re.findall(r'WARNING - (.+)', log_content)
        if warnings:
            print(f"\n⚠️  发现 {len(warnings)} 个警告:")
            for i, warning in enumerate(warnings, 1):
                print(f"  {i}. {warning}")
            self.analysis_results['warnings'] = warnings
        
        # 检查地区限制
        if "检测到地区限制信息" in log_content:
            print("\n🚫 地区限制检测: 是")
            self.analysis_results['region_restricted'] = True
        else:
            print("\n🚫 地区限制检测: 否")
            self.analysis_results['region_restricted'] = False
    
    def analyze_page_contents(self):
        """分析保存的页面内容"""
        print("\n📄 页面内容分析:")
        
        if not self.content_dir.exists():
            print("  ❌ 页面内容目录不存在")
            return
        
        # 获取所有页面内容文件
        content_files = list(self.content_dir.glob("*.json"))
        content_files.sort(key=lambda x: x.stat().st_mtime)
        
        print(f"  📁 找到 {len(content_files)} 个页面记录")
        
        page_analysis = {}
        
        for content_file in content_files:
            try:
                with open(content_file, 'r', encoding='utf-8') as f:
                    page_info = json.load(f)
                
                step = page_info.get('step', 'unknown')
                url = page_info.get('url', 'unknown')
                
                # 分析对应的文本文件
                text_file = content_file.with_suffix('.txt')
                if text_file.exists():
                    with open(text_file, 'r', encoding='utf-8') as f:
                        page_text = f.read().lower()
                    
                    # 检查关键词
                    keywords_found = []
                    
                    # 地区限制关键词
                    region_keywords = [
                        "limiting signups", "certain regions", "increased demand",
                        "地区限制", "region", "限制注册", "not available in your region"
                    ]
                    
                    for keyword in region_keywords:
                        if keyword in page_text:
                            keywords_found.append(f"🚫 地区限制: {keyword}")
                    
                    # reCAPTCHA相关
                    if "recaptcha" in page_text:
                        keywords_found.append("🔒 reCAPTCHA检测")
                    
                    # Verisoul相关
                    if "verisoul" in page_text:
                        keywords_found.append("🛡️  Verisoul检测")
                    
                    # Turnstile相关
                    if "turnstile" in page_text or "cloudflare" in page_text:
                        keywords_found.append("☁️  Turnstile/Cloudflare")
                    
                    page_analysis[step] = {
                        'url': url,
                        'keywords': keywords_found,
                        'text_length': len(page_text)
                    }
                
            except Exception as e:
                print(f"  ❌ 分析文件失败 {content_file.name}: {e}")
        
        # 显示页面分析结果
        for step, info in page_analysis.items():
            print(f"\n  📄 {step}:")
            print(f"    URL: {info['url']}")
            print(f"    文本长度: {info['text_length']} 字符")
            if info['keywords']:
                print(f"    关键发现:")
                for keyword in info['keywords']:
                    print(f"      {keyword}")
            else:
                print(f"    关键发现: 无特殊检测")
        
        self.analysis_results['page_analysis'] = page_analysis
    
    def generate_report(self):
        """生成综合分析报告"""
        print("\n" + "="*50)
        print("📋 综合分析报告")
        print("="*50)
        
        # 执行成功率
        total_steps = len(self.analysis_results.get('steps_status', {}))
        successful_steps = sum(1 for status in self.analysis_results.get('steps_status', {}).values() 
                              if status == "✅ 成功")
        
        if total_steps > 0:
            success_rate = (successful_steps / total_steps) * 100
            print(f"📊 执行成功率: {success_rate:.1f}% ({successful_steps}/{total_steps})")
        
        # 主要问题诊断
        print(f"\n🔍 问题诊断:")
        
        if self.analysis_results.get('region_restricted'):
            print("  🚫 主要问题: 地区限制检测")
            print("     建议: 需要更强的地理位置伪装和指纹模拟")
        
        errors = self.analysis_results.get('errors', [])
        if errors:
            print(f"  ❌ 技术错误: {len(errors)} 个")
            print("     建议: 检查元素选择器和页面加载时间")
        
        # 检测系统分析
        page_analysis = self.analysis_results.get('page_analysis', {})
        detection_systems = set()
        
        for step_info in page_analysis.values():
            for keyword in step_info.get('keywords', []):
                if 'reCAPTCHA' in keyword:
                    detection_systems.add('Google reCAPTCHA')
                elif 'Verisoul' in keyword:
                    detection_systems.add('Verisoul反欺诈')
                elif 'Turnstile' in keyword:
                    detection_systems.add('Cloudflare Turnstile')
        
        if detection_systems:
            print(f"\n🛡️  检测到的反欺诈系统:")
            for system in detection_systems:
                print(f"     • {system}")
        
        # 改进建议
        print(f"\n💡 改进建议:")
        
        if self.analysis_results.get('region_restricted'):
            print("  1. 增强IP地理位置伪装（使用高质量美国住宅代理）")
            print("  2. 完善设备指纹伪装（Canvas、WebGL、Audio噪声）")
            print("  3. 模拟更真实的美国用户网络环境")
        
        if 'Google reCAPTCHA' in detection_systems:
            print("  4. 针对reCAPTCHA的行为模式优化")
            print("  5. 增加鼠标轨迹和点击模式的真实性")
        
        if 'Verisoul反欺诈' in detection_systems:
            print("  6. 研究Verisoul的设备指纹检测机制")
            print("  7. 实现更深层的浏览器环境伪装")
        
        print(f"\n📁 详细日志位置: {self.log_dir}")
        print(f"📄 页面内容位置: {self.content_dir}")
        
        # 保存分析结果
        report_file = self.log_dir / f"analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
        
        print(f"📊 分析报告已保存: {report_file}")

def main():
    analyzer = LogAnalyzer()
    analyzer.analyze_latest_run()

if __name__ == "__main__":
    main()
