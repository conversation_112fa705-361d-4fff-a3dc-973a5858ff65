#!/usr/bin/env python3
"""
测试无痕模式和环境隔离效果
验证每次启动都是干净的环境，不会互相污染
"""

import time
import os
from logger import logging

def test_incognito_isolation():
    """测试无痕模式的环境隔离效果"""
    
    logging.info("=" * 80)
    logging.info("🔒 测试无痕模式和环境隔离")
    logging.info("=" * 80)
    
    test_results = []
    
    # 测试多次启动，检查环境隔离
    for round_num in range(3):
        logging.info(f"\n🧪 第 {round_num + 1} 轮测试...")
        
        result = test_single_browser_session(round_num + 1)
        test_results.append(result)
        
        # 等待一下再进行下一轮测试
        time.sleep(3)
    
    # 分析测试结果
    analyze_isolation_results(test_results)
    
    return all(r["isolated"] for r in test_results)

def test_single_browser_session(session_id: int) -> dict:
    """测试单个浏览器会话"""
    
    result = {
        "session_id": session_id,
        "isolated": False,
        "user_data_dir": None,
        "cookies_count": 0,
        "local_storage_count": 0,
        "session_storage_count": 0,
        "error": None
    }
    
    try:
        from browser_utils import BrowserManager
        
        # 使用 Chrome 浏览器测试
        browser_manager = BrowserManager(browser_type="chrome")
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        # 记录用户数据目录
        result["user_data_dir"] = browser_manager.temp_user_data_dir
        logging.info(f"📁 会话 {session_id} 用户数据目录: {result['user_data_dir']}")
        
        # 访问一个测试页面
        logging.info(f"🌐 会话 {session_id}: 访问测试页面...")
        tab.get("https://httpbin.org/cookies")
        time.sleep(2)
        
        # 设置一些测试数据
        logging.info(f"💾 会话 {session_id}: 设置测试数据...")
        
        # 设置 localStorage
        tab.run_js(f"""
            localStorage.setItem('test_session', '{session_id}');
            localStorage.setItem('test_timestamp', '{int(time.time())}');
        """)
        
        # 设置 sessionStorage
        tab.run_js(f"""
            sessionStorage.setItem('session_id', '{session_id}');
            sessionStorage.setItem('session_data', 'test_data_{session_id}');
        """)
        
        # 设置 cookie
        tab.run_js(f"""
            document.cookie = 'test_session={session_id}; path=/';
            document.cookie = 'test_data=session_{session_id}_data; path=/';
        """)
        
        # 检查存储的数据
        storage_info = tab.run_js("""
            return {
                localStorage: Object.keys(localStorage).length,
                sessionStorage: Object.keys(sessionStorage).length,
                cookies: document.cookie.split(';').filter(c => c.trim()).length
            };
        """)
        
        if storage_info:
            result["local_storage_count"] = storage_info.get("localStorage", 0)
            result["session_storage_count"] = storage_info.get("sessionStorage", 0)
            result["cookies_count"] = storage_info.get("cookies", 0)
        
        logging.info(f"📊 会话 {session_id} 存储数据:")
        logging.info(f"   localStorage: {result['local_storage_count']} 项")
        logging.info(f"   sessionStorage: {result['session_storage_count']} 项")
        logging.info(f"   cookies: {result['cookies_count']} 个")
        
        # 检查是否能访问之前会话的数据
        previous_data = tab.run_js("""
            const previousSessions = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.startsWith('test_session') && localStorage.getItem(key) !== arguments[0]) {
                    previousSessions.push(localStorage.getItem(key));
                }
            }
            return previousSessions;
        """, session_id)
        
        if previous_data and len(previous_data) > 0:
            logging.warning(f"⚠️ 会话 {session_id} 发现之前会话的数据: {previous_data}")
            result["isolated"] = False
        else:
            logging.info(f"✅ 会话 {session_id} 环境隔离正常")
            result["isolated"] = True
        
        # 关闭浏览器
        browser_manager.quit()
        
        # 验证临时目录是否被清理
        time.sleep(3)  # 等待清理完成
        
        if result["user_data_dir"] and os.path.exists(result["user_data_dir"]):
            logging.warning(f"⚠️ 会话 {session_id} 临时目录未被清理: {result['user_data_dir']}")
        else:
            logging.info(f"✅ 会话 {session_id} 临时目录已清理")
        
    except Exception as e:
        logging.error(f"❌ 会话 {session_id} 测试异常: {e}")
        result["error"] = str(e)
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
    
    return result

def analyze_isolation_results(results: list):
    """分析隔离测试结果"""
    
    logging.info("\n" + "=" * 80)
    logging.info("📊 环境隔离测试结果分析")
    logging.info("=" * 80)
    
    isolated_count = sum(1 for r in results if r["isolated"])
    total_count = len(results)
    
    logging.info(f"总测试轮数: {total_count}")
    logging.info(f"隔离成功: {isolated_count}")
    logging.info(f"隔离失败: {total_count - isolated_count}")
    logging.info(f"隔离成功率: {isolated_count / total_count * 100:.1f}%")
    
    # 检查用户数据目录是否都不同
    user_data_dirs = [r["user_data_dir"] for r in results if r["user_data_dir"]]
    unique_dirs = set(user_data_dirs)
    
    logging.info(f"\n📁 用户数据目录:")
    logging.info(f"总目录数: {len(user_data_dirs)}")
    logging.info(f"唯一目录数: {len(unique_dirs)}")
    
    if len(unique_dirs) == len(user_data_dirs):
        logging.info("✅ 每次都使用了不同的用户数据目录")
    else:
        logging.warning("⚠️ 发现重复的用户数据目录")
    
    # 显示详细结果
    logging.info(f"\n📋 详细结果:")
    for i, result in enumerate(results, 1):
        status = "✅ 隔离" if result["isolated"] else "❌ 污染"
        error = f" (错误: {result['error']})" if result["error"] else ""
        logging.info(f"会话 {i}: {status}{error}")
        if result["user_data_dir"]:
            logging.info(f"   目录: {os.path.basename(result['user_data_dir'])}")
    
    # 总结建议
    if isolated_count == total_count:
        logging.info("\n🎉 环境隔离测试完全成功！")
        logging.info("✅ 每次启动都是干净的环境，不会互相污染")
    else:
        logging.warning("\n⚠️ 环境隔离存在问题")
        logging.warning("需要检查无痕模式配置")

def test_multiple_browsers():
    """测试多个浏览器的隔离效果"""
    
    logging.info("=" * 80)
    logging.info("🌐 测试多浏览器环境隔离")
    logging.info("=" * 80)
    
    browsers = ["chrome", "edge"]  # 测试 Chrome 和 Edge
    
    for browser_type in browsers:
        logging.info(f"\n🧪 测试 {browser_type.upper()} 浏览器...")
        
        try:
            from browser_utils import BrowserManager
            
            browser_manager = BrowserManager(browser_type=browser_type)
            browser = browser_manager.init_browser()
            tab = browser.latest_tab
            
            # 检查浏览器信息
            browser_info = tab.run_js("""
                return {
                    userAgent: navigator.userAgent,
                    cookieEnabled: navigator.cookieEnabled,
                    doNotTrack: navigator.doNotTrack,
                    language: navigator.language,
                    onLine: navigator.onLine
                };
            """)
            
            logging.info(f"📋 {browser_type.upper()} 浏览器信息:")
            logging.info(f"   User-Agent: {browser_info.get('userAgent', 'N/A')[:100]}...")
            logging.info(f"   Cookie 启用: {browser_info.get('cookieEnabled', 'N/A')}")
            logging.info(f"   Do Not Track: {browser_info.get('doNotTrack', 'N/A')}")
            logging.info(f"   语言: {browser_info.get('language', 'N/A')}")
            
            # 检查无痕模式特征
            incognito_check = tab.run_js("""
                // 检查一些无痕模式的特征
                return {
                    webkitTemporaryStorage: typeof webkitTemporaryStorage !== 'undefined',
                    requestFileSystem: typeof webkitRequestFileSystem !== 'undefined',
                    indexedDB: typeof indexedDB !== 'undefined',
                    localStorage: typeof localStorage !== 'undefined'
                };
            """)
            
            logging.info(f"🔒 {browser_type.upper()} 无痕模式特征:")
            for feature, available in incognito_check.items():
                status = "可用" if available else "不可用"
                logging.info(f"   {feature}: {status}")
            
            browser_manager.quit()
            logging.info(f"✅ {browser_type.upper()} 测试完成")
            
        except Exception as e:
            logging.error(f"❌ {browser_type.upper()} 测试失败: {e}")

def main():
    """主函数"""
    
    logging.info("🔒 开始无痕模式和环境隔离测试")
    
    # 测试 1: 环境隔离
    isolation_success = test_incognito_isolation()
    
    # 测试 2: 多浏览器
    test_multiple_browsers()
    
    # 总结
    logging.info("\n" + "=" * 80)
    logging.info("🎯 测试总结")
    logging.info("=" * 80)
    
    if isolation_success:
        logging.info("🎉 无痕模式和环境隔离测试成功！")
        logging.info("✅ 每次注册都会使用全新的环境")
        logging.info("✅ 不会有 cookies、缓存等污染问题")
    else:
        logging.warning("⚠️ 环境隔离存在问题，需要进一步调试")
    
    return isolation_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
