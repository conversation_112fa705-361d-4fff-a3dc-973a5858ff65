"""
AugmentCode 专用邮箱验证码处理器
基于现有的 EmailVerificationHandler，专门处理 AugmentCode 的验证码格式
"""

import re
import time
from logger import logging
from get_email_code import EmailVerificationHandler


class AugmentCodeEmailHandler(EmailVerificationHandler):
    """AugmentCode 专用邮箱验证码处理器"""
    
    def __init__(self, account):
        """
        初始化处理器
        
        Args:
            account: 邮箱账号
        """
        super().__init__(account)
        
    def extract_augmentcode_verification_code(self, email_text: str) -> str:
        """
        从邮件文本中提取 AugmentCode 验证码
        
        Args:
            email_text: 邮件文本内容
            
        Returns:
            str: 验证码，如果未找到则返回 None
        """
        # AugmentCode 验证码格式: "Your verification code is: 441575"
        patterns = [
            r"Your verification code is:\s*(\d{6})",  # 标准格式
            r"verification code is:\s*(\d{6})",       # 简化格式
            r"code is:\s*(\d{6})",                    # 更简化格式
            r"验证码[：:]\s*(\d{6})",                   # 中文格式
            r"verification code[：:]\s*(\d{6})",       # 英文格式
            r"(?<![a-zA-Z@.])\b(\d{6})\b",            # 通用6位数字（作为后备）
        ]
        
        for pattern in patterns:
            match = re.search(pattern, email_text, re.IGNORECASE)
            if match:
                code = match.group(1)
                logging.info(f"使用模式 '{pattern}' 找到验证码: {code}")
                return code
        
        logging.warning("未找到匹配的验证码格式")
        return None
    
    def get_augmentcode_verification_code(self, max_retries=5, retry_interval=30):
        """
        获取 AugmentCode 验证码，专门处理 AugmentCode 的邮件格式
        
        Args:
            max_retries: 最大重试次数
            retry_interval: 重试间隔时间（秒）
            
        Returns:
            str: 验证码，如果获取失败则返回 None
        """
        logging.info("开始获取 AugmentCode 验证码...")
        
        for attempt in range(max_retries):
            try:
                logging.info(f"第 {attempt + 1}/{max_retries} 次尝试获取验证码...")
                
                if not self.imap:
                    # 使用临时邮箱
                    verify_code, first_id = self._get_augmentcode_mail_code()
                    if verify_code is not None and first_id is not None:
                        self._cleanup_mail(first_id)
                        return verify_code
                else:
                    # 使用 IMAP/POP3
                    if self.protocol.upper() == 'IMAP':
                        verify_code = self._get_augmentcode_mail_code_by_imap()
                    else:
                        verify_code = self._get_augmentcode_mail_code_by_pop3()
                    if verify_code is not None:
                        return verify_code
                
                if attempt < max_retries - 1:
                    logging.warning(f"未获取到验证码，{retry_interval} 秒后重试...")
                    time.sleep(retry_interval)
                    
            except Exception as e:
                logging.error(f"获取验证码失败: {e}")
                if attempt < max_retries - 1:
                    logging.error(f"发生错误，{retry_interval} 秒后重试...")
                    time.sleep(retry_interval)
                else:
                    raise Exception(f"获取验证码失败且已达最大重试次数: {e}") from e
        
        raise Exception(f"经过 {max_retries} 次尝试后仍未获取到验证码。")
    
    def _get_augmentcode_mail_code(self):
        """
        从临时邮箱获取 AugmentCode 验证码
        
        Returns:
            tuple: (验证码, 邮件ID)
        """
        # 获取邮件列表
        mail_list_url = f"https://tempmail.plus/api/mails?email={self.username}{self.emailExtension}&limit=20&epin={self.epin}"
        mail_list_response = self.session.get(mail_list_url)
        mail_list_data = mail_list_response.json()
        time.sleep(0.5)
        
        if not mail_list_data.get("result"):
            return None, None
        
        # 获取最新邮件的ID
        first_id = mail_list_data.get("first_id")
        if not first_id:
            return None, None
        
        # 获取具体邮件内容
        mail_detail_url = f"https://tempmail.plus/api/mails/{first_id}?email={self.username}{self.emailExtension}&epin={self.epin}"
        mail_detail_response = self.session.get(mail_detail_url)
        mail_detail_data = mail_detail_response.json()
        time.sleep(0.5)
        
        if not mail_detail_data.get("result"):
            return None, None
        
        # 从邮件文本中提取验证码
        mail_text = mail_detail_data.get("text", "")
        mail_subject = mail_detail_data.get("subject", "")
        
        logging.info(f"找到邮件主题: {mail_subject}")
        logging.debug(f"邮件内容预览: {mail_text[:200]}...")
        
        # 检查是否是 AugmentCode 的邮件
        augmentcode_keywords = ["augmentcode", "augment code", "welcome to augment"]
        is_augmentcode_mail = any(keyword in mail_subject.lower() for keyword in augmentcode_keywords) or \
                             any(keyword in mail_text.lower() for keyword in augmentcode_keywords)

        if not is_augmentcode_mail:
            logging.warning("邮件似乎不是来自 AugmentCode")
        else:
            logging.info("✅ 确认这是 AugmentCode 的邮件")
        
        # 提取验证码
        verification_code = self.extract_augmentcode_verification_code(mail_text)
        
        if verification_code:
            return verification_code, first_id
        return None, None
    
    def _get_augmentcode_mail_code_by_imap(self, retry=0):
        """
        通过 IMAP 获取 AugmentCode 验证码
        
        Args:
            retry: 重试次数
            
        Returns:
            str: 验证码
        """
        if retry > 0:
            time.sleep(3)
        if retry >= 20:
            raise Exception("获取验证码超时")
        
        try:
            import imaplib
            import email
            from datetime import datetime
            
            # 连接到IMAP服务器
            mail = imaplib.IMAP4_SSL(self.imap['imap_server'], self.imap['imap_port'])
            mail.login(self.imap['imap_user'], self.imap['imap_pass'])
            
            search_by_date = False
            # 针对网易系邮箱的特殊处理
            if self.imap['imap_user'].endswith(('@163.com', '@126.com', '@yeah.net')):
                imap_id = ("name", self.imap['imap_user'].split('@')[0], "contact", self.imap['imap_user'], "version", "1.0.0", "vendor", "imaplib")
                mail.xatom('ID', '("' + '" "'.join(imap_id) + '")')
                search_by_date = True
            
            mail.select(self.imap['imap_dir'])
            
            if search_by_date:
                date = datetime.now().strftime("%d-%b-%Y")
                status, messages = mail.search(None, f'ON {date} UNSEEN')
            else:
                status, messages = mail.search(None, 'TO', '"' + self.account + '"')
            
            if status != 'OK':
                return None
            
            mail_ids = messages[0].split()
            if not mail_ids:
                return self._get_augmentcode_mail_code_by_imap(retry=retry + 1)
            
            for mail_id in reversed(mail_ids):
                status, msg_data = mail.fetch(mail_id, '(RFC822)')
                if status != 'OK':
                    continue
                    
                raw_email = msg_data[0][1]
                email_message = email.message_from_bytes(raw_email)
                
                # 如果是按日期搜索的邮件，需要进一步核对收件人地址
                if search_by_date and email_message['to'] != self.account:
                    continue
                
                # 检查是否是 AugmentCode 的邮件
                sender = email_message.get('From', '')
                subject = email_message.get('Subject', '')
                
                if 'augmentcode' not in sender.lower() and 'augmentcode' not in subject.lower():
                    continue
                
                body = self._extract_imap_body(email_message)
                if body:
                    verification_code = self.extract_augmentcode_verification_code(body)
                    if verification_code:
                        # 删除找到验证码的邮件
                        mail.store(mail_id, '+FLAGS', '\\Deleted')
                        mail.expunge()
                        mail.logout()
                        return verification_code
            
            mail.logout()
            return None
            
        except Exception as e:
            logging.error(f"IMAP 获取验证码失败: {e}")
            return None
    
    def _get_augmentcode_mail_code_by_pop3(self, retry=0):
        """
        通过 POP3 获取 AugmentCode 验证码
        
        Args:
            retry: 重试次数
            
        Returns:
            str: 验证码
        """
        if retry > 0:
            time.sleep(3)
        if retry >= 20:
            raise Exception("获取验证码超时")
        
        pop3 = None
        try:
            import poplib
            from email.parser import Parser
            
            # 连接到服务器
            pop3 = poplib.POP3_SSL(self.imap['imap_server'], int(self.imap['imap_port']))
            pop3.user(self.imap['imap_user'])
            pop3.pass_(self.imap['imap_pass'])
            
            # 获取最新的10封邮件
            num_messages = len(pop3.list()[1])
            for i in range(num_messages, max(1, num_messages-9), -1):
                response, lines, octets = pop3.retr(i)
                msg_content = b'\r\n'.join(lines).decode('utf-8')
                msg = Parser().parsestr(msg_content)
                
                # 检查发件人和主题
                sender = msg.get('From', '')
                subject = msg.get('Subject', '')
                
                if 'augmentcode' in sender.lower() or 'augmentcode' in subject.lower():
                    # 提取邮件正文
                    body = self._extract_pop3_body(msg)
                    if body:
                        verification_code = self.extract_augmentcode_verification_code(body)
                        if verification_code:
                            pop3.quit()
                            return verification_code
            
            pop3.quit()
            return self._get_augmentcode_mail_code_by_pop3(retry=retry + 1)
            
        except Exception as e:
            logging.error(f"POP3 获取验证码失败: {e}")
            if pop3:
                try:
                    pop3.quit()
                except:
                    pass
            return None


if __name__ == "__main__":
    # 测试验证码提取
    test_texts = [
        "Your verification code is: 441575",
        "Your verification code is:441575",
        "verification code is: 123456",
        "The code is: 789012",
        "验证码：345678",
        "Some text before 901234 some text after",
    ]
    
    handler = AugmentCodeEmailHandler("<EMAIL>")
    
    for text in test_texts:
        code = handler.extract_augmentcode_verification_code(text)
        print(f"文本: '{text}' -> 验证码: {code}")
