#!/usr/bin/env python3
"""
AugmentCode 注册工具启动脚本
提供用户友好的交互界面
"""

import os
import sys
import time
from logger import logging
from logo import print_logo
from language import language, get_translation
from config import Config
from augmentcode_register import AugmentCodeRegister


def print_welcome():
    """打印欢迎信息"""
    print_logo()
    print("\n" + "=" * 60)
    print("🚀 AugmentCode 自动注册工具")
    print("=" * 60)
    print("基于 Cursor 自动注册项目开发")
    print("支持自动处理 Turnstile 验证、邮箱验证码获取、文件上传等")
    print("=" * 60)


def check_prerequisites():
    """检查前置条件"""
    print("\n📋 检查前置条件...")
    
    issues = []
    
    # 检查配置文件
    try:
        config = Config()
        print("✓ 配置文件加载成功")
    except Exception as e:
        issues.append(f"配置文件错误: {str(e)}")
    
    # 检查 PDF 文件
    pdf_file = os.path.join(os.getcwd(), "WIPDF.pdf")
    if os.path.exists(pdf_file):
        print("✓ 找到 WIPDF.pdf 文件")
    else:
        issues.append("未找到 WIPDF.pdf 文件，请确保文件在项目根目录")
    
    # 检查邮箱配置
    try:
        config = Config()
        if config.get_temp_mail() != "null":
            email = f"{config.get_temp_mail()}{config.get_temp_mail_ext()}"
            print(f"✓ 邮箱配置: {email} (临时邮箱)")
        else:
            imap_config = config.get_imap()
            if imap_config:
                email = imap_config['imap_user']
                protocol = config.get_protocol()
                print(f"✓ 邮箱配置: {email} ({protocol})")
            else:
                issues.append("未配置有效的邮箱")
    except Exception as e:
        issues.append(f"邮箱配置错误: {str(e)}")
    
    if issues:
        print("\n❌ 发现以下问题:")
        for issue in issues:
            print(f"   • {issue}")
        print("\n请解决上述问题后重新运行。")
        return False
    
    print("✓ 所有前置条件检查通过")
    return True


def get_user_confirmation():
    """获取用户确认"""
    print("\n" + "=" * 60)
    print("📝 注册流程说明:")
    print("1. 访问 AugmentCode 页面并点击 'Get your free month'")
    print("2. 自动处理 Turnstile 人机验证")
    print("3. 输入邮箱地址")
    print("4. 点击 Continue 按钮")
    print("5. 自动获取并输入邮箱验证码")
    print("6. 上传 WIPDF.pdf 文件")
    print("=" * 60)
    
    print("\n⚠️  注意事项:")
    print("• 请确保网络连接稳定")
    print("• 注册过程中请勿关闭程序")
    print("• 如遇到问题，请查看日志文件和截图")
    print("• 整个过程大约需要 2-5 分钟")
    
    while True:
        try:
            choice = input("\n是否开始注册？(y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                return True
            elif choice in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y 或 n")
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            return False


def run_registration():
    """运行注册流程"""
    try:
        # 获取邮箱配置
        config = Config()
        
        if config.get_temp_mail() != "null":
            email = f"{config.get_temp_mail()}{config.get_temp_mail_ext()}"
        else:
            imap_config = config.get_imap()
            if imap_config:
                email = imap_config['imap_user']
            else:
                logging.error("未配置有效的邮箱")
                return False
        
        print(f"\n🚀 开始注册，使用邮箱: {email}")
        print("=" * 60)
        
        # 创建注册器
        register = AugmentCodeRegister(email)
        
        # 执行注册
        start_time = time.time()
        success = register.register()
        end_time = time.time()
        
        duration = int(end_time - start_time)
        minutes = duration // 60
        seconds = duration % 60
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 注册成功！")
            print(f"⏱️  用时: {minutes}分{seconds}秒")
            print(f"📧 注册邮箱: {email}")
            print("\n📁 相关文件:")
            print("   • 日志文件: logs/")
            print("   • 截图文件: screenshots/")
        else:
            print("❌ 注册失败")
            print(f"⏱️  用时: {minutes}分{seconds}秒")
            print("\n🔍 故障排除:")
            print("   • 查看最新的日志文件了解详细错误")
            print("   • 查看截图文件了解页面状态")
            print("   • 检查网络连接和邮箱配置")
            print("   • 尝试重新运行程序")
        
        print("=" * 60)
        return success
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        logging.error(f"程序异常: {str(e)}")
        return False


def main():
    """主函数"""
    try:
        # 打印欢迎信息
        print_welcome()
        
        # 检查前置条件
        if not check_prerequisites():
            input("\n按回车键退出...")
            return False
        
        # 获取用户确认
        if not get_user_confirmation():
            print("\n用户取消操作，程序退出。")
            return False
        
        # 运行注册流程
        success = run_registration()
        
        # 等待用户确认退出
        if success:
            input("\n🎉 注册完成！按回车键退出...")
        else:
            input("\n❌ 注册失败，按回车键退出...")
        
        return success
        
    except Exception as e:
        print(f"\n程序出错: {str(e)}")
        logging.error(f"主程序出错: {str(e)}")
        input("\n按回车键退出...")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
