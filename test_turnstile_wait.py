#!/usr/bin/env python3
"""
测试 Turnstile 验证等待逻辑
确保真正等到验证完成才继续
"""

import os
import sys
import time
from logger import logging

def test_turnstile_wait_logic():
    """测试 Turnstile 等待逻辑"""
    try:
        logging.info("🧪 测试 Turnstile 等待逻辑...")
        
        # 使用 Cursor 的 BrowserManager
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        logging.info("✅ 浏览器初始化成功")
        
        # 1. 访问 Cursor 资源页面
        logging.info("🌐 访问 Cursor 资源页面...")
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(3)
        
        # 2. 点击注册按钮
        logging.info("🖱️ 点击 Get your free month 按钮...")
        button = tab.ele("@text()=Get your free month", timeout=5)
        if button:
            button.click()
            time.sleep(5)
            logging.info("✅ 按钮点击成功")
        else:
            logging.error("❌ 未找到注册按钮")
            return False
        
        # 3. 检测并等待 Turnstile 验证
        logging.info("🔍 检测 Turnstile 验证...")
        auth0_captcha = tab.ele("#ulp-auth0-v2-captcha", timeout=5)
        
        if not auth0_captcha:
            logging.info("ℹ️ 未检测到 Turnstile 验证")
            return True
        
        logging.info("🔍 检测到 Auth0 V2 Captcha，开始等待验证完成...")
        
        # 详细的等待逻辑
        max_wait_time = 60  # 最多等待60秒
        check_interval = 2   # 每2秒检查一次
        
        for i in range(0, max_wait_time, check_interval):
            time.sleep(check_interval)
            
            logging.info(f"⏳ 检查验证状态... ({i+check_interval}/{max_wait_time}秒)")

            # 检查1: 在 Turnstile iframe 内部查找成功标志
            try:
                # 访问 Turnstile iframe 内部
                auth0_captcha = tab.ele("#ulp-auth0-v2-captcha", timeout=1)
                if auth0_captcha:
                    try:
                        iframe_body = (
                            auth0_captcha
                            .child()
                            .shadow_root.ele("tag:iframe")
                            .ele("tag:body")
                        )

                        if iframe_body:
                            # 优先检查 #success 元素（已知的成功标志）
                            try:
                                success_element = iframe_body.sr("#success")
                                if success_element:
                                    success_text = success_element.text if hasattr(success_element, 'text') else ""
                                    logging.info(f"🔍 找到 #success 元素，文本: '{success_text}'")

                                    if success_text and success_text.lower() == "success!":
                                        logging.info("🎉 检测到 Success! 标志，验证完成！")
                                        tab.get_screenshot(path=f"turnstile_success_{int(time.time())}.png")
                                        return True
                            except Exception as e:
                                logging.debug(f"检查 #success 元素异常: {e}")

                            # 检查其他可能的成功标志
                            other_indicators = [
                                "tag:input[value*='success']",
                                "tag:input[data-success='true']",
                                ".success",
                                "@text()=Success!",
                            ]

                            for indicator in other_indicators:
                                try:
                                    element = iframe_body.sr(indicator)
                                    if element:
                                        element_text = element.text if hasattr(element, 'text') else ""
                                        element_value = element.attr("value") if hasattr(element, 'attr') else ""

                                        logging.debug(f"检查元素 {indicator}: 文本='{element_text}', 值='{element_value}'")

                                        # 检查是否包含成功标志
                                        if (element_text and "success" in element_text.lower()) or \
                                           (element_value and "success" in element_value.lower()):
                                            logging.info(f"🎉 检测到其他成功标志: {indicator}")
                                            tab.get_screenshot(path=f"turnstile_other_success_{int(time.time())}.png")
                                            return True
                                except:
                                    continue

                            # 检查 input 元素的值是否有变化（表示验证完成）
                            try:
                                challenge_input = iframe_body.sr("tag:input")
                                if challenge_input:
                                    input_value = challenge_input.attr("value")
                                    logging.info(f"🔍 Challenge input 值: '{input_value}' (长度: {len(input_value) if input_value else 0})")

                                    if input_value and len(input_value) > 10:
                                        logging.info("🎉 Challenge input 值已生成，验证完成！")
                                        tab.get_screenshot(path=f"turnstile_input_success_{int(time.time())}.png")
                                        return True
                            except:
                                pass

                    except Exception as e:
                        logging.debug(f"访问 iframe 内部异常: {e}")

            except Exception as e:
                logging.debug(f"检查 iframe 内成功标志异常: {e}")

            # 检查1.5: 其他成功标志
            try:
                success_indicators = [
                    {"selector": "@text()=Success!", "desc": "Success! 文本"},
                    {"selector": ".success", "desc": "success 类"},
                    {"selector": "[data-success='true']", "desc": "success 属性"},
                    {"selector": ".verified", "desc": "verified 类"}
                ]

                for indicator in success_indicators:
                    element = tab.ele(indicator["selector"], timeout=1)
                    if element:
                        logging.info(f"🎉 检测到成功标志: {indicator['desc']}")
                        tab.get_screenshot(path=f"turnstile_success_alt_{int(time.time())}.png")
                        return True
            except Exception as e:
                logging.debug(f"检查其他成功标志异常: {e}")

            # 检查2: Continue 按钮状态（作为备用）
            try:
                continue_button = tab.ele("@text()=Continue", timeout=1)
                if continue_button:
                    disabled = continue_button.attr("disabled")
                    aria_disabled = continue_button.attr("aria-disabled")
                    class_name = continue_button.attr("class") or ""

                    logging.debug(f"Continue 按钮状态: disabled={disabled}, aria-disabled={aria_disabled}")

                    # 注意：按钮本身可能没有 disabled，所以这个检查不太可靠
                    if not disabled and aria_disabled != "true" and "disabled" not in class_name.lower():
                        logging.debug("Continue 按钮看起来可用，但需要 Success 标志确认")
                else:
                    logging.debug("未找到 Continue 按钮")
            except Exception as e:
                logging.debug(f"检查 Continue 按钮异常: {e}")
            
            # 检查2: Turnstile 内部状态
            try:
                challenge_check = (
                    auth0_captcha
                    .child()
                    .shadow_root.ele("tag:iframe")
                    .ele("tag:body")
                    .sr("tag:input")
                )
                
                if challenge_check:
                    challenge_value = challenge_check.attr("value")
                    logging.info(f"🔍 Challenge 值长度: {len(challenge_value) if challenge_value else 0}")
                    
                    if challenge_value and len(challenge_value) > 10:
                        logging.info("🎉 Turnstile challenge 值已生成，验证完成！")
                        tab.get_screenshot(path=f"turnstile_challenge_success_{int(time.time())}.png")
                        return True
                else:
                    logging.debug("未找到 challenge input")
                    
            except Exception as e:
                logging.debug(f"检查 challenge 状态异常: {e}")
            
            # 检查3: 页面跳转
            try:
                current_url = tab.url
                logging.info(f"🔍 当前 URL: {current_url}")
                
                if "passwordless" in current_url or "challenge" in current_url:
                    logging.info("🎉 页面已跳转，验证完成！")
                    tab.get_screenshot(path=f"turnstile_url_change_{int(time.time())}.png")
                    return True
            except Exception as e:
                logging.debug(f"检查 URL 异常: {e}")
            
            # 每10秒截图一次
            if (i + check_interval) % 10 == 0:
                screenshot_path = f"turnstile_wait_{i+check_interval}s_{int(time.time())}.png"
                tab.get_screenshot(path=screenshot_path)
                logging.info(f"📸 截图保存: {screenshot_path}")
        
        # 超时处理
        logging.warning(f"⚠️ Turnstile 验证在 {max_wait_time} 秒内未完成")
        tab.get_screenshot(path=f"turnstile_timeout_{int(time.time())}.png")
        
        # 最后检查一次
        try:
            continue_button = tab.ele("@text()=Continue", timeout=2)
            if continue_button:
                logging.info("🤔 Continue 按钮仍然存在，可能需要手动处理")
                return False
        except:
            pass
        
        return False
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        try:
            input("按 Enter 键关闭浏览器...")
            browser_manager.quit()
        except:
            pass

def test_continue_button_detection():
    """专门测试 Continue 按钮检测逻辑"""
    try:
        logging.info("🧪 测试 Continue 按钮检测逻辑...")
        
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        # 访问登录页面
        tab.get("https://login.augmentcode.com/u/login/identifier")
        time.sleep(5)
        
        # 查找 Continue 按钮
        continue_button = tab.ele("@text()=Continue", timeout=5)
        
        if continue_button:
            logging.info("✅ 找到 Continue 按钮")
            
            # 检查各种属性
            attributes = ["disabled", "aria-disabled", "class", "style"]
            for attr in attributes:
                value = continue_button.attr(attr)
                logging.info(f"🔍 {attr}: {value}")
            
            # 检查按钮文本
            button_text = continue_button.text
            logging.info(f"🔍 按钮文本: '{button_text}'")
            
            # 检查是否可点击
            try:
                is_clickable = continue_button.click_point
                logging.info(f"🔍 可点击坐标: {is_clickable}")
            except:
                logging.info("🔍 按钮不可点击")
            
        else:
            logging.error("❌ 未找到 Continue 按钮")
        
        input("按 Enter 键关闭浏览器...")
        browser_manager.quit()
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")

def main():
    """主测试函数"""
    logging.info("=" * 80)
    logging.info("Turnstile 等待逻辑测试")
    logging.info("=" * 80)
    
    # 检查环境
    proxy = os.getenv("BROWSER_PROXY", "")
    if not proxy:
        logging.warning("⚠️ 建议设置代理: set BROWSER_PROXY=127.0.0.1:1080")
    
    print("选择测试类型：")
    print("1. 完整 Turnstile 等待逻辑测试")
    print("2. Continue 按钮检测测试")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        success = test_turnstile_wait_logic()
        if success:
            logging.info("🎉 Turnstile 等待逻辑测试成功！")
        else:
            logging.error("❌ Turnstile 等待逻辑测试失败！")
    elif choice == "2":
        test_continue_button_detection()
    else:
        logging.error("❌ 无效选择")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
