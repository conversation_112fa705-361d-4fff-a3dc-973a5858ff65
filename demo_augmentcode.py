#!/usr/bin/env python3
"""
AugmentCode 注册工具演示脚本
展示如何使用注册器的各个功能
"""

import os
import time
from logger import logging
from config import Config
from augmentcode_register import AugmentCodeRegister
from augmentcode_email_handler import AugmentCodeEmailHandler


def demo_config():
    """演示配置加载"""
    print("=" * 50)
    print("演示：配置加载")
    print("=" * 50)
    
    try:
        config = Config()
        print("✓ 配置加载成功")
        
        # 显示配置信息
        config.print_config()
        
        # 获取邮箱地址
        if config.get_temp_mail() != "null":
            email = f"{config.get_temp_mail()}{config.get_temp_mail_ext()}"
            print(f"✓ 将使用邮箱: {email}")
        else:
            imap_config = config.get_imap()
            if imap_config:
                email = imap_config['imap_user']
                print(f"✓ 将使用邮箱: {email}")
            else:
                print("✗ 未配置有效的邮箱")
                return None
        
        return email
        
    except Exception as e:
        print(f"✗ 配置加载失败: {str(e)}")
        return None


def demo_email_handler(email):
    """演示邮箱处理器"""
    print("\n" + "=" * 50)
    print("演示：邮箱验证码处理器")
    print("=" * 50)
    
    try:
        # 创建邮箱处理器
        handler = AugmentCodeEmailHandler(email)
        print(f"✓ 邮箱处理器创建成功: {email}")
        
        # 测试验证码提取
        test_texts = [
            "Your verification code is: 123456",
            "verification code is: 789012",
            "The code is: 345678",
        ]
        
        print("\n测试验证码提取:")
        for text in test_texts:
            code = handler.extract_augmentcode_verification_code(text)
            print(f"  文本: '{text}' -> 验证码: {code}")
        
        return True
        
    except Exception as e:
        print(f"✗ 邮箱处理器演示失败: {str(e)}")
        return False


def demo_register_init(email):
    """演示注册器初始化"""
    print("\n" + "=" * 50)
    print("演示：注册器初始化")
    print("=" * 50)
    
    try:
        # 创建注册器
        register = AugmentCodeRegister(email)
        print(f"✓ 注册器创建成功")
        print(f"  邮箱: {register.email}")
        print(f"  起始URL: {register.start_url}")
        print(f"  推广URL: {register.promotion_url}")
        
        return register
        
    except Exception as e:
        print(f"✗ 注册器初始化失败: {str(e)}")
        return None


def demo_file_check():
    """演示文件检查"""
    print("\n" + "=" * 50)
    print("演示：文件检查")
    print("=" * 50)
    
    # 检查 PDF 文件
    pdf_file = os.path.join(os.getcwd(), "WIPDF.pdf")
    if os.path.exists(pdf_file):
        file_size = os.path.getsize(pdf_file)
        print(f"✓ 找到 PDF 文件: {pdf_file}")
        print(f"  文件大小: {file_size} 字节")
        return True
    else:
        print(f"✗ 未找到 PDF 文件: {pdf_file}")
        print("  注册时需要此文件，请确保文件存在")
        return False


def demo_browser_check():
    """演示浏览器检查"""
    print("\n" + "=" * 50)
    print("演示：浏览器检查")
    print("=" * 50)
    
    try:
        from browser_utils import BrowserManager
        
        print("正在初始化浏览器...")
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        
        if browser:
            print("✓ 浏览器初始化成功")
            
            # 测试基本功能
            tab = browser.latest_tab
            print("正在测试页面访问...")
            tab.get("https://www.google.com")
            print("✓ 页面访问测试成功")
            
            # 清理
            browser_manager.quit()
            print("✓ 浏览器关闭成功")
            return True
        else:
            print("✗ 浏览器初始化失败")
            return False
            
    except Exception as e:
        print(f"✗ 浏览器检查失败: {str(e)}")
        return False


def demo_registration_steps():
    """演示注册步骤（不实际执行）"""
    print("\n" + "=" * 50)
    print("演示：注册步骤说明")
    print("=" * 50)
    
    steps = [
        "第一步：访问 AugmentCode 页面并点击 'Get your free month'",
        "第二步：处理 Turnstile 人机验证",
        "第三步：输入邮箱地址",
        "第四步：点击 Continue 按钮",
        "第五步：获取并输入邮箱验证码",
        "第六步：上传 WIPDF.pdf 文件",
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"{i}. {step}")
        time.sleep(0.5)  # 模拟处理时间
    
    print("\n注意：这只是步骤说明，实际注册需要运行完整的注册流程。")


def main():
    """主演示函数"""
    print("AugmentCode 注册工具演示")
    print("=" * 50)
    print("这个演示将展示工具的各个组件和功能")
    print("=" * 50)
    
    # 演示配置加载
    email = demo_config()
    if not email:
        print("\n演示中断：配置加载失败")
        return False
    
    # 演示邮箱处理器
    if not demo_email_handler(email):
        print("\n演示中断：邮箱处理器失败")
        return False
    
    # 演示注册器初始化
    register = demo_register_init(email)
    if not register:
        print("\n演示中断：注册器初始化失败")
        return False
    
    # 演示文件检查
    demo_file_check()
    
    # 演示浏览器检查
    demo_browser_check()
    
    # 演示注册步骤
    demo_registration_steps()
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("=" * 50)
    print("如果所有检查都通过，您可以运行实际的注册流程：")
    print("  Windows: 双击 run_augmentcode.bat")
    print("  Linux/Mac: ./run_augmentcode.sh")
    print("  手动: python run_augmentcode_register.py")
    print("=" * 50)
    
    return True


if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n演示出错: {str(e)}")
        input("\n按回车键退出...")
