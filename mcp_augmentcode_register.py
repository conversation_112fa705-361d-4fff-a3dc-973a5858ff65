#!/usr/bin/env python3
"""
使用 MCP Playwright 完成 AugmentCode 注册
基于之前验证成功的 MCP 环境
"""

import os
import sys
import time
import re
from logger import logging
from email_handler import AugmentCodeEmailH<PERSON><PERSON>

def generate_email_with_timestamp():
    """生成带时间戳的邮箱地址"""
    import time
    import random
    timestamp = str(int(time.time()))[-4:]
    random_num = random.randint(1000, 9999)
    return f"jun-temp{random_num}@ai.ikunlove.de"

class MCPAugmentCodeRegister:
    """使用 MCP Playwright 的 AugmentCode 注册器"""
    
    def __init__(self, email: str):
        """
        初始化注册器
        
        Args:
            email: 注册邮箱
        """
        self.email = email
        self.email_handler = AugmentCodeEmailHandler(email)
        
    def register_with_mcp(self) -> bool:
        """
        使用 MCP Playwright 完成注册
        
        Returns:
            bool: 注册是否成功
        """
        try:
            logging.info("🚀 使用 MCP Playwright 开始 AugmentCode 注册...")
            logging.info(f"📧 注册邮箱: {self.email}")
            
            # 导入 MCP 工具
            from browser_navigate_Playwright import browser_navigate
            from browser_click_Playwright import browser_click
            from browser_type_Playwright import browser_type
            from browser_wait_for_Playwright import browser_wait_for
            from browser_snapshot_Playwright import browser_snapshot
            from browser_take_screenshot_Playwright import browser_take_screenshot
            
            # 1. 访问 AugmentCode Cursor 资源页面
            logging.info("📋 第一步：访问 AugmentCode Cursor 资源页面...")
            browser_navigate("https://www.augmentcode.com/resources/cursor")
            
            # 等待页面加载
            time.sleep(3)
            
            # 截图记录
            browser_take_screenshot(filename="mcp_step1_homepage.png")
            
            # 2. 点击 "Get your free month" 按钮
            logging.info("📋 第二步：点击 Get your free month 按钮...")
            
            # 获取页面快照找到按钮
            snapshot = browser_snapshot()
            
            # 查找 "Get your free month" 按钮
            get_free_month_buttons = [
                "//a[contains(text(), 'Get your free month')]",
                "//button[contains(text(), 'Get your free month')]",
                "//a[contains(@href, 'signup') or contains(@href, 'register')]",
                "//button[contains(@class, 'signup') or contains(@class, 'register')]"
            ]
            
            button_clicked = False
            for button_xpath in get_free_month_buttons:
                try:
                    # 这里需要从 snapshot 中找到对应的元素引用
                    # 由于 MCP 的限制，我们需要手动指定元素
                    browser_click(
                        element="Get your free month button",
                        ref="button_get_free_month"  # 需要从实际页面获取
                    )
                    button_clicked = True
                    logging.info("✅ 成功点击 Get your free month 按钮")
                    break
                except:
                    continue
            
            if not button_clicked:
                logging.error("❌ 未找到 Get your free month 按钮")
                return False
            
            # 等待页面跳转
            browser_wait_for(time=5)
            browser_take_screenshot(filename="mcp_step2_after_click.png")
            
            # 3. 处理人机验证（在 MCP 环境中应该自动通过）
            logging.info("📋 第三步：等待人机验证自动完成...")
            
            # 在 MCP 环境中，Turnstile 验证应该自动通过
            # 等待验证完成
            browser_wait_for(time=10)
            
            # 检查是否有 Continue 按钮可用
            try:
                browser_click(
                    element="Continue button",
                    ref="button_continue"
                )
                logging.info("✅ 人机验证完成，点击 Continue")
            except:
                logging.info("ℹ️ 未找到 Continue 按钮，可能已自动跳转")
            
            browser_take_screenshot(filename="mcp_step3_after_captcha.png")
            
            # 4. 输入邮箱
            logging.info("📋 第四步：输入邮箱...")
            
            try:
                browser_type(
                    element="Email input field",
                    ref="input_email",
                    text=self.email
                )
                logging.info(f"✅ 邮箱输入成功: {self.email}")
            except Exception as e:
                logging.error(f"❌ 邮箱输入失败: {e}")
                return False
            
            browser_take_screenshot(filename="mcp_step4_email_input.png")
            
            # 5. 点击 Continue
            logging.info("📋 第五步：点击 Continue...")
            
            try:
                browser_click(
                    element="Continue button after email",
                    ref="button_continue_email"
                )
                logging.info("✅ 点击 Continue 成功")
            except Exception as e:
                logging.error(f"❌ 点击 Continue 失败: {e}")
                return False
            
            # 等待页面跳转
            browser_wait_for(time=5)
            browser_take_screenshot(filename="mcp_step5_after_continue.png")
            
            # 6. 获取并输入验证码
            logging.info("📋 第六步：获取并输入验证码...")
            
            # 等待邮件发送
            time.sleep(10)
            
            # 获取验证码
            verification_code = self.email_handler.get_verification_code()
            
            if not verification_code:
                logging.error("❌ 未获取到验证码")
                return False
            
            logging.info(f"✅ 获取到验证码: {verification_code}")
            
            # 输入验证码
            try:
                browser_type(
                    element="Verification code input field",
                    ref="input_verification_code",
                    text=verification_code
                )
                logging.info("✅ 验证码输入成功")
            except Exception as e:
                logging.error(f"❌ 验证码输入失败: {e}")
                return False
            
            browser_take_screenshot(filename="mcp_step6_verification_code.png")
            
            # 7. 提交验证码
            logging.info("📋 第七步：提交验证码...")
            
            try:
                browser_click(
                    element="Submit verification code button",
                    ref="button_submit_code"
                )
                logging.info("✅ 验证码提交成功")
            except Exception as e:
                logging.error(f"❌ 验证码提交失败: {e}")
                return False
            
            # 等待验证
            browser_wait_for(time=5)
            browser_take_screenshot(filename="mcp_step7_after_verification.png")
            
            # 8. 上传文件（如果需要）
            logging.info("📋 第八步：检查是否需要上传文件...")
            
            try:
                # 检查是否有文件上传要求
                snapshot = browser_snapshot()
                
                if "upload" in snapshot.lower() or "file" in snapshot.lower():
                    logging.info("🔍 检测到文件上传要求")
                    
                    # 创建一个简单的文本文件
                    upload_file = "temp_upload.txt"
                    with open(upload_file, "w") as f:
                        f.write("AugmentCode registration file")
                    
                    # 上传文件（需要根据实际页面调整）
                    browser_click(
                        element="File upload button",
                        ref="button_file_upload"
                    )
                    
                    # 清理临时文件
                    if os.path.exists(upload_file):
                        os.remove(upload_file)
                    
                    logging.info("✅ 文件上传完成")
                else:
                    logging.info("ℹ️ 无需上传文件")
                    
            except Exception as e:
                logging.warning(f"⚠️ 文件上传步骤出错: {e}")
            
            browser_take_screenshot(filename="mcp_step8_final.png")
            
            # 9. 检查注册结果
            logging.info("📋 第九步：检查注册结果...")
            
            # 等待最终结果
            browser_wait_for(time=10)
            
            # 获取最终页面快照
            final_snapshot = browser_snapshot()
            
            # 检查成功标志
            success_indicators = [
                "welcome",
                "success",
                "congratulations",
                "account created",
                "registration complete"
            ]
            
            is_success = any(indicator in final_snapshot.lower() for indicator in success_indicators)
            
            if is_success:
                logging.info("🎉 AugmentCode 注册成功！")
                browser_take_screenshot(filename="mcp_registration_success.png")
                return True
            else:
                logging.warning("⚠️ 注册结果不确定，请手动检查")
                browser_take_screenshot(filename="mcp_registration_uncertain.png")
                return False
                
        except Exception as e:
            logging.error(f"❌ MCP 注册过程中发生错误: {e}")
            import traceback
            logging.error(f"错误详情: {traceback.format_exc()}")
            
            # 保存错误状态截图
            try:
                browser_take_screenshot(filename="mcp_registration_error.png")
            except:
                pass
            
            return False

def main():
    """主函数"""
    try:
        logging.info("=" * 80)
        logging.info("MCP Playwright AugmentCode 注册")
        logging.info("=" * 80)
        
        # 生成邮箱
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱: {email}")
        
        # 创建注册器
        register = MCPAugmentCodeRegister(email)
        
        # 执行注册
        success = register.register_with_mcp()
        
        if success:
            logging.info("🎉 MCP AugmentCode 注册成功！")
            print(f"\n✅ 注册成功！")
            print(f"📧 邮箱: {email}")
        else:
            logging.error("😞 MCP AugmentCode 注册失败！")
            print(f"\n❌ 注册失败！")
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 主程序执行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
