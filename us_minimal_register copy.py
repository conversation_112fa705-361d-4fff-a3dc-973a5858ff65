#!/usr/bin/env python3
"""
基于 Cursor 成功经验的美国用户环境注册器
保持原有成功机制，只做最小化的地理位置修改
"""

import os
import sys
import time
from logger import logging
from augmentcode_register import generate_email_with_timestamp


class USMinimalRegister:
    """美国用户环境最小化修改注册器 - 基于Cursor成功逻辑"""

    def __init__(self, email: str, browser_type: str = "chrome"):
        """
        初始化注册器

        Args:
            email: 注册邮箱
            browser_type: 浏览器类型 ("chrome", "edge", "chromium")
        """
        self.email = email
        self.browser = None
        self.tab = None
        self.browser_manager = None
        self.browser_type = browser_type
    
    def init_browser(self) -> bool:
        """
        初始化浏览器（使用 Cursor 的成功配置 + 美国用户环境）
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logging.info(f"🇺🇸 初始化 {self.browser_type.upper()} 浏览器（美国用户环境）...")

            from browser_utils import BrowserManager

            self.browser_manager = BrowserManager(browser_type=self.browser_type)
            self.browser = self.browser_manager.init_browser()
            self.tab = self.browser.latest_tab
            
            # 注入最小化的美国用户环境脚本
            self._inject_minimal_us_environment()
            
            logging.info("✅ 美国用户环境浏览器初始化成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 浏览器初始化失败: {e}")
            return False

    def _inject_minimal_us_environment(self):
        """注入最小化的美国用户环境脚本（不影响人机验证）"""
        try:
            logging.info("🇺🇸 注入最小化美国用户环境...")
            
            # 最小化的美国用户环境脚本 - 只修改地理位置相关
            minimal_us_script = """
            (function() {
                'use strict';
                
                // 美国纽约坐标
                const US_COORDS = {
                    latitude: 40.7128,
                    longitude: -74.0060,
                    accuracy: 10
                };
                
                // 1. 地理位置API伪装（只修改坐标，不影响其他功能）
                if (navigator.geolocation && navigator.geolocation.getCurrentPosition) {
                    const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
                    
                    navigator.geolocation.getCurrentPosition = function(success, error, options) {
                        if (success) {
                            setTimeout(() => {
                                success({
                                    coords: US_COORDS,
                                    timestamp: Date.now()
                                });
                            }, Math.random() * 100 + 50);
                        }
                    };
                }
                
                // 2. 时区伪装（美国东部时区）
                const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
                Date.prototype.getTimezoneOffset = function() {
                    return 300; // EST: UTC-5
                };
                
                // 3. 语言环境伪装（保持简单，不影响验证）
                if (navigator.language !== 'en-US') {
                    try {
                        Object.defineProperty(navigator, 'language', {
                            get: function() { return 'en-US'; }
                        });
                        
                        Object.defineProperty(navigator, 'languages', {
                            get: function() { return ['en-US', 'en']; }
                        });
                    } catch(e) {
                        // 如果无法修改，忽略错误
                    }
                }
                
                console.log('🇺🇸 最小化美国用户环境已激活');
                
            })();
            """
            
            # 注入脚本
            self.tab.run_js(minimal_us_script)
            
            logging.info("✅ 最小化美国用户环境脚本注入成功")
            
        except Exception as e:
            logging.warning(f"⚠️ 美国用户环境脚本注入失败: {e}")

    def step1_visit_promotion_page(self) -> bool:
        """步骤1: 访问推广页面（保持原始逻辑）"""
        try:
            logging.info("🌐 步骤1: 访问推广页面...")
            
            # 使用原始脚本中的正确URL
            promotion_url = "https://www.augmentcode.com/resources/cursor"
            
            # 访问页面
            self.tab.get(promotion_url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 验证页面加载
            current_url = self.tab.url
            if "augmentcode.com" not in current_url:
                logging.error(f"❌ 页面加载失败，当前URL: {current_url}")
                return False
            
            logging.info(f"✅ 推广页面访问成功: {current_url}")
            
            # 截图记录
            self.tab.get_screenshot(path=f"us_step1_promotion_{int(time.time())}.png")
            
            logging.info("✅ 第一步完成：推广页面访问成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 访问推广页面失败: {e}")
            return False

    def step2_click_register_button(self) -> bool:
        """步骤2: 点击注册按钮（保持原始逻辑）"""
        try:
            logging.info("🖱️ 步骤2: 点击注册按钮...")
            
            # 等待页面完全加载
            time.sleep(3)
            
            # 使用原始脚本中成功的查找逻辑 - Cursor 促销页面按钮
            register_button = None
            register_texts = [
                "Get your free month",
                "Claim offer",
                "Get started",
                "Continue",
                "Sign up",
                "Register",
                "Start free trial",
                "Redeem"
            ]
            
            for text in register_texts:
                try:
                    register_button = self.tab.ele(f"@text()={text}", timeout=3)
                    if register_button:
                        logging.info(f"✅ 找到注册按钮: {text}")
                        break
                except:
                    continue
            
            if not register_button:
                # 尝试通过链接查找
                try:
                    register_button = self.tab.ele("@href*=register", timeout=3)
                    if register_button:
                        logging.info("✅ 通过链接找到注册按钮")
                except:
                    pass
            
            if not register_button:
                logging.error("❌ 未找到注册按钮")
                return False
            
            # 点击注册按钮（使用原始逻辑）
            register_button.click()
            logging.info("✅ 注册按钮点击成功")
            
            # 等待页面跳转
            time.sleep(5)
            
            # 验证跳转
            new_url = self.tab.url
            logging.info(f"🔍 跳转后URL: {new_url}")
            
            # 截图记录
            self.tab.get_screenshot(path=f"us_step2_register_click_{int(time.time())}.png")
            
            logging.info("✅ 第二步完成：注册按钮点击成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 点击注册按钮失败: {e}")
            return False

    def step3_handle_turnstile(self) -> bool:
        """步骤3: 处理Turnstile验证（完全保持原始逻辑）"""
        try:
            logging.info("🔐 步骤3: 处理Turnstile验证...")
            
            # 等待Turnstile加载
            time.sleep(5)
            
            # 查找Turnstile iframe（使用原始逻辑）
            turnstile_found = False
            try:
                # 查找Turnstile相关元素
                turnstile_elements = [
                    "iframe[src*='turnstile']",
                    "iframe[src*='cloudflare']",
                    ".cf-turnstile",
                    "#cf-turnstile"
                ]
                
                for selector in turnstile_elements:
                    try:
                        element = self.tab.ele(selector, timeout=3)
                        if element:
                            logging.info(f"✅ 检测到Turnstile: {selector}")
                            turnstile_found = True
                            break
                    except:
                        continue
                        
            except Exception as e:
                logging.info(f"ℹ️ Turnstile检测异常: {e}")
            
            if turnstile_found:
                logging.info("⏳ 等待Turnstile自动完成...")
                # 等待Turnstile自动完成（保持原始时间）
                time.sleep(10)
            else:
                logging.info("ℹ️ 未检测到Turnstile，可能已跳过")
            
            # 截图记录
            self.tab.get_screenshot(path=f"us_step3_turnstile_{int(time.time())}.png")
            
            logging.info("✅ 第三步完成：Turnstile处理完成")
            return True
            
        except Exception as e:
            logging.error(f"❌ Turnstile处理失败: {e}")
            return False

    def step4_input_email(self) -> bool:
        """步骤4: 输入邮箱（完全保持原始逻辑）"""
        try:
            logging.info("📧 步骤4: 输入邮箱...")

            # 等待页面稳定
            time.sleep(3)

            # 使用传入的邮箱
            email = self.email

            # 使用原始脚本中成功的选择器
            email_input = None
            email_selectors = [
                "#username",  # 原始脚本中成功的选择器
                "input[name=username]",
                "input[type=email]",
                "@type=email",
                "@placeholder*=email",
                "@name=email",
                "@id*=email"
            ]

            for selector in email_selectors:
                try:
                    if selector.startswith("#") or selector.startswith("input["):
                        email_input = self.tab.ele(selector, timeout=3)
                    else:
                        email_input = self.tab.ele(selector, timeout=3)

                    if email_input:
                        logging.info(f"✅ 找到邮箱输入框: {selector}")
                        break
                except:
                    continue

            if not email_input:
                logging.error("❌ 未找到邮箱输入框")
                return False

            # 输入邮箱（保持原始逻辑）
            email_input.clear()
            email_input.input(email)

            logging.info(f"✅ 邮箱输入成功: {email}")

            # 等待输入完成
            time.sleep(2)

            # 截图记录
            self.tab.get_screenshot(path=f"us_step4_email_{int(time.time())}.png")

            logging.info("✅ 第四步完成：邮箱输入成功")
            return True

        except Exception as e:
            logging.error(f"❌ 邮箱输入失败: {e}")
            return False

    def step5_click_continue(self) -> bool:
        """步骤5: 点击继续按钮（保持原始逻辑）"""
        try:
            logging.info("➡️ 步骤5: 点击继续按钮...")

            continue_buttons = [
                "Continue",
                "Next",
                "Proceed",
                "Submit",
                "Sign up",
                "Create account"
            ]

            for btn_text in continue_buttons:
                try:
                    button = self.tab.ele(f"@text()={btn_text}", timeout=3)
                    if button and button.states.is_enabled:
                        button.click()
                        logging.info(f"✅ 点击继续按钮成功: {btn_text}")
                        time.sleep(3)
                        return True
                except:
                    continue

            # 尝试通过类型查找
            try:
                submit_btn = self.tab.ele("input[type='submit']", timeout=3)
                if submit_btn:
                    submit_btn.click()
                    logging.info("✅ 点击提交按钮成功")
                    time.sleep(3)
                    return True
            except:
                pass

            logging.error("❌ 未找到继续按钮")
            return False

        except Exception as e:
            logging.error(f"❌ 点击继续按钮失败: {e}")
            return False

    def step6_handle_verification_code(self) -> bool:
        """步骤6: 处理验证码（完全保持原始逻辑）"""
        try:
            logging.info("📋 步骤6: 处理验证码...")

            # 等待邮件发送
            logging.info("⏳ 等待验证码邮件发送...")
            time.sleep(10)

            # 获取验证码（使用原始逻辑）
            try:
                from augmentcode_register import AugmentCodeRegister
                temp_register = AugmentCodeRegister(self.email)
                verification_code = temp_register.email_handler.get_augmentcode_verification_code(max_retries=5, retry_interval=30)

                if not verification_code:
                    logging.error("❌ 未获取到验证码")
                    return False

                logging.info(f"✅ 获取到验证码: {verification_code}")

            except Exception as e:
                logging.error(f"❌ 获取验证码失败: {e}")
                return False

            # 查找验证码输入框（使用原始选择器）
            code_input = None
            code_selectors = [
                "#code",  # 原始脚本中成功的选择器
                "input[id=code]",
                "input[name=code]",
                "@type=text",
                "@placeholder*=code",
                "@placeholder*=verification",
                "@name*=code",
                "@id*=code"
            ]

            for selector in code_selectors:
                try:
                    if selector.startswith("#") or selector.startswith("input["):
                        code_input = self.tab.ele(selector, timeout=3)
                    else:
                        code_input = self.tab.ele(selector, timeout=3)

                    if code_input:
                        logging.info(f"✅ 找到验证码输入框: {selector}")
                        break
                except:
                    continue

            if not code_input:
                logging.error("❌ 未找到验证码输入框")
                return False

            # 输入验证码
            code_input.clear()
            code_input.input(verification_code)

            logging.info("✅ 验证码输入成功")
            time.sleep(2)

            # 提交验证码
            submit_button = None
            submit_texts = ["Continue", "Submit", "Verify", "Confirm"]

            for text in submit_texts:
                try:
                    submit_button = self.tab.ele(f"@text()={text}", timeout=3)
                    if submit_button:
                        logging.info(f"✅ 找到提交按钮: {text}")
                        break
                except:
                    continue

            if submit_button:
                submit_button.click()
                logging.info("✅ 验证码提交成功")
                time.sleep(5)
            else:
                logging.warning("⚠️ 未找到提交按钮，可能自动提交")

            # 截图记录
            self.tab.get_screenshot(path=f"us_step6_verification_{int(time.time())}.png")

            logging.info("✅ 步骤6完成：验证码处理成功")
            return True

        except Exception as e:
            logging.error(f"❌ 步骤6失败: {e}")
            return False

    def step7_final_terms_agreement(self) -> bool:
        """步骤7: 最终条款同意（完全保持原始逻辑）"""
        try:
            logging.info("📋 步骤7: 最终条款同意...")

            # 检查当前URL
            current_url = self.tab.url
            logging.info(f"🔍 当前URL: {current_url}")

            # 如果不在terms-accept页面，说明可能已经完成
            if "terms-accept" not in current_url:
                logging.info("ℹ️ 不在条款同意页面，可能已完成注册")
                return True

            logging.info("🔍 检测到最终条款同意页面")

            # 等待页面完全加载
            time.sleep(2)

            # 调用条款同意处理方法（使用原始逻辑）
            if self._handle_terms_agreement():
                logging.info("✅ 最终条款同意处理成功")

                # 等待页面跳转
                time.sleep(5)

                # 检查是否跳转成功
                final_url = self.tab.url
                logging.info(f"🔍 处理后URL: {final_url}")

                if "terms-accept" not in final_url:
                    logging.info("🎉 成功跳转，注册流程完成！")
                    return True
                else:
                    logging.warning("⚠️ 仍在条款同意页面，可能需要手动检查")
                    return True  # 保守地认为成功
            else:
                logging.error("❌ 最终条款同意处理失败")
                return False

        except Exception as e:
            logging.error(f"❌ 步骤7失败: {e}")
            return False

    def _handle_terms_agreement(self) -> bool:
        """处理条款同意页面（完全保持原始逻辑）"""
        try:
            logging.info("📋 处理条款同意页面...")

            # 等待页面加载
            time.sleep(3)

            # 查找并勾选条款复选框（使用原始脚本逻辑）
            checkbox_found = False
            checkbox_selectors = [
                "input[type='checkbox']",
                ".checkbox input",
                "#terms-checkbox",
                "@type=checkbox"
            ]

            for selector in checkbox_selectors:
                try:
                    if selector.startswith("#") or selector.startswith(".") or selector.startswith("input["):
                        checkbox = self.tab.ele(selector, timeout=3)
                    else:
                        checkbox = self.tab.ele(selector, timeout=3)

                    if checkbox:
                        is_checked = checkbox.states.is_checked
                        if not is_checked:
                            # 方法1: 直接点击复选框
                            checkbox.click()
                            time.sleep(1)

                            # 检查是否勾选成功
                            is_checked_after_click = checkbox.states.is_checked
                            if is_checked_after_click:
                                logging.info(f"✅ 勾选条款复选框成功: {selector}")
                                checkbox_found = True
                            else:
                                logging.warning("⚠️ 直接点击失败，尝试JavaScript方法...")
                                # 方法2: 使用JavaScript设置checked属性并触发change事件
                                try:
                                    self.tab.run_js("""
                                        var checkbox = document.querySelector('input[type="checkbox"]');
                                        if (checkbox) {
                                            checkbox.checked = true;
                                            // 触发change事件
                                            var event = new Event('change', { bubbles: true });
                                            checkbox.dispatchEvent(event);
                                            // 也尝试调用onchange函数
                                            if (typeof updateSignupButton === 'function') {
                                                updateSignupButton(checkbox);
                                            }
                                        }
                                    """)
                                    time.sleep(1)
                                    logging.info("✅ JavaScript方法勾选成功")
                                    checkbox_found = True
                                except Exception as e:
                                    logging.warning(f"⚠️ JavaScript方法异常: {e}")
                        else:
                            logging.info("ℹ️ 条款复选框已勾选")
                            checkbox_found = True
                        break
                except:
                    continue

            if not checkbox_found:
                logging.warning("⚠️ 未找到条款复选框，可能已勾选或不需要")

            # 查找并点击注册按钮（使用原始脚本的逻辑）
            signup_button = self.tab.ele("#signup-button", timeout=3)
            if signup_button:
                # 检查按钮是否可用
                disabled_attr = signup_button.attr("disabled")
                is_disabled = disabled_attr is not None
                logging.info(f"🔍 注册按钮状态: {'禁用' if is_disabled else '启用'}")

                if is_disabled:
                    logging.warning("⚠️ 注册按钮仍然禁用，等待启用...")
                    # 等待按钮启用
                    for i in range(10):
                        time.sleep(1)
                        disabled_attr = signup_button.attr("disabled")
                        is_disabled = disabled_attr is not None
                        if not is_disabled:
                            logging.info(f"✅ 注册按钮在 {i+1} 秒后启用")
                            break

                    # 如果仍然禁用，尝试强制启用
                    if is_disabled:
                        logging.warning("⚠️ 按钮仍然禁用，尝试强制启用...")
                        try:
                            self.tab.run_js("""
                                var button = document.getElementById('signup-button');
                                if (button) {
                                    button.removeAttribute('disabled');
                                    button.disabled = false;
                                    button.classList.remove('disabled');
                                    // 触发按钮状态更新
                                    var event = new Event('change', { bubbles: true });
                                    button.dispatchEvent(event);
                                }
                            """)
                            time.sleep(1)

                            # 再次检查按钮状态
                            disabled_attr = signup_button.attr("disabled")
                            is_disabled = disabled_attr is not None
                            if not is_disabled:
                                logging.info("✅ 强制启用成功")
                            else:
                                logging.warning("⚠️ 强制启用失败")
                        except Exception as e:
                            logging.warning(f"⚠️ 强制启用异常: {e}")

                if not is_disabled:
                    signup_button.click()
                    logging.info("✅ 点击注册按钮成功")
                    time.sleep(3)
                    return True
                else:
                    logging.error("❌ 注册按钮仍然禁用")
                    return False
            else:
                logging.error("❌ 未找到注册按钮 (#signup-button)")
                return False

        except Exception as e:
            logging.error(f"❌ 条款同意处理失败: {e}")
            return False

    def run_registration(self):
        """运行完整注册流程（保持原始逻辑）"""
        try:
            logging.info("🇺🇸 开始美国用户环境最小化修改注册流程...")

            # 初始化浏览器
            if not self.init_browser():
                return False

            # 执行注册步骤（完全保持原始顺序和逻辑）
            steps = [
                ("访问推广页面", self.step1_visit_promotion_page),
                ("点击注册按钮", self.step2_click_register_button),
                ("处理Turnstile验证", self.step3_handle_turnstile),
                ("输入邮箱", self.step4_input_email),
                ("点击继续", self.step5_click_continue),
                ("处理验证码", self.step6_handle_verification_code),
                ("最终条款同意", self.step7_final_terms_agreement)
            ]

            for step_name, step_func in steps:
                logging.info(f"\n{'='*50}")
                logging.info(f"执行: {step_name}")
                logging.info('='*50)

                if not step_func():
                    logging.error(f"❌ {step_name} 失败，停止流程")
                    return False

                logging.info(f"✅ {step_name} 完成")
                time.sleep(2)

            logging.info("\n🎉 美国用户环境注册流程完成！")
            return True

        except Exception as e:
            logging.error(f"❌ 注册流程失败: {e}")
            return False
        finally:
            input("按 Enter 键关闭浏览器...")
            if self.browser_manager:
                self.browser_manager.quit()


def main():
    """主函数"""
    try:
        logging.info("=" * 60)
        logging.info("🇺🇸 美国用户环境最小化修改注册测试")
        logging.info("=" * 60)

        # 生成邮箱（使用原始逻辑）
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱: {email}")

        register = USMinimalRegister(email)
        success = register.run_registration()

        if success:
            logging.info("🎉 美国用户环境注册测试成功！")
            print(f"\n✅ 美国用户环境注册完成！")
            print(f"📧 邮箱: {email}")
        else:
            logging.error("😞 美国用户环境注册测试失败！")
            print("\n❌ 美国用户环境注册失败！")

        return success

    except Exception as e:
        logging.error(f"❌ 程序执行失败: {e}")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
