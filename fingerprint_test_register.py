#!/usr/bin/env python3
"""
指纹浏览器测试脚本 - AugmentCode注册
基于cursor_style_register.py的完整逻辑，集成高级指纹伪装功能
"""

import os
import sys
import time
from logger import logging
from augmentcode_register import generate_email_with_timestamp
from fingerprint_browser_utils import FingerprintBrowserManager


class FingerprintRegister:
    """使用指纹伪装的AugmentCode注册器"""

    def __init__(self, email: str, browser_type: str = "chrome"):
        """
        初始化注册器

        Args:
            email: 注册邮箱
            browser_type: 浏览器类型 ("chrome", "edge", "chromium")
        """
        self.email = email
        self.browser = None
        self.tab = None
        self.browser_manager = None
        self.browser_type = browser_type
    
    def init_browser(self) -> bool:
        """
        初始化指纹伪装浏览器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logging.info(f"🚀 初始化指纹伪装 {self.browser_type.upper()} 浏览器...")

            self.browser_manager = FingerprintBrowserManager(browser_type=self.browser_type)
            self.browser = self.browser_manager.init_browser()
            
            if not self.browser:
                logging.error("❌ 浏览器初始化失败")
                return False
                
            self.tab = self.browser.latest_tab
            
            logging.info("✅ 指纹伪装浏览器初始化成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 浏览器初始化失败: {e}")
            return False
    
    def step1_visit_promotion_page(self) -> bool:
        """
        第一步：访问 AugmentCode Cursor 促销页面

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第一步：访问 AugmentCode Cursor 促销页面...")

            self.tab.get("https://www.augmentcode.com/resources/cursor")
            time.sleep(3)

            # 注入指纹伪装脚本
            if self.browser_manager:
                self.browser_manager.inject_scripts_to_tab(self.tab)

            # 截图记录
            self.tab.get_screenshot(path=f"fingerprint_step1_promotion_page_{int(time.time())}.png")

            page_title = self.tab.title
            logging.info(f"📄 页面标题: {page_title}")

            logging.info("✅ 第一步完成：成功访问促销页面")
            return True

        except Exception as e:
            logging.error(f"❌ 第一步失败: {e}")
            return False
    
    def step2_click_register_button(self) -> bool:
        """
        第二步：点击注册按钮
        
        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第二步：查找并点击注册按钮...")
            
            # Cursor 促销页面可能的按钮文本
            button_texts = [
                "Get your free month",
                "Claim offer",
                "Get started", 
                "Continue",
                "Sign up",
                "Register",
                "Start free trial",
                "Redeem"
            ]
            
            button_clicked = False
            for button_text in button_texts:
                try:
                    logging.info(f"🔍 查找按钮: {button_text}")
                    button = self.tab.ele(f"@text()={button_text}", timeout=2)
                    
                    if button:
                        button.click()
                        logging.info(f"✅ 成功点击按钮: {button_text}")
                        button_clicked = True
                        break
                        
                except Exception as e:
                    logging.debug(f"按钮 {button_text} 不存在或点击失败: {e}")
                    continue
            
            if not button_clicked:
                logging.error("❌ 未找到可点击的注册按钮")
                return False
            
            # 等待页面跳转
            time.sleep(5)

            # 注入指纹伪装脚本
            if self.browser_manager:
                self.browser_manager.inject_scripts_to_tab(self.tab)

            # 截图记录跳转后的页面
            self.tab.get_screenshot(path=f"fingerprint_step2_after_click_{int(time.time())}.png")
            
            new_url = self.tab.url
            logging.info(f"🌐 跳转后 URL: {new_url}")
            
            logging.info("✅ 第二步完成：成功点击注册按钮")
            return True
            
        except Exception as e:
            logging.error(f"❌ 第二步失败: {e}")
            return False
    
    def step3_handle_turnstile(self) -> bool:
        """
        第三步：处理 Turnstile 验证（使用指纹伪装）

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第三步：处理 Turnstile 验证（指纹伪装模式）...")

            # 使用与原脚本相同的 Turnstile 处理方法
            return self._handle_turnstile_cursor_method()

        except Exception as e:
            logging.error(f"❌ 第三步失败: {e}")
            return False

    def _handle_turnstile_cursor_method(self, max_retries: int = 3) -> bool:
        """
        使用 Cursor 的方法处理 Turnstile 验证（已集成指纹伪装）

        Args:
            max_retries: 最大重试次数

        Returns:
            bool: 验证是否成功
        """
        import random

        # 指纹伪装已在浏览器初始化时设置，这里不需要额外设置

        # 首先检查是否已经验证成功
        if self._check_turnstile_success():
            logging.info("🎉 Turnstile 验证已完成！")
            return True

        retry_count = 0
        while retry_count < max_retries:
            retry_count += 1
            logging.info(f"🔄 Turnstile 验证尝试 {retry_count}/{max_retries}")

            try:
                # 查找 Turnstile 验证框
                challenge_check = None

                # 方法1: 查找 Auth0 V2 Captcha
                try:
                    challenge_check = (
                        self.tab.ele("#ulp-auth0-v2-captcha", timeout=3)
                        .child()
                        .shadow_root.ele("tag:iframe")
                        .ele("tag:body")
                        .sr("tag:input")
                    )
                    if challenge_check:
                        logging.info("🔍 找到 Auth0 V2 Captcha 验证框")
                except:
                    pass

                # 方法2: 查找标准 Cloudflare Turnstile
                if not challenge_check:
                    try:
                        challenge_check = (
                            self.tab.ele("@id=cf-turnstile", timeout=3)
                            .child()
                            .shadow_root.ele("tag:iframe")
                            .ele("tag:body")
                            .sr("tag:input")
                        )
                        if challenge_check:
                            logging.info("🔍 找到 Cloudflare Turnstile 验证框")
                    except:
                        pass

                # 如果找到验证框，点击它
                if challenge_check:
                    logging.info("🖱️ 点击 Turnstile 验证框...")

                    # 随机延迟（模拟人类行为）
                    time.sleep(random.uniform(1, 3))
                    challenge_check.click()
                    time.sleep(2)

                    # 截图记录点击后状态
                    self.tab.get_screenshot(path=f"fingerprint_turnstile_clicked_{int(time.time())}.png")

                    # 检查验证是否成功
                    if self._check_turnstile_success():
                        logging.info("🎉 Turnstile 验证成功！")
                        self.tab.get_screenshot(path=f"fingerprint_turnstile_success_{int(time.time())}.png")
                        return True
                else:
                    logging.info("ℹ️ 未找到 Turnstile 验证框，等待验证中...")

                    # 检查是否已经验证成功
                    if self._check_turnstile_success():
                        logging.info("🎉 Turnstile 验证已完成！")
                        return True

            except Exception as e:
                logging.debug(f"第 {retry_count} 次尝试失败: {e}")

            # 检查是否已经验证成功（即使点击失败）
            if self._check_turnstile_success():
                logging.info("🎉 Turnstile 验证已完成！")
                return True

            # 随机延迟后重试
            if retry_count < max_retries:
                delay = random.uniform(2, 4)
                logging.info(f"⏳ 等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)

        logging.warning("⚠️ Turnstile 验证未能确认成功，但继续流程")
        return True  # 保守地继续流程

    def _check_turnstile_success(self) -> bool:
        """
        检查 Turnstile 验证是否成功（复制原脚本逻辑）

        Returns:
            bool: 是否验证成功
        """
        try:
            # 方法1: 检查页面级别的成功标志
            page_indicators = [
                "@name=password",      # 密码页面
                "@data-index=0",       # 验证码页面
                "Account Settings",    # 账户设置页面
            ]

            for indicator in page_indicators:
                try:
                    element = self.tab.ele(indicator, timeout=1)
                    if element:
                        logging.info(f"✅ 检测到页面级验证成功标志: {indicator}")
                        return True
                except:
                    continue

            # 方法2: 检查 Turnstile iframe 内部的成功标志
            try:
                auth0_captcha = self.tab.ele("#ulp-auth0-v2-captcha", timeout=1)
                if auth0_captcha:
                    iframe_body = (
                        auth0_captcha
                        .child()
                        .shadow_root.ele("tag:iframe")
                        .ele("tag:body")
                    )

                    if iframe_body:
                        # 检查 iframe 内的成功标志
                        try:
                            success_element = iframe_body.sr("#success")
                            if success_element:
                                success_text = success_element.text if hasattr(success_element, 'text') else ""
                                if success_text and success_text.lower() == "success!":
                                    logging.info("✅ 检测到 #success 元素，文本为 'Success!'")
                                    return True
                        except:
                            pass

                        # 检查其他可能的成功标志
                        iframe_success_indicators = [
                            "tag:input[value*='success']",
                            "tag:input[data-success='true']",
                            ".success",
                            "@text()=Success!"
                        ]

                        for indicator in iframe_success_indicators:
                            try:
                                element = iframe_body.sr(indicator)
                                if element:
                                    element_text = element.text if hasattr(element, 'text') else ""
                                    element_value = element.attr("value") if hasattr(element, 'attr') else ""

                                    if (element_text and "success" in element_text.lower()) or \
                                       (element_value and "success" in element_value.lower()):
                                        logging.info(f"✅ 检测到 iframe 内成功标志: {indicator}")
                                        return True
                            except:
                                continue

                        # 检查 challenge input 的值变化
                        try:
                            challenge_input = iframe_body.sr("tag:input")
                            if challenge_input:
                                input_value = challenge_input.attr("value")
                                if input_value and len(input_value) > 10:
                                    logging.info("✅ Challenge input 值已生成，验证完成")
                                    return True
                        except:
                            pass

            except Exception as e:
                logging.debug(f"检查 iframe 内成功标志异常: {e}")

            return False

        except Exception as e:
            logging.debug(f"检查验证成功状态异常: {e}")
            return False

    def _handle_terms_agreement(self) -> bool:
        """
        处理条款同意页面 (复制原脚本逻辑)

        Returns:
            bool: 是否处理成功
        """
        try:
            logging.info("📋 检查是否跳转到条款同意页面...")

            # 等待页面跳转
            time.sleep(3)

            current_url = self.tab.url
            logging.info(f"🔍 当前 URL: {current_url}")

            # 检查是否在条款同意页面
            if "terms-accept" not in current_url:
                logging.info("ℹ️ 未跳转到条款同意页面，继续流程")
                return True

            logging.info("🔍 检测到条款同意页面 (terms-accept)")

            # 等待页面完全加载
            time.sleep(2)

            # 查找条款复选框
            terms_checkbox = self.tab.ele("#terms-of-service-checkbox", timeout=5)
            if not terms_checkbox:
                logging.warning("⚠️ 未找到条款同意复选框")
                return False

            # 勾选条款复选框
            is_checked = terms_checkbox.attr("checked") is not None
            logging.info(f"📋 条款复选框当前状态: {'已勾选' if is_checked else '未勾选'}")

            if not is_checked:
                logging.info("☑️ 勾选条款同意复选框...")

                # 方法1: 直接点击复选框
                terms_checkbox.click()
                time.sleep(1)

                # 检查是否勾选成功
                is_checked_after_click = terms_checkbox.attr("checked") is not None
                if is_checked_after_click:
                    logging.info("✅ 条款复选框勾选成功")
                else:
                    logging.warning("⚠️ 直接点击失败，尝试其他方法...")

                    # 方法2: 使用 JavaScript 设置 checked 属性并触发 change 事件
                    try:
                        self.tab.run_js("""
                            var checkbox = document.getElementById('terms-of-service-checkbox');
                            if (checkbox) {
                                checkbox.checked = true;
                                // 触发 change 事件
                                var event = new Event('change', { bubbles: true });
                                checkbox.dispatchEvent(event);
                                // 也尝试调用 onchange 函数
                                if (typeof updateSignupButton === 'function') {
                                    updateSignupButton(checkbox);
                                }
                            }
                        """)
                        time.sleep(1)

                        # 再次检查
                        is_checked_after_js = terms_checkbox.attr("checked") is not None
                        if is_checked_after_js:
                            logging.info("✅ JavaScript 方法勾选成功")
                        else:
                            logging.warning("⚠️ JavaScript 方法也失败")
                    except Exception as e:
                        logging.warning(f"⚠️ JavaScript 方法异常: {e}")
            else:
                logging.info("ℹ️ 条款复选框已勾选")

            # 点击注册按钮
            signup_button = self.tab.ele("#signup-button", timeout=3)
            if signup_button:
                # 检查按钮是否可用
                disabled_attr = signup_button.attr("disabled")
                is_disabled = disabled_attr is not None
                logging.info(f"🔍 注册按钮状态: {'禁用' if is_disabled else '启用'}")

                if is_disabled:
                    logging.warning("⚠️ 注册按钮仍然禁用，等待启用...")

                    # 等待按钮启用
                    for i in range(10):
                        time.sleep(1)
                        disabled_attr = signup_button.attr("disabled")
                        is_disabled = disabled_attr is not None
                        if not is_disabled:
                            logging.info(f"✅ 注册按钮在 {i+1} 秒后启用")
                            break

                    # 如果仍然禁用，尝试强制启用
                    if is_disabled:
                        try:
                            self.tab.run_js("""
                                var button = document.getElementById('signup-button');
                                if (button) {
                                    button.removeAttribute('disabled');
                                    button.disabled = false;
                                }
                            """)
                            time.sleep(1)
                        except Exception as e:
                            logging.warning(f"⚠️ 强制启用按钮失败: {e}")

                # 尝试点击按钮
                final_disabled_attr = signup_button.attr("disabled")
                final_disabled = final_disabled_attr is not None

                if not final_disabled:
                    logging.info("🖱️ 点击注册按钮...")

                    # 截图记录点击前状态
                    self.tab.get_screenshot(path=f"fingerprint_before_signup_click_{int(time.time())}.png")

                    # 尝试多种点击方法
                    try:
                        signup_button.click()
                        logging.info("✅ 直接点击成功")
                    except Exception as e:
                        logging.warning(f"⚠️ 直接点击失败: {e}，尝试 JavaScript 点击...")
                        try:
                            self.tab.run_js("document.getElementById('signup-button').click();")
                            logging.info("✅ JavaScript 点击成功")
                        except Exception as e2:
                            logging.error(f"❌ JavaScript 点击也失败: {e2}")
                            return False

                    time.sleep(3)

                    # 截图记录点击后状态
                    self.tab.get_screenshot(path=f"fingerprint_after_signup_click_{int(time.time())}.png")

                    logging.info("✅ 注册按钮点击成功")
                    return True
                else:
                    logging.error("❌ 注册按钮仍然禁用，无法点击")
                    return False
            else:
                logging.error("❌ 未找到注册按钮")
                return False

        except Exception as e:
            logging.error(f"❌ 处理条款同意失败: {e}")
            return False

    def step4_input_email(self) -> bool:
        """
        第四步：输入邮箱（复制原脚本逻辑）

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第四步：处理条款同意页面和输入邮箱...")

            # 首先检查是否需要处理条款同意页面
            current_url = self.tab.url
            logging.info(f"🔍 当前 URL: {current_url}")

            if "terms-accept" in current_url:
                logging.info("🔍 检测到条款同意页面，先处理条款同意...")
                if not self._handle_terms_agreement():
                    logging.error("❌ 条款同意处理失败")
                    return False

                # 等待页面跳转
                logging.info("⏳ 等待页面跳转到邮箱输入页面...")
                time.sleep(5)

                # 更新当前 URL
                current_url = self.tab.url
                logging.info(f"🔍 跳转后 URL: {current_url}")

            # 查找邮箱输入框
            email_input = None

            # 尝试多种选择器（按成功概率排序）
            email_selectors = [
                "#username",  # 原始脚本中成功的选择器
                "input[name=username]",
                "input[type=email]",
                "@type=email",
                "@placeholder*=email",
                "@name=email",
                "@id*=email"
            ]

            for selector in email_selectors:
                try:
                    if selector.startswith("#") or selector.startswith("input["):
                        email_input = self.tab.ele(selector, timeout=3)
                    else:
                        email_input = self.tab.ele(selector, timeout=3)

                    if email_input:
                        logging.info(f"✅ 找到邮箱输入框: {selector}")
                        break
                except:
                    continue

            if not email_input:
                logging.error("❌ 未找到邮箱输入框")
                return False

            # 输入邮箱
            email_input.clear()
            email_input.input(self.email)

            logging.info(f"✅ 邮箱输入成功: {self.email}")

            # 等待输入完成
            time.sleep(2)

            # 截图记录
            self.tab.get_screenshot(path=f"fingerprint_step4_email_input_{int(time.time())}.png")

            logging.info("✅ 第四步完成：邮箱输入成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第四步失败: {e}")
            return False

    def step5_click_continue(self) -> bool:
        """
        第五步：点击 Continue 按钮（复制原脚本逻辑）

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第五步：点击 Continue 按钮...")

            # 查找 Continue 按钮
            continue_button = None

            continue_texts = ["Continue", "Next", "Submit", "Proceed"]

            for text in continue_texts:
                try:
                    continue_button = self.tab.ele(f"@text()={text}", timeout=3)
                    if continue_button:
                        logging.info(f"✅ 找到按钮: {text}")
                        break
                except:
                    continue

            if not continue_button:
                logging.error("❌ 未找到 Continue 按钮")
                return False

            # 点击按钮
            continue_button.click()
            logging.info("✅ 成功点击 Continue 按钮")

            # 等待页面跳转
            time.sleep(5)

            # 截图记录
            self.tab.get_screenshot(path=f"fingerprint_step5_after_continue_{int(time.time())}.png")

            new_url = self.tab.url
            logging.info(f"🌐 跳转后 URL: {new_url}")

            logging.info("✅ 第五步完成：成功点击 Continue")
            return True

        except Exception as e:
            logging.error(f"❌ 第五步失败: {e}")
            return False

    def step6_handle_verification_code(self) -> bool:
        """
        第六步：处理验证码（复制原脚本逻辑）

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第六步：处理验证码...")

            # 等待邮件发送
            logging.info("⏳ 等待验证码邮件发送...")
            time.sleep(10)

            # 获取验证码
            try:
                from augmentcode_register import AugmentCodeRegister
                temp_register = AugmentCodeRegister(self.email)
                verification_code = temp_register.email_handler.get_augmentcode_verification_code(max_retries=5, retry_interval=30)

                if not verification_code:
                    logging.error("❌ 未获取到验证码")
                    return False

                logging.info(f"✅ 获取到验证码: {verification_code}")

            except Exception as e:
                logging.error(f"❌ 获取验证码失败: {e}")
                return False

            # 查找验证码输入框
            code_input = None

            code_selectors = [
                "#code",  # 原始脚本中成功的选择器
                "input[id=code]",
                "input[name=code]",
                "@type=text",
                "@placeholder*=code",
                "@placeholder*=verification",
                "@name*=code",
                "@id*=code"
            ]

            for selector in code_selectors:
                try:
                    if selector.startswith("#") or selector.startswith("input["):
                        code_input = self.tab.ele(selector, timeout=3)
                    else:
                        code_input = self.tab.ele(selector, timeout=3)

                    if code_input:
                        logging.info(f"✅ 找到验证码输入框: {selector}")
                        break
                except:
                    continue

            if not code_input:
                logging.error("❌ 未找到验证码输入框")
                return False

            # 输入验证码
            code_input.clear()
            code_input.input(verification_code)

            logging.info("✅ 验证码输入成功")
            time.sleep(2)

            # 提交验证码
            submit_button = None
            submit_texts = ["Continue", "Submit", "Verify", "Confirm"]

            for text in submit_texts:
                try:
                    submit_button = self.tab.ele(f"@text()={text}", timeout=3)
                    if submit_button:
                        logging.info(f"✅ 找到提交按钮: {text}")
                        break
                except:
                    continue

            if submit_button:
                submit_button.click()
                logging.info("✅ 验证码提交成功")
                time.sleep(5)
            else:
                logging.warning("⚠️ 未找到提交按钮，可能自动提交")

            # 截图记录
            self.tab.get_screenshot(path=f"fingerprint_step6_verification_complete_{int(time.time())}.png")

            logging.info("✅ 第六步完成：验证码处理成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第六步失败: {e}")
            return False

    def step7_final_terms_agreement(self) -> bool:
        """
        第七步：最终条款同意（验证码后）（复制原脚本逻辑）

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第七步：最终条款同意...")

            # 检查当前 URL
            current_url = self.tab.url
            logging.info(f"🔍 当前 URL: {current_url}")

            # 如果不在 terms-accept 页面，说明可能已经完成
            if "terms-accept" not in current_url:
                logging.info("ℹ️ 不在条款同意页面，可能已完成注册")
                return True

            logging.info("🔍 检测到最终条款同意页面")

            # 等待页面完全加载
            time.sleep(2)

            # 调用条款同意处理方法
            if self._handle_terms_agreement():
                logging.info("✅ 最终条款同意处理成功")

                # 等待页面跳转
                time.sleep(5)

                # 检查是否跳转成功
                final_url = self.tab.url
                logging.info(f"🔍 处理后 URL: {final_url}")

                if "terms-accept" not in final_url:
                    logging.info("🎉 成功跳转，注册流程完成！")
                    return True
                else:
                    logging.warning("⚠️ 仍在条款同意页面，可能需要手动检查")
                    return True  # 保守地认为成功
            else:
                logging.error("❌ 最终条款同意处理失败")
                return False

        except Exception as e:
            logging.error(f"❌ 第七步失败: {e}")
            return False

    def complete_registration(self) -> bool:
        """
        完成完整的注册流程（复制原脚本逻辑）

        Returns:
            bool: 注册是否成功
        """
        try:
            logging.info("🚀 开始完整的 AugmentCode 指纹伪装注册流程...")

            # 初始化浏览器
            if not self.init_browser():
                return False

            # 执行注册步骤
            steps = [
                ("访问促销页面", self.step1_visit_promotion_page),
                ("点击注册按钮", self.step2_click_register_button),
                ("处理人机验证", self.step3_handle_turnstile),
                ("输入邮箱", self.step4_input_email),
                ("点击继续", self.step5_click_continue),
                ("处理验证码", self.step6_handle_verification_code),
                ("最终条款同意", self.step7_final_terms_agreement),
            ]

            for step_name, step_func in steps:
                logging.info(f"\n{'='*20} {step_name} {'='*20}")

                if not step_func():
                    logging.error(f"❌ {step_name} 失败，注册中止")
                    return False

                logging.info(f"✅ {step_name} 成功")

            # 检查最终结果
            time.sleep(5)
            final_url = self.tab.url
            final_title = self.tab.title

            logging.info(f"🌐 最终 URL: {final_url}")
            logging.info(f"📄 最终标题: {final_title}")

            # 最终截图
            self.tab.get_screenshot(path=f"fingerprint_final_result_{int(time.time())}.png")

            # 检查成功标志
            success_indicators = [
                "welcome",
                "success",
                "congratulations",
                "account created",
                "registration complete",
                "dashboard"
            ]

            page_content = self.tab.html.lower()
            is_success = any(indicator in page_content for indicator in success_indicators)

            if is_success:
                logging.info("🎉 指纹伪装注册成功！")
                return True
            else:
                logging.info("ℹ️ 指纹伪装注册可能成功，请手动检查")
                return True  # 保守地认为成功

        except Exception as e:
            logging.error(f"❌ 指纹伪装注册流程失败: {e}")
            import traceback
            logging.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            # 保持浏览器打开供检查
            input("按 Enter 键关闭浏览器...")
            if self.browser_manager:
                self.browser_manager.quit()


def main():
    """主函数"""
    try:
        logging.info("=" * 80)
        logging.info("指纹伪装浏览器 - AugmentCode 注册测试")
        logging.info("=" * 80)

        # 检查环境
        proxy = os.getenv("BROWSER_PROXY", "")
        if not proxy:
            logging.warning("⚠️ 建议设置代理: set BROWSER_PROXY=127.0.0.1:1080")

        # 生成邮箱
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱: {email}")

        # 创建指纹伪装注册器
        register = FingerprintRegister(email)

        # 执行注册
        success = register.complete_registration()

        if success:
            logging.info("🎉 指纹伪装 AugmentCode 注册成功！")
            print(f"\n✅ 指纹伪装注册成功！")
            print(f"📧 邮箱: {email}")
            print(f"🔒 指纹伪装: 已启用所有14项参数")
        else:
            logging.error("😞 指纹伪装 AugmentCode 注册失败！")
            print(f"\n❌ 指纹伪装注册失败！")

        return success

    except Exception as e:
        logging.error(f"❌ 主程序执行失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
