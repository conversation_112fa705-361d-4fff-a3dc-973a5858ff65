#!/usr/bin/env python3
"""
测试 Success 标志检测
验证我们能正确检测到 iframe 内的 #success 元素
"""

import time
from logger import logging

def test_success_detection():
    """测试成功标志检测"""
    try:
        logging.info("🧪 测试 Success 标志检测...")
        
        from browser_utils import BrowserManager
        
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        tab = browser.latest_tab
        
        logging.info("✅ 浏览器初始化成功")
        
        # 访问页面
        logging.info("🌐 访问 Cursor 资源页面...")
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(3)
        
        # 点击按钮
        logging.info("🖱️ 点击 Get your free month 按钮...")
        button = tab.ele("@text()=Get your free month", timeout=5)
        if button:
            button.click()
            time.sleep(5)
            logging.info("✅ 按钮点击成功")
        else:
            logging.error("❌ 未找到按钮")
            return False
        
        # 检测 Turnstile
        auth0_captcha = tab.ele("#ulp-auth0-v2-captcha", timeout=5)
        if not auth0_captcha:
            logging.info("ℹ️ 未检测到 Turnstile")
            return True
        
        logging.info("🔍 检测到 Auth0 V2 Captcha")
        
        # 主动点击验证框（触发验证）
        try:
            challenge_check = (
                auth0_captcha
                .child()
                .shadow_root.ele("tag:iframe")
                .ele("tag:body")
                .sr("tag:input")
            )
            
            if challenge_check:
                logging.info("🖱️ 找到验证框，主动点击...")
                challenge_check.click()
                time.sleep(2)
                logging.info("✅ 验证框点击成功")
            else:
                logging.info("ℹ️ 未找到验证框")
                
        except Exception as e:
            logging.info(f"ℹ️ 点击验证框异常: {e}")
        
        # 等待并检测成功标志
        logging.info("⏳ 等待验证完成，检测成功标志...")
        
        for i in range(30):  # 等待30秒
            time.sleep(1)
            
            try:
                # 访问 iframe 内部
                iframe_body = (
                    auth0_captcha
                    .child()
                    .shadow_root.ele("tag:iframe")
                    .ele("tag:body")
                )
                
                if iframe_body:
                    # 检查 #success 元素
                    try:
                        success_element = iframe_body.sr("#success")
                        if success_element:
                            success_text = success_element.text
                            logging.info(f"🔍 #success 元素文本: '{success_text}'")
                            
                            if success_text and success_text.lower() == "success!":
                                logging.info("🎉 检测到 Success! 标志，验证完成！")
                                tab.get_screenshot(path=f"success_detected_{int(time.time())}.png")
                                
                                # 等待一下让用户看到结果
                                input("按 Enter 键继续...")
                                return True
                    except:
                        pass
                    
                    # 检查 challenge input 值
                    try:
                        challenge_input = iframe_body.sr("tag:input")
                        if challenge_input:
                            input_value = challenge_input.attr("value")
                            if input_value and len(input_value) > 5:
                                logging.info(f"🔍 Challenge input 值: '{input_value}' (长度: {len(input_value)})")
                    except:
                        pass
                        
            except Exception as e:
                logging.debug(f"检查异常: {e}")
            
            if i % 5 == 0 and i > 0:
                logging.info(f"⏳ 等待验证完成... ({i}/30)")
        
        logging.warning("⚠️ 30秒内未检测到成功标志")
        tab.get_screenshot(path=f"no_success_detected_{int(time.time())}.png")
        
        input("按 Enter 键关闭浏览器...")
        return False
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        try:
            browser_manager.quit()
        except:
            pass

def main():
    """主函数"""
    logging.info("=" * 80)
    logging.info("Success 标志检测测试")
    logging.info("=" * 80)
    
    success = test_success_detection()
    
    if success:
        logging.info("🎉 Success 标志检测测试成功！")
    else:
        logging.error("❌ Success 标志检测测试失败！")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
