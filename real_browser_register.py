#!/usr/bin/env python3
"""
使用真实浏览器环境的 AugmentCode 注册器
解决浏览器指纹检测问题
"""

import os
import sys
import time
from logger import logging
from augmentcode_register import generate_email_with_timestamp

class RealBrowserRegister:
    """使用真实浏览器的注册器"""
    
    def __init__(self, email: str):
        """
        初始化注册器
        
        Args:
            email: 注册邮箱
        """
        self.email = email
        self.tab = None
        self.browser_manager = None
    
    def init_real_browser(self) -> bool:
        """
        初始化真实浏览器环境（参考 Cursor 成功经验）

        Returns:
            bool: 初始化是否成功
        """
        try:
            logging.info("🚀 初始化浏览器环境（参考 Cursor 方案）...")

            # 使用 Cursor 相同的导入方式
            from DrissionPage import ChromiumOptions, Chromium
            from browser_utils import BrowserManager

            # 方案1：直接使用 Cursor 的 BrowserManager
            try:
                logging.info("🔄 尝试使用 Cursor 的 BrowserManager...")
                browser_manager = BrowserManager()
                self.browser = browser_manager.init_browser()
                self.tab = self.browser.latest_tab

                logging.info("✅ 使用 Cursor BrowserManager 成功")
                return True

            except Exception as e:
                logging.warning(f"⚠️ Cursor BrowserManager 失败: {e}")
                logging.info("🔄 回退到自定义配置...")

            # 方案2：参考 Cursor 的配置方式
            co = ChromiumOptions()

            # 1. 加载 turnstilePatch 扩展（Cursor 的关键成功因素）
            extension_path = self._get_extension_path()
            if extension_path and os.path.exists(extension_path):
                co.add_extension(extension_path)
                logging.info(f"✅ 加载 turnstilePatch 扩展: {extension_path}")
            else:
                logging.warning("⚠️ turnstilePatch 扩展不存在，这可能是验证失败的原因")

            # 2. 浏览器路径配置
            browser_path = os.getenv("BROWSER_PATH")
            if browser_path and os.path.exists(browser_path):
                co.set_paths(browser_path=browser_path)
                logging.info(f"✅ 使用环境变量浏览器: {browser_path}")
            else:
                # 使用用户修改的 Chrome Dev 路径
                chrome_path = r"C:\Program Files\Google\Chrome Dev\Application\chrome.exe"
                if os.path.exists(chrome_path):
                    co.set_paths(browser_path=chrome_path)
                    logging.info(f"✅ 使用 Chrome Dev: {chrome_path}")
                else:
                    logging.warning("⚠️ 未找到指定的 Chrome 浏览器")

            # 3. 代理配置（Cursor 的重要配置）
            proxy = os.getenv("BROWSER_PROXY", "127.0.0.1:1080")
            if proxy:
                co.set_proxy(proxy)
                logging.info(f"✅ 配置代理: {proxy}")

            # 4. Cursor 的基本配置
            co.set_pref("credentials_enable_service", False)
            co.set_argument("--hide-crash-restore-bubble")
            co.auto_port()

            # 5. 无头模式配置（Cursor 默认使用）
            headless = os.getenv("BROWSER_HEADLESS", "False").lower() == "true"
            co.headless(headless)
            logging.info(f"✅ 无头模式: {headless}")

            # 6. Mac 系统特殊处理（来自 Cursor）
            if sys.platform == "darwin":
                co.set_argument("--no-sandbox")
                co.set_argument("--disable-gpu")

            # 7. 创建浏览器实例（使用 Chromium 而不是 ChromiumPage）
            self.browser = Chromium(co)
            self.tab = self.browser.latest_tab

            logging.info("✅ 浏览器环境初始化成功（Cursor 风格）")
            return True

        except Exception as e:
            logging.error(f"❌ 初始化浏览器失败: {e}")
            import traceback
            logging.error(f"错误详情: {traceback.format_exc()}")
            return False
    
    def _get_extension_path(self) -> str:
        """获取 turnstilePatch 扩展路径"""
        try:
            root_dir = os.getcwd()
            extension_path = os.path.join(root_dir, "turnstilePatch")
            return extension_path if os.path.exists(extension_path) else None
        except:
            return None
    
    def test_turnstile_with_cursor_method(self) -> bool:
        """
        使用 Cursor 的方法测试 Turnstile 验证

        Returns:
            bool: 测试是否成功
        """
        try:
            logging.info("🧪 使用 Cursor 方法测试 Turnstile...")

            # 访问 AugmentCode 登录页面
            self.tab.get("https://login.augmentcode.com/u/login/identifier")
            time.sleep(5)

            # 截图记录
            screenshot_path = f"cursor_method_test_{int(time.time())}.png"
            self.tab.get_screenshot(path=screenshot_path)
            logging.info(f"📸 测试截图已保存: {screenshot_path}")

            # 检查是否有 Turnstile 验证
            try:
                # 使用 Cursor 的 Turnstile 检测方法
                auth0_captcha = self.tab.ele("#ulp-auth0-v2-captcha", timeout=5)
                if auth0_captcha:
                    logging.info("🔍 检测到 Auth0 V2 Captcha")

                    # 尝试 Cursor 的 shadow DOM 访问方法
                    try:
                        challenge_check = (
                            auth0_captcha
                            .child()
                            .shadow_root.ele("tag:iframe")
                            .ele("tag:body")
                            .sr("tag:input")
                        )

                        if challenge_check:
                            logging.info("✅ 成功访问 Turnstile shadow DOM")
                            return True
                        else:
                            logging.info("ℹ️ Turnstile 可能已自动完成")
                            return True

                    except Exception as e:
                        logging.info(f"ℹ️ Shadow DOM 访问异常（可能正常）: {e}")
                        return True
                else:
                    logging.info("ℹ️ 未检测到 Turnstile 验证")
                    return True

            except Exception as e:
                logging.info(f"ℹ️ Turnstile 检测异常（可能正常）: {e}")
                return True

        except Exception as e:
            logging.error(f"❌ Cursor 方法测试失败: {e}")
            return False
    
    def register_with_cursor_method(self) -> bool:
        """
        使用 Cursor 的方法进行注册

        Returns:
            bool: 注册是否成功
        """
        try:
            logging.info("🚀 使用 Cursor 方法开始注册...")

            # 1. 初始化浏览器
            if not self.init_real_browser():
                return False

            # 2. 测试 Turnstile 环境
            if not self.test_turnstile_with_cursor_method():
                logging.warning("⚠️ Turnstile 环境测试异常，但继续尝试...")

            # 3. 访问 AugmentCode Cursor 资源页面
            logging.info("📋 访问 AugmentCode Cursor 资源页面...")
            self.tab.get("https://www.augmentcode.com/resources/cursor")
            time.sleep(3)

            # 截图记录
            self.tab.get_screenshot(path=f"step1_homepage_{int(time.time())}.png")

            # 4. 查找并点击注册按钮
            logging.info("� 查找注册按钮...")

            # 尝试多种可能的注册按钮（针对 Cursor 促销页面）
            register_selectors = [
                "Get your free month",
                "Claim offer",
                "Get started",
                "Continue",
                "Sign up",
                "Register",
                "Start free trial"
            ]

            button_found = False
            for selector in register_selectors:
                try:
                    button = self.tab.ele(f"@text()={selector}", timeout=2)
                    if button:
                        button.click()
                        logging.info(f"✅ 点击了按钮: {selector}")
                        button_found = True
                        break
                except:
                    continue

            if not button_found:
                logging.error("❌ 未找到注册按钮")
                return False

            # 等待页面跳转
            time.sleep(5)
            self.tab.get_screenshot(path=f"step2_after_click_{int(time.time())}.png")

            # 5. 处理 Turnstile 验证（使用 Cursor 方法）
            logging.info("📋 处理 Turnstile 验证...")

            # 等待 Turnstile 自动完成（在正确环境下应该自动通过）
            time.sleep(10)

            # 检查是否有 Continue 按钮
            try:
                continue_btn = self.tab.ele("@text()=Continue", timeout=5)
                if continue_btn:
                    continue_btn.click()
                    logging.info("✅ 点击 Continue 按钮")
                    time.sleep(3)
            except:
                logging.info("ℹ️ 未找到 Continue 按钮，可能已自动跳转")

            self.tab.get_screenshot(path=f"step3_after_captcha_{int(time.time())}.png")

            # 6. 输入邮箱
            logging.info("📋 输入邮箱...")

            try:
                email_input = self.tab.ele("@type=email", timeout=5)
                if not email_input:
                    email_input = self.tab.ele("@placeholder*=email", timeout=5)

                if email_input:
                    email_input.input(self.email)
                    logging.info(f"✅ 邮箱输入成功: {self.email}")
                    time.sleep(2)
                else:
                    logging.error("❌ 未找到邮箱输入框")
                    return False

            except Exception as e:
                logging.error(f"❌ 邮箱输入失败: {e}")
                return False

            self.tab.get_screenshot(path=f"step4_email_input_{int(time.time())}.png")

            # 7. 点击 Continue
            try:
                continue_btn = self.tab.ele("@text()=Continue", timeout=5)
                if continue_btn:
                    continue_btn.click()
                    logging.info("✅ 点击 Continue 成功")
                    time.sleep(5)
                else:
                    logging.error("❌ 未找到 Continue 按钮")
                    return False
            except Exception as e:
                logging.error(f"❌ 点击 Continue 失败: {e}")
                return False

            self.tab.get_screenshot(path=f"step5_after_continue_{int(time.time())}.png")

            # 8. 获取验证码
            logging.info("📋 获取验证码...")

            # 等待邮件发送
            time.sleep(10)

            # 使用现有的邮件处理器
            try:
                from augmentcode_register import AugmentCodeRegister
                temp_register = AugmentCodeRegister(self.email)
                verification_code = temp_register.email_handler.get_verification_code()

                if verification_code:
                    logging.info(f"✅ 获取到验证码: {verification_code}")

                    # 输入验证码
                    code_input = self.tab.ele("@type=text", timeout=5)
                    if not code_input:
                        code_input = self.tab.ele("@placeholder*=code", timeout=5)

                    if code_input:
                        code_input.input(verification_code)
                        logging.info("✅ 验证码输入成功")
                        time.sleep(2)

                        # 提交验证码
                        submit_btn = self.tab.ele("@text()=Continue", timeout=5)
                        if not submit_btn:
                            submit_btn = self.tab.ele("@text()=Submit", timeout=5)

                        if submit_btn:
                            submit_btn.click()
                            logging.info("✅ 验证码提交成功")
                            time.sleep(5)

                            self.tab.get_screenshot(path=f"step6_final_{int(time.time())}.png")

                            logging.info("🎉 注册流程完成！")
                            return True
                        else:
                            logging.error("❌ 未找到提交按钮")
                            return False
                    else:
                        logging.error("❌ 未找到验证码输入框")
                        return False
                else:
                    logging.error("❌ 未获取到验证码")
                    return False

            except Exception as e:
                logging.error(f"❌ 验证码处理失败: {e}")
                return False

        except Exception as e:
            logging.error(f"❌ Cursor 方法注册失败: {e}")
            import traceback
            logging.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            # 保持浏览器打开供检查
            input("按 Enter 键关闭浏览器...")

def main():
    """主函数"""
    try:
        logging.info("=" * 80)
        logging.info("使用 Cursor 方法的 AugmentCode 注册")
        logging.info("=" * 80)

        # 生成邮箱
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱: {email}")

        # 创建注册器
        register = RealBrowserRegister(email)

        # 执行注册（使用 Cursor 方法）
        success = register.register_with_cursor_method()

        if success:
            logging.info("🎉 使用 Cursor 方法注册成功！")
            print(f"\n✅ 注册成功！")
            print(f"📧 邮箱: {email}")
        else:
            logging.error("😞 使用 Cursor 方法注册失败！")
            print(f"\n❌ 注册失败！")

        return success

    except Exception as e:
        logging.error(f"❌ 主程序执行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
