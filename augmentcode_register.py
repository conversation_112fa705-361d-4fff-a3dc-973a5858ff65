import os
import time
import random
import re
from typing import Optional
from logger import logging
from browser_utils import BrowserManager
from anti_detection_browser import AntiDetectionBrowserManager
from get_email_code import EmailVerificationHandler
from augmentcode_email_handler import Augment<PERSON>odeEmailHandler
from config import Config
from language import get_translation


class AugmentCodeRegister:
    """AugmentCode 注册处理器"""
    
    def __init__(self, email: str):
        """
        初始化注册器
        
        Args:
            email: 注册邮箱
        """
        self.email = email
        self.browser_manager = None
        self.tab = None
        self.email_handler = AugmentCodeEmailHandler(email)
        
        # 注册流程相关URL
        self.start_url = "https://www.augmentcode.com/resources/cursor"
        self.promotion_url = "https://app.augmentcode.com/promotions/cursor"

    def _apply_us_region_settings(self) -> None:
        """
        应用美国地区设置和增强的反检测指纹（基于用户反馈优化）
        """
        try:
            logging.info("应用美国地区设置和增强反检测指纹...")

            # 增强的反检测 JavaScript 代码
            js_code = """
            // 1. 修改浏览器语言和地区设置
            Object.defineProperty(navigator, 'language', {
                get: function() { return 'en-US'; }
            });

            Object.defineProperty(navigator, 'languages', {
                get: function() { return ['en-US', 'en']; }
            });

            // 2. 修改时区
            Object.defineProperty(Intl.DateTimeFormat.prototype, 'resolvedOptions', {
                value: function() {
                    return {
                        locale: 'en-US',
                        timeZone: 'America/New_York',
                        calendar: 'gregory',
                        numberingSystem: 'latn'
                    };
                }
            });

            // 3. 模拟美国地理位置
            Object.defineProperty(navigator.geolocation, 'getCurrentPosition', {
                value: function(success, error, options) {
                    success({
                        coords: {
                            latitude: 40.7128,  // 纽约纬度
                            longitude: -74.0060, // 纽约经度
                            accuracy: 10
                        }
                    });
                }
            });

            // 4. 增强的屏幕指纹修改（基于用户反馈）
            Object.defineProperty(screen, 'width', {
                get: function() { return 1920; }
            });
            Object.defineProperty(screen, 'height', {
                get: function() { return 1080; }
            });
            Object.defineProperty(screen, 'availWidth', {
                get: function() { return 1920; }
            });
            Object.defineProperty(screen, 'availHeight', {
                get: function() { return 1040; }
            });

            // 5. 修改 WebGL 指纹（基于用户提供的具体信息）
            const getParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
                    return 'Google Inc. (AMD)';
                }
                if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
                    return 'ANGLE (AMD, AMD Radeon(TM) R5 Graphics Direct3D9Ex vs_3_0 ps_3_0, aticfx64.dll)';
                }
                return getParameter.call(this, parameter);
            };

            // 6. 修改 Canvas 指纹（添加噪声）
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function() {
                const context = this.getContext('2d');
                if (context) {
                    // 添加微小的随机噪声
                    const imageData = context.getImageData(0, 0, this.width, this.height);
                    for (let i = 0; i < imageData.data.length; i += 4) {
                        imageData.data[i] += Math.floor(Math.random() * 3) - 1;
                    }
                    context.putImageData(imageData, 0, 0);
                }
                return originalToDataURL.apply(this, arguments);
            };

            // 7. 隐藏 WebRTC（防止真实 IP 泄露）
            Object.defineProperty(navigator, 'mediaDevices', {
                get: function() { return undefined; }
            });

            // 8. 修改字体指纹（模拟常见的美国系统字体）
            Object.defineProperty(document, 'fonts', {
                get: function() {
                    return {
                        check: function() { return true; },
                        load: function() { return Promise.resolve(); },
                        ready: Promise.resolve()
                    };
                }
            });

            console.log('增强的美国地区设置和反检测指纹已应用');
            console.log('语言:', navigator.language);
            console.log('语言列表:', navigator.languages);
            console.log('屏幕尺寸:', screen.width + 'x' + screen.height);
            """

            self.tab.run_js(js_code)
            logging.info("增强的美国地区设置和反检测指纹应用成功")

        except Exception as e:
            logging.warning(f"应用增强反检测设置失败: {e}")
            # 不影响主流程，继续执行
        
    def save_screenshot(self, stage: str, timestamp: bool = True) -> None:
        """
        保存页面截图
        
        Args:
            stage: 截图阶段标识
            timestamp: 是否添加时间戳
        """
        try:
            screenshot_dir = "screenshots"
            if not os.path.exists(screenshot_dir):
                os.makedirs(screenshot_dir)
                
            if timestamp:
                filename = f"augmentcode_{stage}_{int(time.time())}.png"
            else:
                filename = f"augmentcode_{stage}.png"
                
            filepath = os.path.join(screenshot_dir, filename)
            self.tab.get_screenshot(filepath)
            logging.debug(f"截图已保存: {filepath}")
        except Exception as e:
            logging.warning(f"保存截图失败: {str(e)}")
    
    def save_shadow_root_html(self, filename_prefix: str = "shadow_root"):
        """保存包含 shadow-root 内容的完整页面HTML"""
        try:
            # 保存完整页面HTML
            timestamp = int(time.time())
            html_filename = f"{filename_prefix}_{timestamp}.html"

            with open(html_filename, "w", encoding="utf-8") as f:
                f.write(self.tab.html)

            logging.info(f"已保存完整页面HTML到: {html_filename}")

            # 尝试获取 shadow-root 内容
            shadow_info_filename = f"{filename_prefix}_shadow_info_{timestamp}.txt"
            with open(shadow_info_filename, "w", encoding="utf-8") as f:
                f.write("=== Shadow Root 分析 ===\n\n")

                # 查找所有可能包含 shadow-root 的元素
                shadow_selectors = [
                    "#ulp-auth0-v2-captcha",
                    "[data-captcha-provider='auth0_v2']",
                    ".ulp-captcha",
                    "iframe[src*='turnstile']",
                    "iframe[src*='cloudflare']"
                ]

                for selector in shadow_selectors:
                    try:
                        element = self.tab.ele(selector, timeout=1)
                        if element:
                            f.write(f"找到元素: {selector}\n")
                            f.write(f"元素HTML: {element.html}\n")
                            f.write(f"元素属性: {element.attrs}\n")

                            # 尝试获取 shadow-root 内容
                            try:
                                if hasattr(element, 'shadow_root'):
                                    shadow_root = element.shadow_root
                                    if shadow_root:
                                        f.write(f"Shadow Root HTML: {shadow_root.html}\n")

                                        # 查找 shadow-root 中的 iframe
                                        iframes = shadow_root.eles("iframe")
                                        for i, iframe in enumerate(iframes):
                                            f.write(f"Shadow Root iframe {i+1}: {iframe.html}\n")
                                            f.write(f"Shadow Root iframe {i+1} src: {iframe.attr('src')}\n")

                                            # 尝试获取 iframe 内容
                                            try:
                                                iframe_body = iframe.ele("body")
                                                if iframe_body:
                                                    f.write(f"Shadow Root iframe {i+1} body: {iframe_body.html}\n")

                                                    # 查找复选框
                                                    checkboxes = iframe_body.eles("input[type=checkbox]")
                                                    for j, checkbox in enumerate(checkboxes):
                                                        f.write(f"Shadow Root iframe {i+1} checkbox {j+1}: {checkbox.html}\n")
                                            except Exception as e:
                                                f.write(f"获取 iframe 内容失败: {str(e)}\n")
                                    else:
                                        f.write("Shadow Root 为空\n")
                                else:
                                    f.write("元素没有 shadow_root 属性\n")
                            except Exception as e:
                                f.write(f"获取 shadow-root 失败: {str(e)}\n")

                            f.write("\n" + "="*50 + "\n\n")
                    except Exception as e:
                        f.write(f"选择器 '{selector}' 失败: {str(e)}\n")

            logging.info(f"已保存 Shadow Root 分析到: {shadow_info_filename}")

        except Exception as e:
            logging.error(f"保存 shadow-root HTML 失败: {str(e)}")

    def _log_turnstile_debug_info(self, stage: str) -> None:
        """
        记录 Turnstile 调试信息

        Args:
            stage: 调试阶段标识
        """
        try:
            logging.info(f"=== Turnstile 调试信息 ({stage}) ===")

            # 1. 检查页面基本信息
            current_url = self.tab.url
            page_title = self.tab.title
            logging.info(f"当前页面: {current_url}")
            logging.info(f"页面标题: {page_title}")

            # 2. 检查 Turnstile 相关元素
            turnstile_elements = []

            # 检查 cf-turnstile
            try:
                cf_turnstile = self.tab.ele("@id=cf-turnstile", timeout=1)
                if cf_turnstile:
                    turnstile_elements.append(f"cf-turnstile: 存在")
                else:
                    turnstile_elements.append(f"cf-turnstile: 不存在")
            except:
                turnstile_elements.append(f"cf-turnstile: 查找失败")

            # 检查 auth0 captcha
            try:
                auth0_captcha = self.tab.ele("#ulp-auth0-v2-captcha", timeout=1)
                if auth0_captcha:
                    turnstile_elements.append(f"auth0-v2-captcha: 存在")
                    # 检查是否有内容
                    inner_html = auth0_captcha.inner_html
                    if inner_html and inner_html.strip():
                        turnstile_elements.append(f"auth0-v2-captcha 内容: 有内容 ({len(inner_html)} 字符)")
                    else:
                        turnstile_elements.append(f"auth0-v2-captcha 内容: 空")
                else:
                    turnstile_elements.append(f"auth0-v2-captcha: 不存在")
            except:
                turnstile_elements.append(f"auth0-v2-captcha: 查找失败")

            # 3. 检查 Continue 按钮状态
            try:
                continue_btn = self.tab.ele('//button[contains(text(), "Continue")]', timeout=1)
                if continue_btn:
                    is_enabled = not continue_btn.attr("disabled")
                    turnstile_elements.append(f"Continue 按钮: 存在, 可用={is_enabled}")
                else:
                    turnstile_elements.append(f"Continue 按钮: 不存在")
            except:
                turnstile_elements.append(f"Continue 按钮: 查找失败")

            # 4. 检查页面中的 JavaScript 错误
            try:
                js_result = self.tab.run_js("""
                    return {
                        turnstileLoaded: typeof turnstile !== 'undefined',
                        turnstileWidgets: typeof turnstile !== 'undefined' ? Object.keys(turnstile).length : 0,
                        pageErrors: window.errors || [],
                        readyState: document.readyState
                    };
                """)
                turnstile_elements.append(f"JavaScript 状态: {js_result}")
            except Exception as e:
                turnstile_elements.append(f"JavaScript 检查失败: {e}")

            # 输出所有调试信息
            for info in turnstile_elements:
                logging.info(f"  {info}")

            logging.info(f"=== Turnstile 调试信息结束 ({stage}) ===")

        except Exception as e:
            logging.error(f"记录 Turnstile 调试信息失败: {e}")

    def handle_turnstile_verification(self, max_retries: int = 3) -> bool:
        """
        处理 Turnstile 人机验证
        简化逻辑：要么点中，要么不中

        Args:
            max_retries: 最大重试次数

        Returns:
            bool: 验证是否成功
        """
        logging.info("开始处理 Turnstile 人机验证（使用 Cursor 验证成功的方法）...")

        # 1. 重置 Turnstile（参考 Cursor 的成功方法）
        try:
            self.tab.run_js("try { turnstile.reset() } catch(e) { console.log('Turnstile reset failed:', e); }")
            logging.info("已执行 Turnstile 重置")
        except Exception as e:
            logging.warning(f"Turnstile 重置失败: {e}")

        self.save_screenshot("turnstile_start")

        # 保存当前页面的 shadow-root 内容用于分析
        self.save_shadow_root_html("turnstile_analysis")

        try:
            # 等待页面完全加载
            time.sleep(3)

            # 查找 Turnstile 验证框
            challenge_check = None

            # 方法1: 标准 cf-turnstile（使用 Cursor 的成功方法）
            try:
                cf_turnstile = self.tab.ele("@id=ulp-auth0-v2-captcha", timeout=2)
                if cf_turnstile:
                    logging.info("找到标准 ulp-auth0-v2-captcha 元素")
                    try:
                        challenge_check = (
                            cf_turnstile
                            .child()
                            .shadow_root.ele("tag:iframe")
                            .ele("tag:body")
                            .sr("tag:input")
                        )
                        if challenge_check:
                            logging.info("在标准 cf-turnstile 中找到输入元素")
                    except Exception as e:
                        logging.debug(f"在标准 cf-turnstile 中查找输入元素失败: {str(e)}")
            except Exception as e:
                logging.debug(f"未找到标准 cf-turnstile: {str(e)}")

            # 备用方法2: 如果简化方法失败，使用复杂的嵌套结构（保留原逻辑但不作为主要方法）
            if not challenge_check:
                logging.info("简化方法失败，尝试复杂的嵌套结构...")
                # 这里保留原来的复杂逻辑作为备用，但现在我们已经有了成功的简化方法

            # 如果找到复选框，尝试点击
            if challenge_check:
                logging.info("找到 Turnstile 复选框，准备点击...")

                # 详细记录点击前的状态
                self._log_turnstile_debug_info("点击前")

                try:
                    # 记录点击元素的详细信息
                    logging.info(f"点击元素信息: tag={challenge_check.tag}, type={getattr(challenge_check, 'type', 'N/A')}")
                    logging.info(f"点击元素位置: {challenge_check.rect}")

                    # 执行点击
                    challenge_check.click()
                    logging.info("✅ 已点击 Turnstile 复选框")

                    # 短暂等待点击生效
                    time.sleep(1)

                    # 详细记录点击后的状态
                    self._log_turnstile_debug_info("点击后")
                    self.save_screenshot("turnstile_clicked")

                    # 等待验证完成 - 15秒超时检测（增加等待时间）
                    logging.info("等待 Turnstile 验证完成...")
                    max_check_time = 15  # 最多检测15秒
                    check_interval = 1   # 每1秒检查一次
                    checked_time = 0

                    while checked_time < max_check_time:
                        time.sleep(check_interval)
                        checked_time += check_interval

                        # 详细检查验证状态
                        success_status = self.check_turnstile_success()
                        logging.info(f"验证检测中... {checked_time}/{max_check_time}s - 状态: {success_status}")

                        if success_status:
                            logging.info(f"🎉 Turnstile 验证成功（用时 {checked_time}s）")
                            self.save_screenshot("turnstile_success")
                            self._log_turnstile_debug_info("验证成功后")
                            return True

                    logging.warning(f"⚠️ Turnstile 验证超时（{max_check_time}s），视为失败")
                    self._log_turnstile_debug_info("验证超时后")
                    self.save_screenshot("turnstile_timeout")
                    return False

                except Exception as e:
                    logging.error(f"❌ 点击 Turnstile 复选框失败: {str(e)}")
                    self._log_turnstile_debug_info("点击失败后")
                    self.save_screenshot("turnstile_click_failed")
                    return False
            else:
                logging.warning("未找到 Turnstile 复选框")
                # 检查是否已经验证成功
                if self.check_turnstile_success():
                    logging.info("Turnstile 可能已经自动验证成功")
                    return True
                else:
                    logging.error("未找到 Turnstile 复选框且验证未成功")
                    return False

        except Exception as e:
            logging.error(f"Turnstile 验证过程出错: {str(e)}")
            return False

    def check_turnstile_success(self):
        """
        检查 Turnstile 验证是否成功（增强调试版本）

        Returns:
            bool: 验证是否成功
        """
        try:
            logging.info("🔍 开始检查 Turnstile 验证状态...")

            # 1. 首先检查 Continue 按钮是否可用 - 这是最直接的成功指示器
            continue_button_selectors = [
                "button[data-action-button-primary='true']",  # 主要的 Continue 按钮
                "button[type='submit'][name='action'][value='default']",  # 具体的提交按钮
                "button._button-login-id",  # 登录页面的按钮类
                "//button[contains(text(), 'Continue')]",  # XPath 查找包含 Continue 文本的按钮
                "button[type='submit']",  # 通用提交按钮
            ]

            button_found = False
            for i, selector in enumerate(continue_button_selectors):
                try:
                    if selector.startswith("//"):
                        button = self.tab.ele(f"xpath:{selector}", timeout=1)
                    else:
                        button = self.tab.ele(selector, timeout=1)

                    if button:
                        button_found = True
                        button_text = button.text.strip() if button.text else "No text"
                        is_disabled = button.attr('disabled') or button.attr('aria-disabled') == 'true'
                        button_class = button.attr('class') or "No class"

                        logging.info(f"  按钮 {i+1}: {selector}")
                        logging.info(f"    文本: '{button_text}'")
                        logging.info(f"    禁用: {is_disabled}")
                        logging.info(f"    类名: {button_class}")

                        # 如果按钮存在且不被禁用，说明验证成功
                        if not is_disabled:
                            logging.info("✅ Continue 按钮可用，Turnstile 验证成功")
                            return True
                except Exception as e:
                    logging.debug(f"检查按钮 {selector} 失败: {str(e)}")
                    continue

            if not button_found:
                logging.warning("⚠️ 未找到任何 Continue 按钮")

            # 2. 检查隐藏的 captcha 输入框是否有值
            logging.info("🔍 检查 captcha 输入框...")
            try:
                captcha_inputs = [
                    "input[name='captcha']",
                    "input[name='cf-turnstile-response']",
                    "input[type='hidden'][name*='captcha']",
                    "input[type='hidden'][name*='turnstile']"
                ]

                for selector in captcha_inputs:
                    captcha_input = self.tab.ele(selector, timeout=1)
                    if captcha_input:
                        captcha_value = captcha_input.attr('value') or ""
                        logging.info(f"  找到 captcha 输入框: {selector}")
                        logging.info(f"  值: '{captcha_value[:50]}...' (长度: {len(captcha_value)})")
                        if captcha_value and captcha_value.strip():
                            logging.info("✅ Captcha 输入框有值，验证成功")
                            return True

                logging.info("  未找到有效的 captcha 输入框")
            except Exception as e:
                logging.warning(f"检查 captcha 输入框失败: {str(e)}")

            # 3. 检查 Turnstile 内部状态（如果存在 cf-turnstile 元素）
            logging.info("🔍 检查 cf-turnstile 状态...")
            try:
                turnstile_frame = self.tab.ele("@id=cf-turnstile", timeout=1)
                if turnstile_frame:
                    logging.info("  找到 cf-turnstile 元素")
                    # 检查 shadow DOM 内的复选框状态
                    try:
                        checkbox = (
                            turnstile_frame.child()
                            .shadow_root.ele("tag:iframe")
                            .ele("tag:body")
                            .sr("tag:input")
                        )
                        if checkbox:
                            is_checked = checkbox.attr("checked")
                            checkbox_type = checkbox.attr("type")
                            logging.info(f"  找到复选框: type={checkbox_type}, checked={is_checked}")
                            if is_checked:
                                logging.info("✅ Turnstile 复选框已勾选")
                                return True
                        else:
                            logging.info("  未找到复选框")
                    except Exception as e:
                        logging.warning(f"  访问 cf-turnstile shadow DOM 失败: {e}")
                else:
                    logging.info("  未找到 cf-turnstile 元素")
            except Exception as e:
                logging.warning(f"检查 cf-turnstile 失败: {str(e)}")

            # 4. 检查页面是否已经跳转到下一步
            logging.info("🔍 检查页面跳转状态...")
            current_url = self.tab.url
            if "identifier" not in current_url and "captcha" not in current_url:
                logging.info(f"✅ 页面已跳转，可能验证成功: {current_url}")
                return True

            logging.info("❌ 未找到 Turnstile 验证成功的指示器")
            return False

        except Exception as e:
            logging.error(f"检查 Turnstile 成功状态时出错: {str(e)}")
            return False
    
    def step1_click_get_free_month(self) -> bool:
        """
        第一步：访问页面并点击 Get your free month 按钮
        
        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("第一步：访问 AugmentCode 页面...")
            self.tab.get(self.start_url)
            time.sleep(random.uniform(2, 4))
            self.save_screenshot("step1_page_loaded")
            
            # 点击 Get your free month 按钮
            button_xpath = '/html/body/main[1]/div/section/div[3]/a'
            button = self.tab.ele(f'xpath:{button_xpath}', timeout=10)
            
            if button:
                logging.info("找到 'Get your free month' 按钮，准备点击...")
                time.sleep(random.uniform(1, 2))
                button.click()
                logging.info("已点击 'Get your free month' 按钮")
                time.sleep(random.uniform(3, 5))
                self.save_screenshot("step1_button_clicked")
                return True
            else:
                logging.error("未找到 'Get your free month' 按钮")
                return False
                
        except Exception as e:
            logging.error(f"第一步操作失败: {str(e)}")
            self.save_screenshot("step1_error")
            return False
    
    def step2_handle_captcha(self) -> bool:
        """
        第二步：处理人机验证

        Returns:
            bool: 验证是否成功
        """
        logging.info("第二步：处理人机验证...")

        # 等待页面加载
        time.sleep(random.uniform(2, 4))
        self.save_screenshot("step2_captcha_page")

        # 检查当前页面类型
        current_url = self.tab.url
        logging.info(f"当前页面 URL: {current_url}")

        # 如果是登录页面，需要先输入邮箱然后处理验证码
        if "login" in current_url.lower():
            logging.info("检测到登录页面，先输入邮箱...")

            # 查找邮箱输入框
            email_input = self.tab.ele("#username", timeout=5)
            if not email_input:
                email_input = self.tab.ele("input[type=email]", timeout=3)
            if not email_input:
                email_input = self.tab.ele("input[name=username]", timeout=3)

            if email_input:
                logging.info("找到邮箱输入框")
                email_input.clear()
                time.sleep(random.uniform(0.5, 1.5))
                email_input.input(self.email)
                logging.info(f"已输入邮箱: {self.email}")
                time.sleep(random.uniform(1, 2))
                self.save_screenshot("step2_email_input")
            else:
                logging.error("未找到邮箱输入框")
                return False

        # 处理 Turnstile 验证
        # 新策略：先检查 Continue 按钮是否可用，如果可用就直接点击
        # 如果不可用，再尝试 Turnstile 验证

        # 检查 Continue 按钮
        continue_button_selectors = [
            "button[data-action-button-primary='true']",  # 主要的 Continue 按钮
            "button[type='submit'][name='action'][value='default']",  # 具体的提交按钮
            "button._button-login-id",  # 登录页面的按钮类
            "button[type='submit']",  # 通用提交按钮
            "button:contains('Continue')",  # 包含 Continue 文本的按钮
        ]

        continue_button = None
        for selector in continue_button_selectors:
            try:
                button = self.tab.ele(selector, timeout=2)
                if button:
                    button_text = button.text.strip() if button.text else "No text"
                    is_disabled = button.attr('disabled') or button.attr('aria-disabled') == 'true'
                    logging.info(f"找到按钮: {selector} - 文本: '{button_text}' - 禁用: {is_disabled}")

                    if not is_disabled:
                        continue_button = button
                        break
            except Exception as e:
                logging.debug(f"选择器 {selector} 失败: {str(e)}")
                continue

        if continue_button:
            # Continue 按钮可用，直接点击
            logging.info("Continue 按钮可用，直接点击...")
            time.sleep(random.uniform(1, 2))
            continue_button.click()
            logging.info("已点击 Continue 按钮")
            time.sleep(random.uniform(3, 5))
            self.save_screenshot("step2_continue_clicked")
            return True
        else:
            # Continue 按钮不可用，尝试 Turnstile 验证
            logging.info("Continue 按钮不可用，尝试 Turnstile 验证...")
            if self.handle_turnstile_verification():
                logging.info("Turnstile 验证成功，再次检查 Continue 按钮...")

                # 重新检查 Continue 按钮
                for selector in continue_button_selectors:
                    try:
                        button = self.tab.ele(selector, timeout=2)
                        if button:
                            is_disabled = button.attr('disabled') or button.attr('aria-disabled') == 'true'
                            if not is_disabled:
                                logging.info("Turnstile 验证后 Continue 按钮可用，点击...")
                                time.sleep(random.uniform(1, 2))
                                button.click()
                                logging.info("已点击 Continue 按钮")
                                time.sleep(random.uniform(3, 5))
                                self.save_screenshot("step2_after_turnstile_continue")
                                return True
                    except Exception as e:
                        logging.debug(f"重新检查选择器 {selector} 失败: {str(e)}")
                        continue

                logging.error("Turnstile 验证成功但 Continue 按钮仍不可用")
                self.save_shadow_root_html("turnstile_success_but_no_continue")
                return False
            else:
                logging.error("Turnstile 验证失败且 Continue 按钮不可用")
                self.save_shadow_root_html("no_turnstile_no_continue")
                return False
    
    def step3_input_email(self) -> bool:
        """
        第三步：输入邮箱
        
        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("第三步：输入邮箱...")
            
            # 等待邮箱输入框出现
            email_xpath = '//*[@id="username"]'
            email_input = self.tab.ele(f'xpath:{email_xpath}', timeout=10)
            
            if email_input:
                logging.info(f"找到邮箱输入框，输入邮箱: {self.email}")
                time.sleep(random.uniform(1, 2))
                email_input.clear()
                email_input.input(self.email)
                logging.info("邮箱输入完成")
                time.sleep(random.uniform(1, 2))
                self.save_screenshot("step3_email_input")
                return True
            else:
                logging.error("未找到邮箱输入框")
                return False
                
        except Exception as e:
            logging.error(f"第三步操作失败: {str(e)}")
            self.save_screenshot("step3_error")
            return False
    
    def step4_click_continue(self) -> bool:
        """
        第四步：点击 Continue 按钮
        
        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("第四步：点击 Continue 按钮...")
            
            # 点击 Continue 按钮
            continue_xpath = '/html/body/div/main/main/section/div/div/div/div[1]/div/form/div[2]/button'
            continue_button = self.tab.ele(f'xpath:{continue_xpath}', timeout=10)
            
            if continue_button:
                logging.info("找到 Continue 按钮，准备点击...")
                time.sleep(random.uniform(1, 2))
                continue_button.click()
                logging.info("已点击 Continue 按钮")
                time.sleep(random.uniform(3, 5))
                self.save_screenshot("step4_continue_clicked")
                return True
            else:
                logging.error("未找到 Continue 按钮")
                return False
                
        except Exception as e:
            logging.error(f"第四步操作失败: {str(e)}")
            self.save_screenshot("step4_error")
            return False

    def step5_input_verification_code(self) -> bool:
        """
        第五步：输入邮箱验证码

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("第五步：等待页面加载并输入验证码...")

            # 等待验证码输入页面加载
            time.sleep(random.uniform(3, 5))
            self.save_screenshot("step5_verification_page")

            # 等待验证码输入框出现
            code_xpath = '//*[@id="code"]'
            code_input = self.tab.ele(f'xpath:{code_xpath}', timeout=15)

            if not code_input:
                logging.error("未找到验证码输入框")
                return False

            logging.info("找到验证码输入框，开始获取邮箱验证码...")

            # 获取邮箱验证码
            verification_code = self.email_handler.get_augmentcode_verification_code(max_retries=5, retry_interval=30)

            if not verification_code:
                logging.error("获取验证码失败")
                return False

            logging.info(f"获取到验证码: {verification_code}")

            # 输入验证码
            time.sleep(random.uniform(1, 2))
            code_input.clear()
            code_input.input(verification_code)
            logging.info("验证码输入完成")
            time.sleep(random.uniform(1, 2))
            self.save_screenshot("step5_code_input")

            # 点击 Continue 按钮
            continue_xpath = '/html/body/div/main/main/section/div/div/div/div/div/form/div[3]/button'
            continue_button = self.tab.ele(f'xpath:{continue_xpath}', timeout=10)

            if continue_button:
                logging.info("找到验证码页面的 Continue 按钮，准备点击...")
                time.sleep(random.uniform(1, 2))
                continue_button.click()
                logging.info("已点击验证码页面的 Continue 按钮")
                time.sleep(random.uniform(3, 5))
                self.save_screenshot("step5_continue_clicked")
                return True
            else:
                logging.error("未找到验证码页面的 Continue 按钮")
                return False

        except Exception as e:
            logging.error(f"第五步操作失败: {str(e)}")
            self.save_screenshot("step5_error")
            return False

    def step6_upload_file(self) -> bool:
        """
        第六步：上传 WIPDF.pdf 文件

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("第六步：上传文件...")

            # 等待文件上传页面加载
            time.sleep(random.uniform(3, 5))
            self.save_screenshot("step6_upload_page")

            # 检查 WIPDF.pdf 文件是否存在
            pdf_file_path = os.path.join(os.getcwd(), "WIPDF.pdf")
            if not os.path.exists(pdf_file_path):
                logging.error(f"未找到文件: {pdf_file_path}")
                return False

            logging.info(f"找到文件: {pdf_file_path}")

            # 查找文件上传控件（通常是 input[type="file"]）
            # 这里需要根据实际页面结构调整选择器
            file_input_selectors = [
                'tag:input@type=file',
                'xpath://input[@type="file"]',
                'css:input[type="file"]'
            ]

            file_input = None
            for selector in file_input_selectors:
                try:
                    file_input = self.tab.ele(selector, timeout=5)
                    if file_input:
                        break
                except:
                    continue

            if not file_input:
                logging.error("未找到文件上传控件")
                return False

            logging.info("找到文件上传控件，开始上传文件...")

            # 上传文件
            file_input.input(pdf_file_path)
            logging.info("文件上传完成")
            time.sleep(random.uniform(2, 4))
            self.save_screenshot("step6_file_uploaded")

            return True

        except Exception as e:
            logging.error(f"第六步操作失败: {str(e)}")
            self.save_screenshot("step6_error")
            return False

    def register(self) -> bool:
        """
        执行完整的注册流程

        Returns:
            bool: 注册是否成功
        """
        try:
            logging.info("开始 AugmentCode 注册流程...")

            # 初始化浏览器 - 使用代理和美国地区设置
            logging.info("使用基础浏览器管理器（带代理和美国地区设置）...")
            self.browser_manager = BrowserManager()

            # 获取用户代理（美国地区的 Chrome）
            user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6876.4 Safari/537.36"

            browser = self.browser_manager.init_browser(user_agent)
            self.tab = browser.latest_tab

            # 应用美国地区设置（模拟我们在 MCP 中验证成功的设置）
            self._apply_us_region_settings()

            # 执行注册步骤
            steps = [
                ("第一步：点击获取免费月份", self.step1_click_get_free_month),
                ("第二步：处理人机验证", self.step2_handle_captcha),
                ("第三步：输入邮箱", self.step3_input_email),
                ("第四步：点击继续", self.step4_click_continue),
                ("第五步：输入验证码", self.step5_input_verification_code),
                ("第六步：上传文件", self.step6_upload_file),
            ]

            for step_name, step_func in steps:
                logging.info(f"执行 {step_name}...")
                if not step_func():
                    logging.error(f"{step_name} 失败，注册流程中断")
                    return False
                logging.info(f"{step_name} 完成")

                # 步骤间随机等待
                time.sleep(random.uniform(1, 3))

            logging.info("AugmentCode 注册流程完成！")
            self.save_screenshot("registration_complete")
            return True

        except Exception as e:
            logging.error(f"注册流程出错: {str(e)}")
            self.save_screenshot("registration_error")
            return False
        finally:
            # 清理资源
            if self.browser_manager:
                self.browser_manager.quit()


def generate_email_with_timestamp() -> str:
    """
    生成基于时间戳的邮箱地址（模拟 Cursor 的 EmailGenerator 逻辑）

    Returns:
        str: 生成的邮箱地址
    """
    config = Config()

    # 使用时间戳后4位确保唯一性
    timestamp = str(int(time.time()))[-4:]

    if config.get_temp_mail() != "null":
        # 使用临时邮箱 + 时间戳
        username = config.get_temp_mail()
        domain = config.get_domain()
        email = f"{username}{timestamp}@{domain}"
    else:
        # 使用 IMAP 邮箱（保持原有逻辑）
        imap_config = config.get_imap()
        if imap_config:
            email = imap_config['imap_user']
        else:
            raise ValueError("邮箱配置错误：既没有配置临时邮箱也没有配置 IMAP")

    logging.info(f"生成的邮箱地址: {email}")
    return email


def main():
    """主函数"""
    try:
        # 生成邮箱地址（使用验证成功的逻辑）
        email = generate_email_with_timestamp()

        logging.info(f"开始使用邮箱 {email} 进行 AugmentCode 注册...")

        # 从配置获取其他信息
        config = Config()

        # 检查代理配置
        proxy = os.getenv("BROWSER_PROXY", "").strip()
        if proxy:
            logging.info(f"使用代理: {proxy}")
        else:
            logging.warning("未配置代理，建议配置 BROWSER_PROXY=http://127.0.0.1:1080")

        # 创建注册器并执行注册
        register = AugmentCodeRegister(email)
        success = register.register()

        if success:
            logging.info("AugmentCode 注册成功！")
        else:
            logging.error("AugmentCode 注册失败！")

        return success

    except Exception as e:
        logging.error(f"主程序出错: {str(e)}")
        return False


if __name__ == "__main__":
    main()
