#!/usr/bin/env python3
"""
测试修复后的 Turnstile 复选框验证逻辑
"""

import time
from logger import logging
from config import Config
from augmentcode_register import AugmentCodeRegister


def test_turnstile_checkbox_fix():
    """测试修复后的 Turnstile 复选框验证"""
    print("=" * 60)
    print("测试修复后的 Turnstile 复选框验证")
    print("=" * 60)
    
    try:
        # 获取邮箱配置
        config = Config()
        
        if config.get_temp_mail() != "null":
            email = f"{config.get_temp_mail()}{config.get_temp_mail_ext()}"
        else:
            imap_config = config.get_imap()
            if imap_config:
                email = imap_config['imap_user']
            else:
                print("❌ 未配置有效的邮箱")
                return False
        
        print(f"✓ 使用邮箱: {email}")
        
        # 创建注册器
        register = AugmentCodeRegister(email)
        print("✓ 注册器创建成功")
        
        # 手动初始化反检测浏览器
        print("\n正在初始化反检测浏览器...")
        try:
            from anti_detection_browser import AntiDetectionBrowserManager
            register.browser_manager = AntiDetectionBrowserManager()
            browser = register.browser_manager.init_browser()
            register.tab = browser.latest_tab
            print("✓ 反检测浏览器初始化成功")
        except Exception as e:
            print(f"❌ 反检测浏览器初始化失败: {str(e)}")
            return False
        
        # 执行第一步：访问页面
        print("\n执行第一步：访问 AugmentCode 页面...")
        if not register.step1_click_get_free_month():
            print("❌ 第一步失败")
            if register.browser_manager:
                register.browser_manager.quit()
            return False
        
        print("✓ 第一步成功")
        
        # 专门测试 Turnstile 验证
        print("\n开始专门测试 Turnstile 复选框验证...")
        print("=" * 40)
        
        # 等待页面稳定
        time.sleep(3)
        
        # 检查 Turnstile 元素
        print("检查 Turnstile 元素...")
        turnstile_element = register.tab.ele("@id=cf-turnstile", timeout=5)
        if turnstile_element:
            print("✓ 找到 Turnstile 元素")
        else:
            print("❌ 未找到 Turnstile 元素")
            return False
        
        # 保存初始状态截图
        register.save_screenshot("turnstile_initial_state")
        
        # 尝试手动调试 shadow DOM 结构
        print("\n调试 Turnstile shadow DOM 结构...")
        debug_turnstile_structure(register.tab)
        
        # 执行修复后的 Turnstile 验证
        print("\n执行修复后的 Turnstile 验证...")
        success = register.handle_turnstile_verification(max_retries=3)
        
        if success:
            print("✅ Turnstile 验证成功！")
            
            # 继续执行后续步骤验证
            print("\n继续执行后续步骤...")
            
            # 第三步：输入邮箱
            print("执行第三步：输入邮箱...")
            if register.step3_input_email():
                print("✓ 邮箱输入成功")
                
                # 第四步：点击继续
                print("执行第四步：点击继续...")
                if register.step4_click_continue():
                    print("✓ 点击继续成功")
                    
                    # 等待页面跳转
                    time.sleep(5)
                    print(f"当前页面 URL: {register.tab.url}")
                    print(f"当前页面标题: {register.tab.title}")
                    
                    # 检查是否到达验证码页面
                    if "login" in register.tab.url.lower():
                        print("⚠️  页面跳转到了登录页面，可能需要进一步调试")
                    else:
                        print("✓ 页面跳转正常")
                else:
                    print("❌ 点击继续失败")
            else:
                print("❌ 邮箱输入失败")
        else:
            print("❌ Turnstile 验证失败")
        
        # 保持浏览器打开以便观察
        print("\n浏览器将保持打开 30 秒以便观察...")
        time.sleep(30)
        
        # 清理资源
        print("\n清理浏览器资源...")
        if register.browser_manager:
            register.browser_manager.quit()
        print("✓ 清理完成")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        logging.error(f"Turnstile 复选框测试出错: {str(e)}")
        return False


def debug_turnstile_structure(tab):
    """调试 Turnstile 结构"""
    try:
        print("调试 Turnstile shadow DOM 结构...")
        
        # 获取 Turnstile 元素
        turnstile_element = tab.ele("@id=cf-turnstile", timeout=3)
        if not turnstile_element:
            print("❌ 未找到 Turnstile 元素")
            return
        
        print("✓ 找到 Turnstile 元素")
        
        # 尝试访问 shadow root
        try:
            shadow_root = turnstile_element.child().shadow_root
            print("✓ 成功访问 shadow root")
            
            # 查找 iframe
            iframe = shadow_root.ele("tag:iframe", timeout=2)
            if iframe:
                print("✓ 找到 iframe")
                
                # 访问 iframe 内容
                body = iframe.ele("tag:body", timeout=2)
                if body:
                    print("✓ 找到 iframe body")
                    
                    # 查找复选框
                    checkbox_selectors = [
                        "tag:input@type=checkbox",
                        "input[type=checkbox]",
                        ".cb-lb input",
                        "label input"
                    ]
                    
                    for selector in checkbox_selectors:
                        try:
                            checkbox = body.sr(selector)
                            if checkbox:
                                print(f"✓ 使用选择器 '{selector}' 找到复选框")
                                print(f"  复选框属性:")
                                print(f"    type: {checkbox.attr('type')}")
                                print(f"    checked: {checkbox.attr('checked')}")
                                print(f"    class: {checkbox.attr('class')}")
                                break
                        except Exception as e:
                            print(f"  选择器 '{selector}' 失败: {str(e)}")
                    
                    # 查找其他相关元素
                    try:
                        label = body.sr("tag:label")
                        if label:
                            print(f"✓ 找到标签: {label.text}")
                    except:
                        pass
                        
                    try:
                        success_div = body.sr("#success")
                        if success_div:
                            print(f"✓ 找到成功元素，显示状态: {success_div.style('display')}")
                    except:
                        pass
                        
                else:
                    print("❌ 未找到 iframe body")
            else:
                print("❌ 未找到 iframe")
                
        except Exception as e:
            print(f"❌ 访问 shadow root 失败: {str(e)}")
            
    except Exception as e:
        print(f"❌ 调试 Turnstile 结构失败: {str(e)}")


def main():
    """主函数"""
    print("AugmentCode Turnstile 复选框修复测试")
    print("=" * 60)
    
    # 询问是否进行测试
    choice = input("是否进行 Turnstile 复选框修复测试？(y/n): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        print("\n开始 Turnstile 复选框修复测试...")
        success = test_turnstile_checkbox_fix()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 Turnstile 复选框修复测试成功！")
            print("验证逻辑已正确修复")
        else:
            print("❌ Turnstile 复选框修复测试失败")
            print("可能需要进一步调试")
        print("=" * 60)
    else:
        print("\n跳过 Turnstile 复选框修复测试。")


if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试程序出错: {str(e)}")
        input("\n按回车键退出...")
