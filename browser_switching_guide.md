# 浏览器切换测试指南

## 🎯 目标

测试不同浏览器内核（Chrome、Edge、Chromium）对 reCAPTCHA 验证的影响，找到验证通过率最高的浏览器。

## 🔧 已实现的功能

### 1. 多浏览器支持

**修改的文件：**
- `browser_utils.py` - 添加了多浏览器路径检测
- `cursor_style_register.py` - 支持指定浏览器类型

**支持的浏览器：**
- `chrome` - Google Chrome
- `edge` - Microsoft Edge  
- `chromium` - Chromium

### 2. 自动路径检测

系统会自动检测以下路径：

**Windows 系统：**
```
Chrome:
- C:\Program Files\Google\Chrome\Application\chrome.exe
- C:\Program Files (x86)\Google\Chrome\Application\chrome.exe
- %USERPROFILE%\AppData\Local\Google\Chrome\Application\chrome.exe

Edge:
- C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe
- C:\Program Files\Microsoft\Edge\Application\msedge.exe
- %USERPROFILE%\AppData\Local\Microsoft\Edge\Application\msedge.exe

Chromium:
- C:\Program Files\Chromium\Application\chrome.exe
- C:\Program Files (x86)\Chromium\Application\chrome.exe
```

**macOS 系统：**
```
Chrome: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Edge: /Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge
Chromium: /Applications/Chromium.app/Contents/MacOS/Chromium
```

**Linux 系统：**
```
Chrome: /usr/bin/google-chrome, /usr/bin/google-chrome-stable
Edge: /usr/bin/microsoft-edge, /usr/bin/microsoft-edge-stable
Chromium: /usr/bin/chromium, /usr/bin/chromium-browser
```

## 🚀 使用方法

### 方法1: 测试单个浏览器

#### 测试 Edge 浏览器：
```bash
python test_edge_registration.py
```

#### 测试特定浏览器：
```bash
# 测试 Chrome
python test_browser_comparison.py chrome

# 测试 Edge
python test_browser_comparison.py edge

# 测试 Chromium
python test_browser_comparison.py chromium
```

### 方法2: 对比所有浏览器

```bash
python test_browser_comparison.py
```

这会依次测试所有浏览器并生成对比报告。

### 方法3: 在注册脚本中指定浏览器

```python
from cursor_style_register import CursorStyleRegister

# 使用 Edge 浏览器
register = CursorStyleRegister(email="<EMAIL>", browser_type="edge")

# 使用 Chrome 浏览器
register = CursorStyleRegister(email="<EMAIL>", browser_type="chrome")

# 使用 Chromium 浏览器
register = CursorStyleRegister(email="<EMAIL>", browser_type="chromium")
```

## 📊 测试内容

每个浏览器测试会检查：

1. **浏览器初始化**：是否能成功启动
2. **语言设置**：navigator.language 是否设置为日语
3. **reCAPTCHA 语言**：URL 中的 hl 参数是否为 ja
4. **验证成功率**：Turnstile 验证是否通过
5. **验证时间**：完成验证所需的时间

## 🎯 预期效果

不同浏览器可能有不同的表现：

### Microsoft Edge 的优势：
- **更新的内核**：基于 Chromium，但可能有不同的指纹特征
- **不同的 User-Agent**：可能被识别为不同的浏览器
- **地区优化**：可能对某些地区有更好的支持

### Google Chrome：
- **标准参考**：最常用的浏览器
- **完整功能**：所有 Web 标准支持

### Chromium：
- **开源版本**：可能有不同的检测特征
- **精简版本**：较少的商业化功能

## 🔍 故障排除

### 1. 浏览器未找到

如果系统提示找不到浏览器：

```bash
# 手动指定浏览器路径
set BROWSER_PATH=C:\Program Files\Microsoft\Edge\Application\msedge.exe
python test_edge_registration.py
```

### 2. 扩展加载失败

如果 turnstilePatch 扩展加载失败：
- 确保 `turnstilePatch` 文件夹存在
- 检查扩展文件是否完整

### 3. 验证仍然失败

如果所有浏览器都验证失败：
- 检查网络连接
- 确认代理设置正确
- 尝试不同的时间段

## 📋 测试报告示例

```
================================================================================
📊 测试结果汇总
================================================================================
CHROME     | ❌ 失败   | 语言: zh    | 耗时: N/A
EDGE       | ✅ 成功   | 语言: ja    | 耗时: 3.45s
CHROMIUM   | ❌ 失败   | 语言: zh    | 耗时: N/A

🏆 推荐浏览器: EDGE
   验证成功，耗时: 3.45秒
   reCAPTCHA 语言: ja
```

## 🎉 使用建议

1. **首先测试 Edge**：作为较新的浏览器，可能有更好的兼容性
2. **对比测试**：运行完整对比来找到最佳浏览器
3. **记录结果**：保存测试截图和日志用于分析
4. **环境变量**：使用 `BROWSER_PATH` 指定特定的浏览器版本

## 🔧 高级配置

### 自定义浏览器路径：
```bash
# Windows
set BROWSER_PATH=D:\CustomBrowser\msedge.exe

# Linux/macOS
export BROWSER_PATH=/opt/custom/browser/chrome
```

### 代理设置：
```bash
set BROWSER_PROXY=127.0.0.1:1080
```

### 无头模式：
```bash
set BROWSER_HEADLESS=false  # 显示浏览器窗口
```

通过这些测试，您应该能找到在您的环境中验证通过率最高的浏览器！
