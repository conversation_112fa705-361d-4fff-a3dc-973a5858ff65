#!/usr/bin/env python3
"""
快速指纹伪装测试脚本
验证指纹伪装功能是否正常工作
"""

import time
from logger import logging
from fingerprint_browser_utils import FingerprintBrowserManager


def quick_test():
    """快速测试指纹伪装功能"""
    try:
        logging.info("🧪 开始快速指纹伪装测试...")
        
        # 初始化指纹浏览器
        browser_manager = FingerprintBrowserManager()
        browser = browser_manager.init_browser()
        
        if not browser:
            logging.error("❌ 浏览器初始化失败")
            return False
        
        tab = browser.latest_tab
        
        # 访问指纹检测网站
        logging.info("🌐 访问指纹检测网站...")
        tab.get("https://browserleaks.com/canvas")
        time.sleep(3)
        
        # 注入指纹伪装脚本
        browser_manager.inject_scripts_to_tab(tab)
        time.sleep(2)
        
        # 测试Canvas指纹
        logging.info("🎨 测试Canvas指纹...")
        canvas_result = tab.run_js("""
            var canvas = document.createElement('canvas');
            var ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Canvas fingerprint test', 2, 2);
            return canvas.toDataURL().substring(0, 50);
        """)
        logging.info(f"Canvas指纹: {canvas_result}")
        
        # 测试WebGL指纹
        logging.info("🔺 测试WebGL指纹...")
        webgl_result = tab.run_js("""
            var canvas = document.createElement('canvas');
            var gl = canvas.getContext('webgl');
            if (gl) {
                return {
                    renderer: gl.getParameter(gl.RENDERER),
                    vendor: gl.getParameter(gl.VENDOR)
                };
            }
            return null;
        """)
        logging.info(f"WebGL信息: {webgl_result}")
        
        # 测试地理位置
        logging.info("📍 测试地理位置...")
        geo_result = tab.run_js("""
            return new Promise((resolve) => {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        (position) => resolve({
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude
                        }),
                        (error) => resolve({error: error.message}),
                        {timeout: 3000}
                    );
                } else {
                    resolve({error: 'Geolocation not supported'});
                }
            });
        """)
        logging.info(f"地理位置: {geo_result}")
        
        # 测试语言和时区
        logging.info("🌐 测试语言时区...")
        lang_tz_result = tab.run_js("""
            return {
                language: navigator.language,
                languages: navigator.languages,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                timezoneOffset: new Date().getTimezoneOffset()
            };
        """)
        logging.info(f"语言时区: {lang_tz_result}")
        
        # 测试硬件信息
        logging.info("💻 测试硬件信息...")
        hardware_result = tab.run_js("""
            return {
                cores: navigator.hardwareConcurrency,
                memory: navigator.deviceMemory,
                platform: navigator.platform,
                userAgent: navigator.userAgent.substring(0, 100)
            };
        """)
        logging.info(f"硬件信息: {hardware_result}")
        
        # 截图记录
        tab.get_screenshot(path=f"quick_fingerprint_test_{int(time.time())}.png")
        
        logging.info("✅ 快速指纹伪装测试完成！")
        
        # 检查关键指纹是否被伪装
        success_checks = []
        
        # 检查WebGL是否被伪装为AMD
        if webgl_result and 'renderer' in webgl_result:
            if 'AMD' in str(webgl_result['renderer']):
                success_checks.append("✅ WebGL伪装成功 (AMD)")
            else:
                success_checks.append("❌ WebGL伪装失败")
        
        # 检查地理位置是否被设置为美国
        if geo_result and 'latitude' in geo_result:
            lat = geo_result['latitude']
            lng = geo_result['longitude']
            if abs(lat - 39.8283) < 1 and abs(lng - (-98.5795)) < 1:
                success_checks.append("✅ 地理位置伪装成功 (美国)")
            else:
                success_checks.append("❌ 地理位置伪装失败")
        
        # 检查硬件信息是否被伪装
        if hardware_result:
            if hardware_result.get('cores') == 8:
                success_checks.append("✅ CPU核心数伪装成功 (8核)")
            else:
                success_checks.append("❌ CPU核心数伪装失败")
                
            if hardware_result.get('memory') == 16:
                success_checks.append("✅ 内存伪装成功 (16GB)")
            else:
                success_checks.append("❌ 内存伪装失败")
        
        # 输出检查结果
        logging.info("\n" + "="*50)
        logging.info("指纹伪装效果检查:")
        for check in success_checks:
            logging.info(check)
        logging.info("="*50)
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 快速指纹伪装测试失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        # 保持浏览器打开供检查
        input("按 Enter 键关闭浏览器...")
        if browser_manager:
            browser_manager.quit()


def main():
    """主函数"""
    try:
        logging.info("=" * 60)
        logging.info("快速指纹伪装功能测试")
        logging.info("=" * 60)
        
        success = quick_test()
        
        if success:
            logging.info("🎉 快速指纹伪装测试成功！")
            print("\n✅ 快速指纹伪装测试完成！")
        else:
            logging.error("😞 快速指纹伪装测试失败！")
            print("\n❌ 快速指纹伪装测试失败！")
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 测试程序执行失败: {e}")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
