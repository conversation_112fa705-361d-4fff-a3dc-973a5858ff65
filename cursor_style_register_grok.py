#!/usr/bin/env python3
"""
基于 Cursor 风格的 AugmentCode 注册测试脚本
用于测试多个指纹浏览器效果（FingerprintJS验证 + Puppeteer-like指纹伪造）
逻辑与原脚本完全相同，仅用于测试，不要用于生产
"""

import os
import sys
import time
import random
from logger import logging
from augmentcode_register import generate_email_with_timestamp

class CursorStyleRegisterTest:
    """测试版 Cursor 风格注册器，支持指纹配置"""

    def __init__(self, email: str, browser_type: str = "chrome", fingerprint_config: dict = None):
        """
        初始化测试注册器

        Args:
            email: 注册邮箱
            browser_type: 浏览器类型 ("chrome", "edge", "chromium")
            fingerprint_config: 指纹配置字典（基于用户参考）
        """
        self.email = email
        self.browser = None
        self.tab = None
        self.browser_manager = None
        self.browser_type = browser_type
        self.fingerprint_config = fingerprint_config or self._default_fingerprint_config()

    def _default_fingerprint_config(self) -> dict:
        """默认指纹配置，基于用户参考"""
        # 假设从环境或IP获取语言/时区（这里模拟）
        ip_based_lang = "en-US"  # 示例：基于IP匹配
        ip_based_timezone = "America/New_York"  # 示例：基于IP匹配
        return {
            "language": ip_based_lang,
            "timezone": ip_based_timezone,
            "geolocation": {"latitude": 37.7749, "longitude": -122.4194, "accuracy": 100},  # 美国位置
            "resolution": "1920x1080",  # 真实分辨率示例
            "fonts": "real",  # 真实字体列表
            "font_fingerprint": "noise",  # 添加噪音
            "webrtc": "hidden",
            "canvas": "noise",
            "webgl_image": "noise",
            "webgl_info": "Google Inc. (AMD) ANGLE (AMD, AMD Radeon(TM) R5 Graphics Direct3D9Ex vs_3_0 ps_3_0, aticfx64.dll)",
            "webgpu": "match_webgl",  # 基于WebGL匹配
            "audio_context": "noise",
            "speech_voices": "enabled",
            "media_devices": "noise"
        }

    def init_browser(self) -> bool:
        """
        初始化浏览器并应用指纹配置（测试版）
        """
        try:
            logging.info(f"🚀 初始化 {self.browser_type.upper()} 浏览器（测试指纹配置）...")

            from browser_utils import BrowserManager

            self.browser_manager = BrowserManager(browser_type=self.browser_type)
            # 应用指纹配置（假设BrowserManager支持；否则需扩展为Playwright/Puppeteer集成）
            self.browser_manager.apply_fingerprint(self.fingerprint_config)
            self.browser = self.browser_manager.init_browser()
            self.tab = self.browser.latest_tab

            # 测试注入FingerprintJS验证指纹
            self._validate_fingerprint_with_fingerprintjs()

            logging.info("✅ 浏览器初始化成功（指纹应用完成）")
            return True

        except Exception as e:
            logging.error(f"❌ 浏览器初始化失败: {e}")
            return False

    def _validate_fingerprint_with_fingerprintjs(self):
        """使用FingerprintJS注入JS采集并验证指纹哈希（测试效果）"""
        try:
            # 注入FingerprintJS库（假设从CDN加载；实际需确保可用）
            self.tab.run_js("""
                (async () => {
                    const fpScript = document.createElement('script');
                    fpScript.src = 'https://openfpcdn.io/fingerprintjs/v4';
                    document.head.appendChild(fpScript);
                    await new Promise(resolve => fpScript.onload = resolve);
                    const fp = await FingerprintJS.load();
                    const result = await fp.get();
                    console.log('Fingerprint Hash:', result.visitorId);
                })();
            """)
            time.sleep(2)  # 等待JS执行
            logging.info("✅ FingerprintJS注入并采集指纹哈希（检查控制台日志）")
        except Exception as e:
            logging.warning(f"⚠️ FingerprintJS验证失败: {e}")

    # 以下步骤逻辑与原脚本完全相同，仅添加测试日志
    def step1_visit_promotion_page(self) -> bool:
        try:
            logging.info("📋 第一步：访问 AugmentCode Cursor 促销页面...")
            self.tab.get("https://www.augmentcode.com/resources/cursor")
            time.sleep(3)
            self.tab.get_screenshot(path=f"test_step1_promotion_page_{int(time.time())}.png")
            page_title = self.tab.title
            logging.info(f"📄 页面标题: {page_title}")
            logging.info("✅ 第一步完成：成功访问促销页面")
            return True
        except Exception as e:
            logging.error(f"❌ 第一步失败: {e}")
            return False

    def step2_click_register_button(self) -> bool:
        try:
            logging.info("📋 第二步：查找并点击注册按钮...")
            button_texts = ["Get your free month", "Claim offer", "Get started", "Continue", "Sign up", "Register", "Start free trial", "Redeem"]
            button_clicked = False
            for button_text in button_texts:
                try:
                    button = self.tab.ele(f"@text()={button_text}", timeout=2)
                    if button:
                        button.click()
                        logging.info(f"✅ 成功点击按钮: {button_text}")
                        button_clicked = True
                        break
                except:
                    continue
            if not button_clicked:
                logging.error("❌ 未找到可点击的注册按钮")
                return False
            time.sleep(5)
            self.tab.get_screenshot(path=f"test_step2_after_click_{int(time.time())}.png")
            new_url = self.tab.url
            logging.info(f"🌐 跳转后 URL: {new_url}")
            logging.info("✅ 第二步完成：成功点击注册按钮")
            return True
        except Exception as e:
            logging.error(f"❌ 第二步失败: {e}")
            return False

    def step3_handle_turnstile(self) -> bool:
        try:
            logging.info("📋 第三步：处理 Turnstile 验证...")
            return self._handle_turnstile_cursor_method()
        except Exception as e:
            logging.error(f"❌ 第三步失败: {e}")
            return False

    def _handle_turnstile_cursor_method(self, max_retries: int = 3) -> bool:
        import random
        self._setup_japanese_language()
        if self._check_turnstile_success():
            logging.info("🎉 Turnstile 验证已完成！")
            return True
        retry_count = 0
        while retry_count < max_retries:
            retry_count += 1
            logging.info(f"🔄 Turnstile 验证尝试 {retry_count}/{max_retries}")
            try:
                challenge_check = None
                try:
                    challenge_check = self.tab.ele("#ulp-auth0-v2-captcha", timeout=3).child().shadow_root.ele("tag:iframe").ele("tag:body").sr("tag:input")
                    if challenge_check:
                        logging.info("🔍 找到 Auth0 V2 Captcha 验证框")
                except:
                    pass
                if not challenge_check:
                    try:
                        challenge_check = self.tab.ele("@id=cf-turnstile", timeout=3).child().shadow_root.ele("tag:iframe").ele("tag:body").sr("tag:input")
                        if challenge_check:
                            logging.info("🔍 找到 Cloudflare Turnstile 验证框")
                    except:
                        pass
                if challenge_check:
                    logging.info("🖱️ 点击 Turnstile 验证框...")
                    time.sleep(random.uniform(1, 3))
                    challenge_check.click()
                    time.sleep(2)
                    self.tab.get_screenshot(path=f"test_turnstile_clicked_{int(time.time())}.png")
                    if self._check_turnstile_success():
                        logging.info("🎉 Turnstile 验证成功！")
                        self.tab.get_screenshot(path=f"test_turnstile_success_{int(time.time())}.png")
                        return True
                else:
                    logging.info("ℹ️ 未找到 Turnstile 验证框，等待验证中...")
                    self._debug_turnstile_content()
                    if self._check_turnstile_success():
                        logging.info("🎉 Turnstile 验证已完成！")
                        return True
            except Exception as e:
                logging.debug(f"第 {retry_count} 次尝试失败: {e}")
            if self._check_turnstile_success():
                logging.info("🎉 Turnstile 验证已完成！")
                return True
            if retry_count < max_retries:
                delay = random.uniform(2, 4)
                logging.info(f"⏳ 等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)
        logging.warning("⚠️ Turnstile 验证未能确认成功，但继续流程")
        return True

    def _setup_japanese_language(self):
        try:
            logging.info("🌐 设置日语语言环境...")
            self.tab.run_js("""
                Object.defineProperty(navigator, 'language', { get: function() { return 'ja-JP'; } });
                Object.defineProperty(navigator, 'languages', { get: function() { return ['ja-JP', 'ja', 'en']; } });
                const originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function() {
                    this.addEventListener('readystatechange', function() {
                        if (this.readyState === 1) this.setRequestHeader('Accept-Language', 'ja-JP,ja;q=0.9,en;q=0.8');
                    });
                    return originalOpen.apply(this, arguments);
                };
                const originalFetch = window.fetch;
                window.fetch = function(input, init) {
                    init = init || {};
                    init.headers = init.headers || {};
                    init.headers['Accept-Language'] = 'ja-JP,ja;q=0.9,en;q=0.8';
                    return originalFetch.apply(this, arguments);
                };
            """)
            logging.info("✅ 日语语言环境设置完成")
        except Exception as e:
            logging.warning(f"⚠️ 设置日语语言环境失败: {e}")

    def _check_turnstile_success(self) -> bool:
        try:
            page_indicators = ["@name=password", "@data-index=0", "Account Settings"]
            for indicator in page_indicators:
                try:
                    if self.tab.ele(indicator, timeout=1):
                        logging.info(f"✅ 检测到页面级验证成功标志: {indicator}")
                        return True
                except:
                    continue
            try:
                auth0_captcha = self.tab.ele("#ulp-auth0-v2-captcha", timeout=1)
                if auth0_captcha:
                    iframe_body = auth0_captcha.child().shadow_root.ele("tag:iframe").ele("tag:body")
                    if iframe_body:
                        try:
                            if iframe_body.sr("#success"):
                                logging.info("✅ 检测到 #success 元素，文本为 'Success!'")
                                return True
                        except:
                            pass
                        iframe_success_indicators = ["tag:input[value*='success']", "tag:input[data-success='true']", ".success", "@text()=Success!"]
                        for indicator in iframe_success_indicators:
                            try:
                                element = iframe_body.sr(indicator)
                                if element and ("success" in (element.text or "").lower() or "success" in (element.attr("value") or "").lower()):
                                    logging.info(f"✅ 检测到 iframe 内成功标志: {indicator}")
                                    return True
                            except:
                                continue
                        try:
                            if len(iframe_body.sr("tag:input").attr("value") or "") > 10:
                                logging.info("✅ Challenge input 值已生成，验证完成")
                                return True
                        except:
                            pass
            except:
                pass
            return False
        except Exception as e:
            logging.debug(f"检查验证成功状态异常: {e}")
            return False

    def _debug_turnstile_content(self):
        try:
            auth0_captcha = self.tab.ele("#ulp-auth0-v2-captcha", timeout=1)
            if auth0_captcha:
                iframe_body = auth0_captcha.child().shadow_root.ele("tag:iframe").ele("tag:body")
                if iframe_body:
                    logging.info(f"🔍 Turnstile iframe 内容: '{(iframe_body.text or '').strip()}'")
                    all_elements = iframe_body.eles("tag:*")[:5]
                    for i, element in enumerate(all_elements):
                        logging.debug(f"  元素{i+1}: <{element.tag}> id='{element.attr('id')}' class='{element.attr('class')}' text='{element.text}'")
        except Exception as e:
            logging.debug(f"调试 Turnstile 内容异常: {e}")

    def _handle_terms_agreement(self) -> bool:
        try:
            logging.info("📋 检查是否跳转到条款同意页面...")
            time.sleep(3)
            current_url = self.tab.url
            logging.info(f"🔍 当前 URL: {current_url}")
            if "terms-accept" not in current_url:
                logging.info("ℹ️ 未跳转到条款同意页面，继续流程")
                return True
            logging.info("🔍 检测到条款同意页面 (terms-accept)")
            time.sleep(2)
            terms_checkbox = self.tab.ele("#terms-of-service-checkbox", timeout=5)
            if not terms_checkbox:
                logging.warning("⚠️ 未找到条款同意复选框")
                return False
            is_checked = terms_checkbox.attr("checked") is not None
            logging.info(f"📋 条款复选框当前状态: {'已勾选' if is_checked else '未勾选'}")
            if not is_checked:
                logging.info("☑️ 勾选条款同意复选框...")
                terms_checkbox.click()
                time.sleep(1)
                if terms_checkbox.attr("checked") is None:
                    logging.warning("⚠️ 直接点击失败，尝试 JavaScript...")
                    self.tab.run_js("""
                        var checkbox = document.getElementById('terms-of-service-checkbox');
                        if (checkbox) {
                            checkbox.checked = true;
                            var event = new Event('change', { bubbles: true });
                            checkbox.dispatchEvent(event);
                            if (typeof updateSignupButton === 'function') updateSignupButton(checkbox);
                        }
                    """)
                    time.sleep(1)
                    if terms_checkbox.attr("checked") is None:
                        logging.warning("⚠️ JavaScript 方法也失败")
            else:
                logging.info("ℹ️ 条款复选框已勾选")
            signup_button = self.tab.ele("#signup-button", timeout=3)
            if signup_button:
                is_disabled = signup_button.attr("disabled") is not None
                logging.info(f"🔍 注册按钮状态: {'禁用' if is_disabled else '启用'}")
                if is_disabled:
                    for i in range(10):
                        time.sleep(1)
                        if signup_button.attr("disabled") is None:
                            logging.info(f"✅ 注册按钮在 {i+1} 秒后启用")
                            break
                    if signup_button.attr("disabled") is not None:
                        logging.warning("⚠️ 按钮仍然禁用，尝试强制启用...")
                        self.tab.run_js("document.getElementById('signup-button').removeAttribute('disabled'); document.getElementById('signup-button').disabled = false;")
                        time.sleep(1)
                if signup_button.attr("disabled") is None:
                    logging.info("🖱️ 点击注册按钮...")
                    self.tab.get_screenshot(path=f"test_before_signup_click_{int(time.time())}.png")
                    try:
                        signup_button.click()
                    except:
                        self.tab.run_js("document.getElementById('signup-button').click();")
                    time.sleep(3)
                    self.tab.get_screenshot(path=f"test_after_signup_click_{int(time.time())}.png")
                    logging.info("✅ 注册按钮点击成功")
                    return True
                else:
                    logging.error("❌ 注册按钮仍然禁用，无法点击")
                    return False
            else:
                logging.error("❌ 未找到注册按钮")
                return False
        except Exception as e:
            logging.error(f"❌ 处理条款同意失败: {e}")
            return False

    def step4_input_email(self) -> bool:
        try:
            logging.info("📋 第四步：处理条款同意页面和输入邮箱...")
            current_url = self.tab.url
            if "terms-accept" in current_url:
                if not self._handle_terms_agreement():
                    return False
                time.sleep(5)
                current_url = self.tab.url
            email_selectors = ["#username", "input[name=username]", "input[type=email]", "@type=email", "@placeholder*=email", "@name=email", "@id*=email"]
            email_input = None
            for selector in email_selectors:
                try:
                    email_input = self.tab.ele(selector, timeout=3)
                    if email_input:
                        logging.info(f"✅ 找到邮箱输入框: {selector}")
                        break
                except:
                    continue
            if not email_input:
                logging.error("❌ 未找到邮箱输入框")
                return False
            email_input.clear()
            email_input.input(self.email)
            logging.info(f"✅ 邮箱输入成功: {self.email}")
            time.sleep(2)
            self.tab.get_screenshot(path=f"test_step4_email_input_{int(time.time())}.png")
            logging.info("✅ 第四步完成：邮箱输入成功")
            return True
        except Exception as e:
            logging.error(f"❌ 第四步失败: {e}")
            return False

    def step5_click_continue(self) -> bool:
        try:
            logging.info("📋 第五步：点击 Continue 按钮...")
            continue_texts = ["Continue", "Next", "Submit", "Proceed"]
            continue_button = None
            for text in continue_texts:
                try:
                    continue_button = self.tab.ele(f"@text()={text}", timeout=3)
                    if continue_button:
                        logging.info(f"✅ 找到按钮: {text}")
                        break
                except:
                    continue
            if not continue_button:
                logging.error("❌ 未找到 Continue 按钮")
                return False
            continue_button.click()
            logging.info("✅ 成功点击 Continue 按钮")
            time.sleep(5)
            self.tab.get_screenshot(path=f"test_step5_after_continue_{int(time.time())}.png")
            new_url = self.tab.url
            logging.info(f"🌐 跳转后 URL: {new_url}")
            logging.info("✅ 第五步完成：成功点击 Continue")
            return True
        except Exception as e:
            logging.error(f"❌ 第五步失败: {e}")
            return False

    def step6_handle_verification_code(self) -> bool:
        try:
            logging.info("📋 第六步：处理验证码...")
            logging.info("⏳ 等待验证码邮件发送...")
            time.sleep(10)
            from augmentcode_register import AugmentCodeRegister
            temp_register = AugmentCodeRegister(self.email)
            verification_code = temp_register.email_handler.get_augmentcode_verification_code(max_retries=5, retry_interval=30)
            if not verification_code:
                logging.error("❌ 未获取到验证码")
                return False
            logging.info(f"✅ 获取到验证码: {verification_code}")
            code_selectors = ["#code", "input[id=code]", "input[name=code]", "@type=text", "@placeholder*=code", "@placeholder*=verification", "@name*=code", "@id*=code"]
            code_input = None
            for selector in code_selectors:
                try:
                    code_input = self.tab.ele(selector, timeout=3)
                    if code_input:
                        logging.info(f"✅ 找到验证码输入框: {selector}")
                        break
                except:
                    continue
            if not code_input:
                logging.error("❌ 未找到验证码输入框")
                return False
            code_input.clear()
            code_input.input(verification_code)
            logging.info("✅ 验证码输入成功")
            time.sleep(2)
            submit_texts = ["Continue", "Submit", "Verify", "Confirm"]
            submit_button = None
            for text in submit_texts:
                try:
                    submit_button = self.tab.ele(f"@text()={text}", timeout=3)
                    if submit_button:
                        logging.info(f"✅ 找到提交按钮: {text}")
                        break
                except:
                    continue
            if submit_button:
                submit_button.click()
                logging.info("✅ 验证码提交成功")
                time.sleep(5)
            else:
                logging.warning("⚠️ 未找到提交按钮，可能自动提交")
            self.tab.get_screenshot(path=f"test_step6_verification_complete_{int(time.time())}.png")
            logging.info("✅ 第六步完成：验证码处理成功")
            return True
        except Exception as e:
            logging.error(f"❌ 第六步失败: {e}")
            return False

    def step7_final_terms_agreement(self) -> bool:
        try:
            logging.info("📋 第七步：最终条款同意...")
            current_url = self.tab.url
            if "terms-accept" not in current_url:
                logging.info("ℹ️ 不在条款同意页面，可能已完成注册")
                return True
            if self._handle_terms_agreement():
                time.sleep(5)
                final_url = self.tab.url
                if "terms-accept" not in final_url:
                    logging.info("🎉 成功跳转，注册流程完成！")
                    return True
                else:
                    logging.warning("⚠️ 仍在条款同意页面，可能需要手动检查")
                    return True
            else:
                return False
        except Exception as e:
            logging.error(f"❌ 第七步失败: {e}")
            return False

    def complete_registration(self) -> bool:
        try:
            logging.info("🚀 开始测试 AugmentCode 注册流程...")
            if not self.init_browser():
                return False
            steps = [
                ("访问促销页面", self.step1_visit_promotion_page),
                ("点击注册按钮", self.step2_click_register_button),
                ("处理人机验证", self.step3_handle_turnstile),
                ("输入邮箱", self.step4_input_email),
                ("点击继续", self.step5_click_continue),
                ("处理验证码", self.step6_handle_verification_code),
                ("最终条款同意", self.step7_final_terms_agreement),
            ]
            for step_name, step_func in steps:
                logging.info(f"\n{'='*20} {step_name} {'='*20}")
                if not step_func():
                    logging.error(f"❌ {step_name} 失败，测试中止")
                    return False
                logging.info(f"✅ {step_name} 成功")
            time.sleep(5)
            final_url = self.tab.url
            final_title = self.tab.title
            logging.info(f"🌐 最终 URL: {final_url}")
            logging.info(f"📄 最终标题: {final_title}")
            self.tab.get_screenshot(path=f"test_final_result_{int(time.time())}.png")
            page_content = self.tab.html.lower()
            success_indicators = ["welcome", "success", "congratulations", "account created", "registration complete", "dashboard"]
            is_success = any(indicator in page_content for indicator in success_indicators)
            if is_success:
                logging.info("🎉 测试注册成功！")
                return True
            else:
                logging.info("ℹ️ 测试注册可能成功，请手动检查")
                return True
        except Exception as e:
            logging.error(f"❌ 测试流程失败: {e}")
            import traceback
            logging.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            input("按 Enter 键关闭浏览器...")
            if self.browser_manager:
                self.browser_manager.quit()

def test_multiple_fingerprints():
    """测试多个指纹浏览器（2个示例）"""
    logging.info("=" * 80)
    logging.info("开始多指纹浏览器测试")
    logging.info("=" * 80)
    
    proxy = os.getenv("BROWSER_PROXY", "")
    if not proxy:
        logging.warning("⚠️ 建议设置代理: set BROWSER_PROXY=127.0.0.1:1080")
    
    # 测试1: 默认指纹
    email1 = generate_email_with_timestamp()
    logging.info(f"🎯 测试1 邮箱: {email1}")
    register1 = CursorStyleRegisterTest(email1)
    success1 = register1.complete_registration()
    logging.info(f"测试1 {'成功' if success1 else '失败'}")
    
    # 测试2: 自定义噪音指纹（示例：增加Canvas/WebGL噪音）
    custom_config = {
        "canvas": "high_noise",  # 自定义变体
        "webgl_image": "high_noise",
        "audio_context": "high_noise"
    }
    email2 = generate_email_with_timestamp()
    logging.info(f"🎯 测试2 邮箱: {email2} (自定义噪音)")
    register2 = CursorStyleRegisterTest(email2, fingerprint_config=custom_config)
    success2 = register2.complete_registration()
    logging.info(f"测试2 {'成功' if success2 else '失败'}")
    
    return success1 and success2

if __name__ == "__main__":
    success = test_multiple_fingerprints()
    sys.exit(0 if success else 1)