#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行为模式深度模拟注册器
专门针对 g-recaptcha-response 和 verisoul-session-id 的绕过
包含完整的页面内容记录和日志分析功能
"""

import os
import sys
import time
import json
import random
import math
from datetime import datetime
from pathlib import Path
from logger import logging
from browser_utils import BrowserManager
from augmentcode_register import generate_email_with_timestamp, AugmentCodeRegister

class BehavioralSimulationRegister:
    """行为模式深度模拟注册器"""
    
    def __init__(self):
        self.browser_manager = BrowserManager()
        self.tab = None
        self.email = None
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_dir = Path("logs") / f"behavioral_test_{self.session_id}"
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 行为模拟参数
        self.typing_speed_range = (80, 150)  # 每分钟字符数
        self.mouse_move_steps = (15, 25)     # 鼠标移动步数
        self.pause_range = (0.5, 2.0)       # 随机停顿范围
        
        logging.info(f"🎭 行为模拟注册器初始化完成，会话ID: {self.session_id}")
        logging.info(f"📁 日志目录: {self.log_dir}")

    def save_page_content(self, step_name: str, additional_info: dict = None):
        """保存页面内容和相关信息"""
        try:
            timestamp = datetime.now().strftime("%H%M%S")
            
            # 保存HTML内容
            html_content = self.tab.html
            html_file = self.log_dir / f"{step_name}_{timestamp}.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # 保存纯文本内容
            try:
                text_content = self.tab.run_js("return document.body.innerText;")
                text_file = self.log_dir / f"{step_name}_{timestamp}_text.txt"
                with open(text_file, 'w', encoding='utf-8') as f:
                    f.write(text_content)
            except:
                pass
            
            # 保存页面信息
            page_info = {
                "timestamp": datetime.now().isoformat(),
                "step": step_name,
                "url": self.tab.url,
                "title": self.tab.title,
                "user_agent": self.tab.run_js("return navigator.userAgent;"),
                "screen_resolution": self.tab.run_js("return screen.width + 'x' + screen.height;"),
                "viewport_size": self.tab.run_js("return window.innerWidth + 'x' + window.innerHeight;"),
                "timezone": self.tab.run_js("return Intl.DateTimeFormat().resolvedOptions().timeZone;"),
                "language": self.tab.run_js("return navigator.language;"),
                "additional_info": additional_info or {}
            }
            
            info_file = self.log_dir / f"{step_name}_{timestamp}_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(page_info, f, indent=2, ensure_ascii=False)
            
            logging.info(f"📄 页面内容已保存: {step_name}")
            return True
            
        except Exception as e:
            logging.error(f"❌ 保存页面内容失败: {e}")
            return False

    def simulate_human_typing(self, element, text: str):
        """模拟人类打字行为"""
        try:
            # 清空输入框
            element.clear()
            time.sleep(random.uniform(0.2, 0.5))
            
            # 计算打字速度（字符/秒）
            chars_per_minute = random.randint(*self.typing_speed_range)
            chars_per_second = chars_per_minute / 60
            
            logging.info(f"⌨️ 模拟打字速度: {chars_per_minute} 字符/分钟")
            
            for i, char in enumerate(text):
                element.input(char)
                
                # 随机打字间隔
                base_interval = 1 / chars_per_second
                actual_interval = base_interval * random.uniform(0.7, 1.3)
                
                # 某些字符打字稍慢（如数字、特殊字符）
                if char.isdigit() or char in '@.-_':
                    actual_interval *= random.uniform(1.2, 1.8)
                
                # 偶尔的长停顿（思考时间）
                if random.random() < 0.1:
                    actual_interval += random.uniform(0.5, 1.5)
                
                time.sleep(actual_interval)
            
            # 打字完成后的短暂停顿
            time.sleep(random.uniform(0.3, 0.8))
            logging.info(f"✅ 人类打字模拟完成: {text}")
            return True
            
        except Exception as e:
            logging.error(f"❌ 人类打字模拟失败: {e}")
            return False

    def simulate_mouse_movement(self, target_element):
        """模拟人类鼠标移动轨迹"""
        try:
            # 获取目标元素位置
            target_rect = target_element.rect
            target_x = target_rect.midpoint[0]
            target_y = target_rect.midpoint[1]
            
            # 获取当前鼠标位置（模拟）
            current_x = random.randint(100, 800)
            current_y = random.randint(100, 600)
            
            # 生成贝塞尔曲线路径
            steps = random.randint(*self.mouse_move_steps)
            
            logging.info(f"🖱️ 模拟鼠标移动: ({current_x},{current_y}) -> ({target_x},{target_y})")
            
            for i in range(steps):
                t = i / (steps - 1)
                
                # 贝塞尔曲线插值
                control_x = (current_x + target_x) / 2 + random.randint(-50, 50)
                control_y = (current_y + target_y) / 2 + random.randint(-50, 50)
                
                x = (1-t)**2 * current_x + 2*(1-t)*t * control_x + t**2 * target_x
                y = (1-t)**2 * current_y + 2*(1-t)*t * control_y + t**2 * target_y
                
                # 执行鼠标移动（通过JavaScript）
                self.tab.run_js(f"""
                    var event = new MouseEvent('mousemove', {{
                        clientX: {x},
                        clientY: {y},
                        bubbles: true
                    }});
                    document.dispatchEvent(event);
                """)
                
                time.sleep(random.uniform(0.01, 0.03))
            
            # 到达目标后稍作停顿
            time.sleep(random.uniform(0.1, 0.3))
            logging.info("✅ 鼠标移动轨迹模拟完成")
            return True
            
        except Exception as e:
            logging.error(f"❌ 鼠标移动模拟失败: {e}")
            return False

    def simulate_human_pause(self, action_name: str = ""):
        """模拟人类思考停顿"""
        pause_time = random.uniform(*self.pause_range)
        if action_name:
            logging.info(f"🤔 模拟人类思考停顿 ({action_name}): {pause_time:.2f}秒")
        time.sleep(pause_time)

    def inject_advanced_us_fingerprint(self):
        """注入高级美国用户指纹"""
        fingerprint_script = """
        // 高级美国用户环境模拟
        (function() {
            console.log('🇺🇸 注入高级美国用户指纹...');
            
            // 1. 地理位置伪装（纽约）
            const US_COORDS = {
                latitude: 40.7128,
                longitude: -74.0060,
                accuracy: 10,
                altitude: null,
                altitudeAccuracy: null,
                heading: null,
                speed: null
            };
            
            // 重写地理位置API
            if (navigator.geolocation) {
                const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
                const originalWatchPosition = navigator.geolocation.watchPosition;
                
                navigator.geolocation.getCurrentPosition = function(success, error, options) {
                    setTimeout(() => {
                        success({
                            coords: US_COORDS,
                            timestamp: Date.now()
                        });
                    }, Math.random() * 100 + 50);
                };
                
                navigator.geolocation.watchPosition = function(success, error, options) {
                    return setInterval(() => {
                        success({
                            coords: US_COORDS,
                            timestamp: Date.now()
                        });
                    }, 1000);
                };
            }
            
            // 2. 时区伪装（美国东部时区 UTC-5）
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {
                return 300; // EST: UTC-5
            };
            
            // 3. 语言环境伪装
            Object.defineProperty(navigator, 'language', {
                get: function() { return 'en-US'; }
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: function() { return ['en-US', 'en']; }
            });
            
            // 4. 屏幕分辨率伪装（常见美国用户分辨率）
            const commonResolutions = [
                {width: 1920, height: 1080},
                {width: 1366, height: 768},
                {width: 1440, height: 900},
                {width: 1536, height: 864}
            ];
            const selectedRes = commonResolutions[Math.floor(Math.random() * commonResolutions.length)];
            
            Object.defineProperty(screen, 'width', {
                get: function() { return selectedRes.width; }
            });
            Object.defineProperty(screen, 'height', {
                get: function() { return selectedRes.height; }
            });
            Object.defineProperty(screen, 'availWidth', {
                get: function() { return selectedRes.width; }
            });
            Object.defineProperty(screen, 'availHeight', {
                get: function() { return selectedRes.height - 40; }
            });
            
            // 5. WebRTC IP泄露防护
            const originalCreateDataChannel = RTCPeerConnection.prototype.createDataChannel;
            RTCPeerConnection.prototype.createDataChannel = function() {
                throw new Error('WebRTC blocked for privacy');
            };
            
            // 6. Canvas指纹噪声注入
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function() {
                const context = this.getContext('2d');
                if (context) {
                    // 添加微小的随机噪声
                    const imageData = context.getImageData(0, 0, this.width, this.height);
                    for (let i = 0; i < imageData.data.length; i += 4) {
                        if (Math.random() < 0.001) {
                            imageData.data[i] = Math.floor(Math.random() * 256);
                            imageData.data[i + 1] = Math.floor(Math.random() * 256);
                            imageData.data[i + 2] = Math.floor(Math.random() * 256);
                        }
                    }
                    context.putImageData(imageData, 0, 0);
                }
                return originalToDataURL.apply(this, arguments);
            };
            
            // 7. 音频指纹伪装
            const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
            AudioContext.prototype.createAnalyser = function() {
                const analyser = originalCreateAnalyser.apply(this, arguments);
                const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                analyser.getFloatFrequencyData = function(array) {
                    originalGetFloatFrequencyData.apply(this, arguments);
                    // 添加微小噪声
                    for (let i = 0; i < array.length; i++) {
                        array[i] += Math.random() * 0.0001 - 0.00005;
                    }
                };
                return analyser;
            };
            
            console.log('✅ 高级美国用户指纹注入完成');
        })();
        """
        
        try:
            self.tab.run_js(fingerprint_script)
            logging.info("✅ 高级美国用户指纹注入成功")
            return True
        except Exception as e:
            logging.error(f"❌ 指纹注入失败: {e}")
            return False

    def init_browser_with_behavioral_simulation(self):
        """初始化浏览器并启用行为模拟"""
        try:
            logging.info("🎭 初始化行为模拟浏览器...")

            # 初始化浏览器
            self.browser = self.browser_manager.init_browser()
            if not self.browser:
                logging.error("❌ 浏览器初始化失败")
                return False

            # 获取tab
            self.tab = self.browser.latest_tab
            if not self.tab:
                logging.error("❌ 获取浏览器标签页失败")
                return False

            # 注入高级指纹
            if not self.inject_advanced_us_fingerprint():
                logging.warning("⚠️ 指纹注入失败，继续执行")

            # 模拟浏览器启动后的自然行为
            self.simulate_human_pause("浏览器启动")

            logging.info("✅ 行为模拟浏览器初始化成功")
            return True

        except Exception as e:
            logging.error(f"❌ 浏览器初始化失败: {e}")
            return False

    def step1_visit_promotion_page(self) -> bool:
        """步骤1: 访问推广页面（带行为模拟）"""
        try:
            logging.info("🌐 步骤1: 访问推广页面（行为模拟模式）...")

            # 访问页面
            promotion_url = "https://www.augmentcode.com/resources/cursor"
            self.tab.get(promotion_url)

            # 模拟页面加载等待
            self.simulate_human_pause("页面加载")

            # 保存页面内容
            self.save_page_content("step1_promotion_page", {
                "action": "visit_promotion_page",
                "url": promotion_url
            })

            # 模拟用户浏览页面的行为
            logging.info("👀 模拟用户浏览页面...")

            # 随机滚动页面
            for _ in range(random.randint(2, 4)):
                scroll_amount = random.randint(200, 500)
                self.tab.run_js(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(0.8, 1.5))

            # 回到顶部
            self.tab.run_js("window.scrollTo(0, 0);")
            self.simulate_human_pause("浏览页面")

            logging.info("✅ 步骤1完成：推广页面访问成功")
            return True

        except Exception as e:
            logging.error(f"❌ 步骤1失败: {e}")
            self.save_page_content("step1_error", {"error": str(e)})
            return False

    def step2_click_register_button(self) -> bool:
        """步骤2: 点击注册按钮（带行为模拟）"""
        try:
            logging.info("🖱️ 步骤2: 点击注册按钮（行为模拟模式）...")

            # 保存点击前的页面状态
            self.save_page_content("step2_before_click")

            # 查找注册按钮
            button_texts = [
                "Get your free month",
                "Claim offer",
                "Get started",
                "Continue",
                "Sign up",
                "Register",
                "Start free trial",
                "Redeem"
            ]

            register_button = None
            for button_text in button_texts:
                try:
                    logging.info(f"🔍 查找按钮: {button_text}")
                    button = self.tab.ele(f"@text()={button_text}", timeout=2)

                    if button:
                        register_button = button
                        logging.info(f"✅ 找到注册按钮: {button_text}")
                        break

                except Exception as e:
                    logging.debug(f"按钮 {button_text} 不存在: {e}")
                    continue

            if not register_button:
                logging.error("❌ 未找到注册按钮")
                self.save_page_content("step2_no_button_found")
                return False

            # 模拟鼠标移动到按钮
            self.simulate_mouse_movement(register_button)

            # 模拟点击前的犹豫
            self.simulate_human_pause("点击前思考")

            # 点击按钮
            register_button.click()
            logging.info("✅ 注册按钮点击成功")

            # 等待页面跳转
            time.sleep(3)

            # 保存跳转后的页面
            self.save_page_content("step2_after_click", {
                "action": "click_register_button",
                "button_text": button_text,
                "new_url": self.tab.url
            })

            logging.info(f"🔍 跳转后URL: {self.tab.url}")
            logging.info("✅ 步骤2完成：注册按钮点击成功")
            return True

        except Exception as e:
            logging.error(f"❌ 步骤2失败: {e}")
            self.save_page_content("step2_error", {"error": str(e)})
            return False

    def step3_handle_turnstile(self) -> bool:
        """步骤3: 处理Turnstile验证（带行为模拟）"""
        try:
            logging.info("🔐 步骤3: 处理Turnstile验证（行为模拟模式）...")

            # 保存当前页面状态
            self.save_page_content("step3_turnstile_check")

            # 检查是否存在Turnstile
            turnstile_found = False
            turnstile_selectors = [
                "iframe[src*='turnstile']",
                ".cf-turnstile",
                "[data-sitekey]"
            ]

            for selector in turnstile_selectors:
                try:
                    element = self.tab.ele(selector, timeout=2)
                    if element:
                        turnstile_found = True
                        logging.info(f"🔍 检测到Turnstile: {selector}")
                        break
                except:
                    continue

            if turnstile_found:
                logging.info("⏳ 等待Turnstile自动完成...")

                # 模拟用户等待验证的行为
                for i in range(10):
                    self.simulate_human_pause("等待验证")

                    # 检查验证是否完成
                    try:
                        success_indicator = self.tab.ele(".cf-turnstile-success", timeout=1)
                        if success_indicator:
                            logging.info("✅ Turnstile验证成功")
                            break
                    except:
                        pass

                    logging.info(f"⏳ 等待Turnstile验证... ({i+1}/10)")

                # 保存验证后状态
                self.save_page_content("step3_turnstile_completed")
            else:
                logging.info("ℹ️ 未检测到Turnstile，可能已跳过")

            logging.info("✅ 步骤3完成：Turnstile处理完成")
            return True

        except Exception as e:
            logging.error(f"❌ 步骤3失败: {e}")
            self.save_page_content("step3_error", {"error": str(e)})
            return False

    def step4_input_email(self) -> bool:
        """步骤4: 输入邮箱（带行为模拟）"""
        try:
            logging.info("📧 步骤4: 输入邮箱（行为模拟模式）...")

            # 保存输入前页面状态
            self.save_page_content("step4_before_email_input")

            # 查找邮箱输入框
            email_input = self.tab.ele("#username", timeout=10)
            if not email_input:
                logging.error("❌ 未找到邮箱输入框")
                self.save_page_content("step4_no_email_input")
                return False

            logging.info("✅ 找到邮箱输入框: #username")

            # 模拟鼠标移动到输入框
            self.simulate_mouse_movement(email_input)

            # 点击输入框获得焦点
            email_input.click()
            self.simulate_human_pause("获得焦点")

            # 使用人类打字模拟输入邮箱
            if not self.simulate_human_typing(email_input, self.email):
                logging.error("❌ 邮箱输入失败")
                return False

            # 保存输入后状态
            self.save_page_content("step4_after_email_input", {
                "action": "input_email",
                "email": self.email
            })

            logging.info(f"✅ 邮箱输入成功: {self.email}")
            logging.info("✅ 步骤4完成：邮箱输入成功")
            return True

        except Exception as e:
            logging.error(f"❌ 步骤4失败: {e}")
            self.save_page_content("step4_error", {"error": str(e)})
            return False

    def step5_click_continue(self) -> bool:
        """步骤5: 点击继续按钮（带行为模拟）"""
        try:
            logging.info("➡️ 步骤5: 点击继续按钮（行为模拟模式）...")

            # 保存点击前状态
            self.save_page_content("step5_before_continue")

            # 查找继续按钮
            continue_texts = ["Continue", "Next", "Proceed", "Submit"]
            continue_button = None

            for text in continue_texts:
                try:
                    button = self.tab.ele(f"@text()={text}", timeout=3)
                    if button and button.states.is_enabled:
                        continue_button = button
                        logging.info(f"✅ 找到继续按钮: {text}")
                        break
                except:
                    continue

            if not continue_button:
                logging.error("❌ 未找到继续按钮")
                self.save_page_content("step5_no_continue_button")
                return False

            # 模拟鼠标移动和点击
            self.simulate_mouse_movement(continue_button)
            self.simulate_human_pause("点击前确认")

            continue_button.click()
            logging.info("✅ 点击继续按钮成功")

            # 等待页面响应
            time.sleep(3)

            # 保存点击后状态
            self.save_page_content("step5_after_continue", {
                "action": "click_continue",
                "new_url": self.tab.url
            })

            logging.info("✅ 步骤5完成：继续按钮点击成功")
            return True

        except Exception as e:
            logging.error(f"❌ 步骤5失败: {e}")
            self.save_page_content("step5_error", {"error": str(e)})
            return False

    def step6_handle_verification_code(self) -> bool:
        """步骤6: 处理验证码（带行为模拟）"""
        try:
            logging.info("📋 步骤6: 处理验证码（行为模拟模式）...")

            # 保存验证码页面状态
            self.save_page_content("step6_verification_page")

            # 等待验证码邮件
            logging.info("⏳ 等待验证码邮件发送...")
            self.simulate_human_pause("等待邮件")

            # 获取验证码
            augment_register = AugmentCodeRegister(self.email)
            verification_code = None

            for attempt in range(5):
                logging.info(f"第 {attempt + 1}/5 次尝试获取验证码...")
                verification_code = augment_register.get_verification_code(self.email)

                if verification_code:
                    logging.info(f"✅ 获取到验证码: {verification_code}")
                    break

                # 模拟用户等待的行为
                wait_time = random.uniform(10, 20)
                logging.info(f"⏳ 等待 {wait_time:.1f} 秒后重试...")
                time.sleep(wait_time)

            if not verification_code:
                logging.error("❌ 获取验证码失败")
                self.save_page_content("step6_no_verification_code")
                return False

            # 查找验证码输入框
            code_input = self.tab.ele("#code", timeout=10)
            if not code_input:
                logging.error("❌ 未找到验证码输入框")
                self.save_page_content("step6_no_code_input")
                return False

            logging.info("✅ 找到验证码输入框: #code")

            # 模拟输入验证码
            self.simulate_mouse_movement(code_input)
            code_input.click()
            self.simulate_human_pause("准备输入验证码")

            if not self.simulate_human_typing(code_input, verification_code):
                logging.error("❌ 验证码输入失败")
                return False

            logging.info("✅ 验证码输入成功")

            # 查找提交按钮
            submit_texts = ["Continue", "Verify", "Submit", "Next"]
            submit_button = None

            for text in submit_texts:
                try:
                    button = self.tab.ele(f"@text()={text}", timeout=3)
                    if button and button.states.is_enabled:
                        submit_button = button
                        logging.info(f"✅ 找到提交按钮: {text}")
                        break
                except:
                    continue

            if not submit_button:
                logging.error("❌ 未找到提交按钮")
                self.save_page_content("step6_no_submit_button")
                return False

            # 模拟点击提交
            self.simulate_mouse_movement(submit_button)
            self.simulate_human_pause("提交前确认")

            submit_button.click()
            logging.info("✅ 验证码提交成功")

            # 等待处理
            time.sleep(5)

            # 保存提交后状态
            self.save_page_content("step6_after_verification", {
                "action": "submit_verification_code",
                "verification_code": verification_code,
                "new_url": self.tab.url
            })

            logging.info("✅ 步骤6完成：验证码处理成功")
            return True

        except Exception as e:
            logging.error(f"❌ 步骤6失败: {e}")
            self.save_page_content("step6_error", {"error": str(e)})
            return False

    def step7_final_terms_agreement(self) -> bool:
        """步骤7: 最终条款同意（带行为模拟）"""
        try:
            logging.info("📋 步骤7: 最终条款同意（行为模拟模式）...")

            # 保存当前页面状态
            current_url = self.tab.url
            logging.info(f"🔍 当前URL: {current_url}")
            self.save_page_content("step7_terms_page")

            # 检查是否在条款同意页面
            if "terms-accept" not in current_url:
                logging.info("ℹ️ 不在条款同意页面，可能已完成注册")
                self.save_page_content("step7_not_terms_page")
                return True

            logging.info("🔍 检测到最终条款同意页面")

            # 模拟阅读条款的行为
            logging.info("📖 模拟阅读条款...")
            for _ in range(random.randint(3, 6)):
                scroll_amount = random.randint(100, 300)
                self.tab.run_js(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(1.0, 2.5))

            # 回到顶部
            self.tab.run_js("window.scrollTo(0, 0);")
            self.simulate_human_pause("阅读条款")

            # 查找并勾选条款复选框
            checkbox_selectors = [
                "input[type='checkbox']",
                ".checkbox input",
                "#terms-checkbox",
                "@type=checkbox"
            ]

            checkbox_found = False
            for selector in checkbox_selectors:
                try:
                    if selector.startswith("#") or selector.startswith(".") or selector.startswith("input["):
                        checkbox = self.tab.ele(selector, timeout=3)
                    else:
                        checkbox = self.tab.ele(selector, timeout=3)

                    if checkbox:
                        is_checked = checkbox.states.is_checked
                        if not is_checked:
                            # 模拟鼠标移动到复选框
                            self.simulate_mouse_movement(checkbox)
                            self.simulate_human_pause("考虑是否同意")

                            # 点击复选框
                            checkbox.click()
                            time.sleep(1)

                            # 检查是否勾选成功
                            is_checked_after_click = checkbox.states.is_checked
                            if is_checked_after_click:
                                logging.info(f"✅ 勾选条款复选框成功: {selector}")
                                checkbox_found = True
                            else:
                                # 使用JavaScript方法
                                logging.warning("⚠️ 直接点击失败，尝试JavaScript方法...")
                                try:
                                    self.tab.run_js("""
                                        var checkbox = document.querySelector('input[type="checkbox"]');
                                        if (checkbox) {
                                            checkbox.checked = true;
                                            var event = new Event('change', { bubbles: true });
                                            checkbox.dispatchEvent(event);
                                        }
                                    """)
                                    time.sleep(1)
                                    logging.info("✅ JavaScript方法勾选成功")
                                    checkbox_found = True
                                except Exception as e:
                                    logging.warning(f"⚠️ JavaScript方法异常: {e}")
                        else:
                            logging.info("ℹ️ 条款复选框已勾选")
                            checkbox_found = True
                        break
                except:
                    continue

            if not checkbox_found:
                logging.warning("⚠️ 未找到条款复选框，可能已勾选或不需要")

            # 保存勾选后状态
            self.save_page_content("step7_after_checkbox")

            # 查找并点击注册按钮
            signup_button = self.tab.ele("#signup-button", timeout=3)
            if signup_button:
                # 检查按钮是否可用
                disabled_attr = signup_button.attr("disabled")
                is_disabled = disabled_attr is not None
                logging.info(f"🔍 注册按钮状态: {'禁用' if is_disabled else '启用'}")

                if is_disabled:
                    logging.warning("⚠️ 注册按钮仍然禁用，等待启用...")
                    # 等待按钮启用
                    for i in range(10):
                        time.sleep(1)
                        disabled_attr = signup_button.attr("disabled")
                        is_disabled = disabled_attr is not None
                        if not is_disabled:
                            logging.info(f"✅ 注册按钮在 {i+1} 秒后启用")
                            break

                    # 如果仍然禁用，尝试强制启用
                    if is_disabled:
                        logging.warning("⚠️ 按钮仍然禁用，尝试强制启用...")
                        try:
                            self.tab.run_js("""
                                var button = document.getElementById('signup-button');
                                if (button) {
                                    button.removeAttribute('disabled');
                                    button.disabled = false;
                                    button.classList.remove('disabled');
                                }
                            """)
                            time.sleep(1)
                            disabled_attr = signup_button.attr("disabled")
                            is_disabled = disabled_attr is not None
                            if not is_disabled:
                                logging.info("✅ 强制启用成功")
                        except Exception as e:
                            logging.warning(f"⚠️ 强制启用异常: {e}")

                if not is_disabled:
                    # 模拟最终确认的行为
                    self.simulate_mouse_movement(signup_button)
                    self.simulate_human_pause("最终确认")

                    signup_button.click()
                    logging.info("✅ 点击注册按钮成功")
                    time.sleep(3)

                    # 保存最终状态
                    final_url = self.tab.url
                    self.save_page_content("step7_final_result", {
                        "action": "final_signup",
                        "final_url": final_url
                    })

                    logging.info(f"🔍 处理后URL: {final_url}")

                    if "terms-accept" not in final_url:
                        logging.info("🎉 成功跳转，注册流程完成！")
                        return True
                    else:
                        logging.warning("⚠️ 仍在条款同意页面，可能需要手动检查")
                        return True
                else:
                    logging.error("❌ 注册按钮仍然禁用")
                    return False
            else:
                logging.error("❌ 未找到注册按钮 (#signup-button)")
                return False

        except Exception as e:
            logging.error(f"❌ 步骤7失败: {e}")
            self.save_page_content("step7_error", {"error": str(e)})
            return False

    def run_behavioral_registration(self):
        """运行完整的行为模拟注册流程"""
        try:
            logging.info("🎭 开始行为模拟注册流程...")

            # 生成邮箱
            self.email = generate_email_with_timestamp()
            logging.info(f"🎯 生成的邮箱: {self.email}")

            # 初始化浏览器
            if not self.init_browser_with_behavioral_simulation():
                return False

            # 执行注册步骤
            steps = [
                ("访问推广页面", self.step1_visit_promotion_page),
                ("点击注册按钮", self.step2_click_register_button),
                ("处理Turnstile验证", self.step3_handle_turnstile),
                ("输入邮箱", self.step4_input_email),
                ("点击继续", self.step5_click_continue),
                ("处理验证码", self.step6_handle_verification_code),
                ("最终条款同意", self.step7_final_terms_agreement)
            ]

            for step_name, step_func in steps:
                logging.info(f"\n{'='*50}")
                logging.info(f"执行: {step_name}")
                logging.info(f"{'='*50}")

                if not step_func():
                    logging.error(f"❌ {step_name} 失败，停止流程")
                    return False

                logging.info(f"✅ {step_name} 完成")

                # 步骤间的自然停顿
                self.simulate_human_pause(f"{step_name}完成后")

            logging.info("\n🎉 行为模拟注册流程完成！")
            return True

        except Exception as e:
            logging.error(f"❌ 注册流程异常: {e}")
            return False
        finally:
            # 保存最终的完整页面状态
            if self.tab:
                self.save_page_content("final_complete_state", {
                    "email": self.email,
                    "final_url": self.tab.url,
                    "completion_time": datetime.now().isoformat()
                })

            # 等待用户查看结果
            input("按 Enter 键关闭浏览器...")

            # 清理
            if self.browser_manager:
                self.browser_manager.cleanup()

def main():
    """主程序"""
    try:
        print("="*60)
        print("🎭 行为模式深度模拟注册测试")
        print("="*60)

        register = BehavioralSimulationRegister()
        success = register.run_behavioral_registration()

        if success:
            print("\n🎉 行为模拟注册测试成功！")
            print(f"✅ 行为模拟注册完成！")
            print(f"📧 邮箱: {register.email}")
            print(f"📁 日志目录: {register.log_dir}")
        else:
            print("\n😞 行为模拟注册测试失败！")
            print(f"❌ 行为模拟注册失败！")
            print(f"📁 日志目录: {register.log_dir}")

        return success

    except Exception as e:
        logging.error(f"❌ 主程序异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
