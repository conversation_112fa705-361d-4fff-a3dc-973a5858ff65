#!/usr/bin/env python3
"""
使用代理测试 AugmentCode 注册流程
确保使用 127.0.0.1:1080 代理和美国地区设置
"""

import time
import random
from DrissionPage import Chromium, ChromiumOptions
from augmentcode_email_handler import AugmentCodeEmail<PERSON>andler
from logger import logging

def create_browser_with_proxy():
    """创建使用代理的浏览器实例"""
    co = ChromiumOptions()
    
    # 设置代理
    co.set_proxy('127.0.0.1:1080')
    
    # 设置美国地区的 User-Agent
    us_user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6876.4 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6876.4 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6876.4 Safari/537.36'
    ]
    co.set_user_agent(random.choice(us_user_agents))
    
    # 设置语言为英文
    co.set_argument('--lang=en-US')
    co.set_argument('--accept-lang=en-US,en;q=0.9')
    
    # 设置时区为美国东部时间
    co.set_argument('--timezone=America/New_York')
    
    # 其他反检测设置
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--no-sandbox')
    co.set_argument('--disable-gpu')
    co.set_argument('--disable-extensions')
    
    # 设置窗口大小
    co.set_window_size(1920, 1080)
    
    # 不使用无头模式，方便观察
    co.headless(False)
    
    return Chromium(co)

def generate_email():
    """生成邮箱地址"""
    timestamp = str(int(time.time()))[-4:]
    return f"jun-temp{timestamp}@ai.ikunlove.de"

def test_ip_with_proxy(browser):
    """测试代理是否生效"""
    try:
        logging.info("检查代理是否生效...")
        tab = browser.latest_tab
        tab.get('https://httpbin.org/ip')
        time.sleep(2)
        
        ip_info = tab.ele('tag:pre').text
        logging.info(f"当前 IP 信息: {ip_info}")
        
        if '127.0.0.1' in ip_info or 'proxy' in ip_info.lower():
            logging.info("✅ 代理设置成功")
            return True
        else:
            logging.warning("⚠️ 代理可能未生效")
            return False
            
    except Exception as e:
        logging.error(f"检查代理时出错: {e}")
        return False

def test_augmentcode_registration():
    """测试 AugmentCode 注册流程"""
    browser = None
    try:
        # 创建浏览器
        logging.info("创建浏览器实例...")
        browser = create_browser_with_proxy()
        
        # 测试代理
        if not test_ip_with_proxy(browser):
            logging.warning("代理未生效，但继续测试...")
        
        # 生成邮箱
        email = generate_email()
        logging.info(f"生成的邮箱地址: {email}")
        
        # 访问 AugmentCode
        logging.info("访问 AugmentCode 注册页面...")
        tab = browser.latest_tab
        tab.get('https://app.augmentcode.com/promotions/cursor')
        time.sleep(3)
        
        # 输入邮箱
        logging.info("输入邮箱地址...")
        email_input = tab.ele('@@placeholder=Email address')
        if email_input:
            email_input.input(email)
            time.sleep(1)
            
            # 点击 Continue
            continue_btn = tab.ele('@@text()=Continue')
            if continue_btn:
                continue_btn.click()
                time.sleep(3)
                
                # 检查是否到达验证码页面
                if 'passwordless-email-challenge' in tab.url:
                    logging.info("✅ 成功到达验证码页面")
                    logging.info(f"当前页面: {tab.url}")
                    
                    # 获取验证码
                    logging.info("开始获取验证码...")
                    handler = AugmentCodeEmailHandler(email)
                    code = handler.get_augmentcode_verification_code(max_retries=3, retry_interval=30)
                    
                    if code:
                        logging.info(f"✅ 获取到验证码: {code}")
                        
                        # 输入验证码
                        code_input = tab.ele('@@placeholder=Enter the code')
                        if code_input:
                            code_input.input(code)
                            time.sleep(1)
                            
                            # 点击 Continue
                            continue_btn2 = tab.ele('@@text()=Continue')
                            if continue_btn2:
                                continue_btn2.click()
                                time.sleep(5)
                                
                                # 检查结果
                                current_url = tab.url
                                logging.info(f"验证码提交后的页面: {current_url}")
                                
                                if 'terms-accept' in current_url:
                                    logging.info("✅ 验证码验证成功，到达条款页面")
                                    
                                    # 检查是否有地区限制
                                    page_text = tab.html
                                    if 'limiting signups in certain regions' in page_text:
                                        logging.warning("❌ 遇到地区限制")
                                        return False
                                    else:
                                        logging.info("✅ 没有地区限制，可以继续注册")
                                        return True
                                else:
                                    logging.error("❌ 验证码验证失败")
                                    return False
                    else:
                        logging.error("❌ 未能获取到验证码")
                        return False
                else:
                    logging.error("❌ 未能到达验证码页面")
                    logging.info(f"当前页面: {tab.url}")
                    return False
            else:
                logging.error("❌ 未找到 Continue 按钮")
                return False
        else:
            logging.error("❌ 未找到邮箱输入框")
            return False
            
    except Exception as e:
        logging.error(f"测试过程中出错: {e}")
        return False
    finally:
        if browser:
            logging.info("关闭浏览器...")
            browser.quit()

if __name__ == "__main__":
    logging.info("开始测试 AugmentCode 注册流程（使用代理）...")
    success = test_augmentcode_registration()
    
    if success:
        logging.info("🎉 测试成功！代理设置有效，可以绕过地区限制")
    else:
        logging.error("😞 测试失败，需要进一步调试")
