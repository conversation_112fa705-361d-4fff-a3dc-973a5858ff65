#!/usr/bin/env python3
"""
调试页面结构脚本
用于检查点击注册按钮后的页面结构
"""

import time
from logger import logging
from browser_utils import BrowserManager


def debug_page_structure():
    """调试页面结构"""
    try:
        logging.info("🔍 开始调试页面结构...")
        
        # 初始化浏览器
        browser_manager = BrowserManager()
        browser = browser_manager.init_browser()
        
        if not browser:
            logging.error("❌ 浏览器初始化失败")
            return False
        
        tab = browser.latest_tab
        
        # 步骤1: 访问推广页面
        logging.info("🌐 访问AugmentCode推广页面...")
        tab.get("https://www.augmentcode.com/resources/cursor")
        time.sleep(3)
        
        # 获取初始页面信息
        initial_url = tab.url
        initial_title = tab.title
        logging.info(f"📄 初始页面URL: {initial_url}")
        logging.info(f"📄 初始页面标题: {initial_title}")
        
        # 步骤2: 查找并点击注册按钮
        logging.info("🖱️ 查找并点击注册按钮...")
        
        button_texts = [
            "Get your free month",
            "Claim offer", 
            "Get started",
            "Continue",
            "Sign up"
        ]
        
        button_found = False
        for button_text in button_texts:
            try:
                logging.info(f"🔍 查找按钮: {button_text}")
                button = tab.ele(f"@text()={button_text}", timeout=3)
                if button:
                    logging.info(f"✅ 找到按钮: {button_text}")
                    button.click()
                    logging.info("🖱️ 点击按钮成功")
                    button_found = True
                    break
            except Exception as btn_e:
                logging.debug(f"按钮 '{button_text}' 未找到: {btn_e}")
                continue
        
        if not button_found:
            logging.error("❌ 未找到注册按钮")
            return False
        
        # 等待页面跳转
        time.sleep(5)
        
        # 获取跳转后页面信息
        new_url = tab.url
        new_title = tab.title
        logging.info(f"📄 跳转后URL: {new_url}")
        logging.info(f"📄 跳转后标题: {new_title}")
        
        # 检查页面内容
        logging.info("🔍 分析页面结构...")
        
        # 获取所有输入框
        inputs_info = tab.run_js("""
            const inputs = document.querySelectorAll('input');
            const inputsData = [];
            
            inputs.forEach((input, index) => {
                inputsData.push({
                    index: index,
                    type: input.type,
                    name: input.name,
                    id: input.id,
                    placeholder: input.placeholder,
                    className: input.className,
                    value: input.value,
                    visible: input.offsetParent !== null
                });
            });
            
            return inputsData;
        """)
        
        logging.info("📋 页面中的所有输入框:")
        for input_info in inputs_info:
            logging.info(f"  输入框 {input_info['index']}:")
            logging.info(f"    类型: {input_info['type']}")
            logging.info(f"    名称: {input_info['name']}")
            logging.info(f"    ID: {input_info['id']}")
            logging.info(f"    占位符: {input_info['placeholder']}")
            logging.info(f"    类名: {input_info['className']}")
            logging.info(f"    可见: {input_info['visible']}")
            logging.info("")
        
        # 获取所有按钮
        buttons_info = tab.run_js("""
            const buttons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
            const buttonsData = [];
            
            buttons.forEach((button, index) => {
                buttonsData.push({
                    index: index,
                    tagName: button.tagName,
                    type: button.type,
                    textContent: button.textContent.trim(),
                    value: button.value,
                    className: button.className,
                    visible: button.offsetParent !== null,
                    disabled: button.disabled
                });
            });
            
            return buttonsData;
        """)
        
        logging.info("🔘 页面中的所有按钮:")
        for button_info in buttons_info:
            logging.info(f"  按钮 {button_info['index']}:")
            logging.info(f"    标签: {button_info['tagName']}")
            logging.info(f"    类型: {button_info['type']}")
            logging.info(f"    文本: {button_info['textContent']}")
            logging.info(f"    值: {button_info['value']}")
            logging.info(f"    类名: {button_info['className']}")
            logging.info(f"    可见: {button_info['visible']}")
            logging.info(f"    禁用: {button_info['disabled']}")
            logging.info("")
        
        # 获取页面主要文本内容
        page_text = tab.run_js("""
            return document.body.innerText.substring(0, 1000);
        """)
        
        logging.info("📝 页面主要文本内容:")
        logging.info(page_text)
        
        # 检查是否有iframe
        iframes_info = tab.run_js("""
            const iframes = document.querySelectorAll('iframe');
            const iframesData = [];
            
            iframes.forEach((iframe, index) => {
                iframesData.push({
                    index: index,
                    src: iframe.src,
                    id: iframe.id,
                    className: iframe.className,
                    visible: iframe.offsetParent !== null
                });
            });
            
            return iframesData;
        """)
        
        if iframes_info:
            logging.info("🖼️ 页面中的iframe:")
            for iframe_info in iframes_info:
                logging.info(f"  iframe {iframe_info['index']}:")
                logging.info(f"    源: {iframe_info['src']}")
                logging.info(f"    ID: {iframe_info['id']}")
                logging.info(f"    类名: {iframe_info['className']}")
                logging.info(f"    可见: {iframe_info['visible']}")
                logging.info("")
        
        # 截图保存
        screenshot_path = f"debug_page_{int(time.time())}.png"
        tab.get_screenshot(path=screenshot_path)
        logging.info(f"📸 页面截图已保存: {screenshot_path}")
        
        logging.info("✅ 页面结构调试完成！")
        return True
        
    except Exception as e:
        logging.error(f"❌ 页面结构调试失败: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return False
    finally:
        input("按 Enter 键关闭浏览器...")
        if browser_manager:
            browser_manager.quit()


def main():
    """主函数"""
    try:
        logging.info("=" * 60)
        logging.info("页面结构调试")
        logging.info("=" * 60)
        
        success = debug_page_structure()
        
        if success:
            logging.info("🎉 页面结构调试成功！")
            print("\n✅ 页面结构调试完成！")
        else:
            logging.error("😞 页面结构调试失败！")
            print("\n❌ 页面结构调试失败！")
        
        return success
        
    except Exception as e:
        logging.error(f"❌ 程序执行失败: {e}")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
