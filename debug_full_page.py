#!/usr/bin/env python3
"""
完整的页面调试工具
"""

import time
from logger import logging
from config import Config
from augmentcode_register import AugmentCodeRegister


def save_full_page_html(tab, filename):
    """保存完整的页面HTML，包括shadow-root内容"""
    try:
        print(f"保存完整页面HTML到 {filename}...")

        # 获取基础HTML
        base_html = tab.html

        # 执行JavaScript来获取包含shadow-root的完整HTML
        js_script = """
        function getFullHTML() {
            function getElementHTML(element) {
                let html = '';

                // 获取元素的开始标签
                html += '<' + element.tagName.toLowerCase();

                // 添加属性
                for (let attr of element.attributes) {
                    html += ' ' + attr.name + '="' + attr.value + '"';
                }
                html += '>';

                // 如果有shadow root，获取其内容
                if (element.shadowRoot) {
                    html += '\\n<!-- SHADOW ROOT START -->\\n';
                    html += element.shadowRoot.innerHTML;
                    html += '\\n<!-- SHADOW ROOT END -->\\n';
                } else {
                    // 递归处理子元素
                    for (let child of element.children) {
                        html += getElementHTML(child);
                    }

                    // 添加文本内容
                    if (element.childNodes.length > 0) {
                        for (let node of element.childNodes) {
                            if (node.nodeType === Node.TEXT_NODE) {
                                html += node.textContent;
                            }
                        }
                    }
                }

                // 添加结束标签
                html += '</' + element.tagName.toLowerCase() + '>';

                return html;
            }

            return getElementHTML(document.documentElement);
        }

        return getFullHTML();
        """

        try:
            full_html = tab.run_js(js_script)
            if full_html:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(full_html)
                print(f"✓ 完整HTML已保存到 {filename}")
            else:
                # 如果JavaScript方法失败，使用基础HTML
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(base_html)
                print(f"✓ 基础HTML已保存到 {filename}")
        except Exception as e:
            print(f"JavaScript方法失败: {str(e)}")
            # 使用基础HTML作为备选
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(base_html)
            print(f"✓ 基础HTML已保存到 {filename}")

        # 额外保存shadow-root信息
        save_shadow_root_info(tab, filename.replace('.html', '_shadow_info.txt'))

    except Exception as e:
        print(f"❌ 保存HTML失败: {str(e)}")


def save_shadow_root_info(tab, filename):
    """专门保存shadow-root信息"""
    try:
        print(f"保存shadow-root信息到 {filename}...")

        # 查找所有可能包含shadow-root的元素
        js_script = """
        function getShadowRootInfo() {
            let info = [];

            function checkElement(element, path = '') {
                let currentPath = path + '/' + element.tagName.toLowerCase();
                if (element.id) currentPath += '#' + element.id;
                if (element.className) currentPath += '.' + element.className.split(' ').join('.');

                if (element.shadowRoot) {
                    info.push({
                        path: currentPath,
                        shadowHTML: element.shadowRoot.innerHTML,
                        attributes: Array.from(element.attributes).map(attr => attr.name + '="' + attr.value + '"').join(' ')
                    });
                }

                for (let child of element.children) {
                    checkElement(child, currentPath);
                }
            }

            checkElement(document.documentElement);
            return info;
        }

        return getShadowRootInfo();
        """

        shadow_info = tab.run_js(js_script)

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("Shadow Root 信息报告\\n")
            f.write("=" * 50 + "\\n\\n")

            if shadow_info and len(shadow_info) > 0:
                for i, info in enumerate(shadow_info):
                    f.write(f"Shadow Root {i+1}:\\n")
                    f.write(f"路径: {info['path']}\\n")
                    f.write(f"属性: {info['attributes']}\\n")
                    f.write(f"Shadow HTML:\\n{info['shadowHTML']}\\n")
                    f.write("-" * 30 + "\\n\\n")
            else:
                f.write("未找到任何shadow-root元素\\n")

        print(f"✓ Shadow-root信息已保存到 {filename}")

    except Exception as e:
        print(f"❌ 保存shadow-root信息失败: {str(e)}")


def debug_full_page():
    """完整的页面调试"""
    print("=" * 60)
    print("完整的页面调试")
    print("=" * 60)
    
    try:
        # 获取邮箱配置
        config = Config()
        
        if config.get_temp_mail() != "null":
            email = f"{config.get_temp_mail()}{config.get_temp_mail_ext()}"
        else:
            imap_config = config.get_imap()
            if imap_config:
                email = imap_config['imap_user']
            else:
                print("❌ 未配置有效的邮箱")
                return False
        
        print(f"✓ 使用邮箱: {email}")
        
        # 创建注册器
        register = AugmentCodeRegister(email)
        print("✓ 注册器创建成功")
        
        # 手动初始化反检测浏览器
        print("\n正在初始化反检测浏览器...")
        try:
            from anti_detection_browser import AntiDetectionBrowserManager
            register.browser_manager = AntiDetectionBrowserManager()
            browser = register.browser_manager.init_browser()
            register.tab = browser.latest_tab
            print("✓ 反检测浏览器初始化成功")
        except Exception as e:
            print(f"❌ 反检测浏览器初始化失败: {str(e)}")
            return False
        
        # 访问页面
        print("\n访问 AugmentCode 页面...")
        url = "https://augmentcode.com/"
        register.tab.get(url)
        time.sleep(3)
        
        print(f"当前 URL: {register.tab.url}")
        print(f"页面标题: {register.tab.title}")
        
        # 保存初始页面截图和HTML
        register.save_screenshot("debug_initial_page")
        save_full_page_html(register.tab, "debug_initial_page.html")
        
        # 查找所有可能的按钮
        print("\n查找页面上的所有按钮...")
        buttons = register.tab.eles('tag:button')
        links = register.tab.eles('tag:a')
        
        print(f"找到 {len(buttons)} 个按钮:")
        for i, button in enumerate(buttons):
            try:
                text = button.text.strip()
                if text:
                    print(f"  按钮 {i+1}: '{text}'")
            except:
                print(f"  按钮 {i+1}: [无法获取文本]")
        
        print(f"\n找到 {len(links)} 个链接:")
        for i, link in enumerate(links):
            try:
                text = link.text.strip()
                href = link.attr('href')
                if text and ('free' in text.lower() or 'month' in text.lower() or 'sign' in text.lower()):
                    print(f"  链接 {i+1}: '{text}' -> {href}")
            except:
                pass
        
        # 查找包含特定文本的元素
        print("\n查找包含关键词的元素...")
        keywords = ['free month', 'get your free', 'sign up', 'register', 'start free']
        
        for keyword in keywords:
            elements = register.tab.eles(f'text:{keyword}')
            if elements:
                print(f"找到包含 '{keyword}' 的元素: {len(elements)} 个")
                for i, elem in enumerate(elements[:3]):  # 只显示前3个
                    try:
                        tag = elem.tag
                        text = elem.text.strip()[:50]
                        print(f"  元素 {i+1}: <{tag}> '{text}...'")
                    except:
                        pass
        
        # 尝试点击 "Get your free month" 按钮
        print("\n尝试点击 'Get your free month' 按钮...")
        
        # 多种选择器尝试
        selectors = [
            'text:Get your free month',
            'text:Get your free',
            'text:Start free trial',
            'text:Sign up',
            'text:Try for free',
            '.cta-button',
            '.signup-button',
            'a[href*="signup"]',
            'a[href*="register"]',
            'button[type="submit"]'
        ]
        
        clicked = False
        for selector in selectors:
            try:
                element = register.tab.ele(selector, timeout=2)
                if element:
                    print(f"✓ 使用选择器 '{selector}' 找到元素")
                    print(f"  元素文本: '{element.text.strip()}'")
                    print(f"  元素标签: {element.tag}")
                    
                    # 尝试点击
                    time.sleep(1)
                    element.click()
                    print(f"✓ 已点击元素")
                    clicked = True
                    break
            except Exception as e:
                print(f"  选择器 '{selector}' 失败: {str(e)}")
        
        if not clicked:
            print("❌ 未找到可点击的按钮")
            return False
        
        # 等待页面跳转
        print("\n等待页面跳转...")
        time.sleep(5)
        
        print(f"跳转后 URL: {register.tab.url}")
        print(f"跳转后标题: {register.tab.title}")
        
        # 保存跳转后的截图和HTML
        register.save_screenshot("debug_after_click")
        save_full_page_html(register.tab, "debug_after_click.html")
        
        # 检查是否有 Turnstile
        print("\n检查 Turnstile 元素...")
        turnstile_selectors = [
            "@id=cf-turnstile",
            ".cf-turnstile",
            "[data-sitekey]",
            "iframe[src*='turnstile']"
        ]
        
        for selector in turnstile_selectors:
            try:
                element = register.tab.ele(selector, timeout=2)
                if element:
                    print(f"✓ 使用选择器 '{selector}' 找到 Turnstile")
                    
                    # 尝试调试 Turnstile 结构
                    debug_turnstile_detailed(register.tab, element)
                    break
            except Exception as e:
                print(f"  选择器 '{selector}' 失败: {str(e)}")
        else:
            print("❌ 未找到 Turnstile 元素")
            
            # 检查是否有其他验证元素
            print("\n检查其他可能的验证元素...")
            captcha_selectors = [
                ".captcha",
                ".recaptcha",
                "#captcha",
                "iframe[src*='captcha']",
                "iframe[src*='recaptcha']"
            ]
            
            for selector in captcha_selectors:
                try:
                    element = register.tab.ele(selector, timeout=1)
                    if element:
                        print(f"✓ 找到验证元素: {selector}")
                except:
                    pass
        
        # 检查页面上的所有 iframe
        print("\n检查页面上的所有 iframe...")
        iframes = register.tab.eles('tag:iframe')
        print(f"找到 {len(iframes)} 个 iframe:")
        
        for i, iframe in enumerate(iframes):
            try:
                src = iframe.attr('src') or 'N/A'
                id_attr = iframe.attr('id') or 'N/A'
                class_attr = iframe.attr('class') or 'N/A'
                print(f"  iframe {i+1}:")
                print(f"    src: {src}")
                print(f"    id: {id_attr}")
                print(f"    class: {class_attr}")
            except Exception as e:
                print(f"  iframe {i+1}: 获取属性失败 - {str(e)}")
        
        # 保持浏览器打开
        print("\n浏览器将保持打开状态，请手动检查...")
        input("按回车键继续...")
        
        # 清理资源
        print("\n清理浏览器资源...")
        if register.browser_manager:
            register.browser_manager.quit()
        print("✓ 清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {str(e)}")
        logging.error(f"完整页面调试出错: {str(e)}")
        return False


def debug_turnstile_detailed(tab, turnstile_element):
    """详细调试 Turnstile 元素"""
    try:
        print("详细调试 Turnstile 元素...")
        
        # 获取 Turnstile 属性
        print(f"Turnstile 元素标签: {turnstile_element.tag}")
        print(f"Turnstile 元素 ID: {turnstile_element.attr('id')}")
        print(f"Turnstile 元素 class: {turnstile_element.attr('class')}")
        print(f"Turnstile 元素 data-sitekey: {turnstile_element.attr('data-sitekey')}")
        
        # 检查子元素
        children = turnstile_element.children()
        print(f"Turnstile 子元素数量: {len(children)}")
        
        for i, child in enumerate(children):
            try:
                print(f"  子元素 {i+1}: <{child.tag}> id='{child.attr('id')}' class='{child.attr('class')}'")
            except:
                print(f"  子元素 {i+1}: [无法获取信息]")
        
        # 尝试访问 shadow root
        if children:
            try:
                first_child = children[0]
                shadow_root = first_child.shadow_root
                print("✓ 成功访问 shadow root")
                
                # 查找 shadow root 中的元素
                shadow_elements = shadow_root.eles('*')
                print(f"shadow root 中的元素数量: {len(shadow_elements)}")
                
                for i, elem in enumerate(shadow_elements[:5]):  # 只显示前5个
                    try:
                        print(f"  shadow 元素 {i+1}: <{elem.tag}> id='{elem.attr('id')}' class='{elem.attr('class')}'")
                    except:
                        print(f"  shadow 元素 {i+1}: [无法获取信息]")
                
            except Exception as e:
                print(f"❌ 访问 shadow root 失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 详细调试 Turnstile 失败: {str(e)}")


def main():
    """主函数"""
    print("AugmentCode 完整页面调试工具")
    print("=" * 60)
    
    # 询问是否进行调试
    choice = input("是否进行完整页面调试？(y/n): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        print("\n开始完整页面调试...")
        success = debug_full_page()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 完整页面调试完成！")
        else:
            print("❌ 完整页面调试失败")
        print("=" * 60)
    else:
        print("\n跳过完整页面调试。")


if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n调试被用户中断")
    except Exception as e:
        print(f"\n调试程序出错: {str(e)}")
        input("\n按回车键退出...")
