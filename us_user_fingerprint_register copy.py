#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美国用户环境完全模拟注册器
专门针对地区限制检测的增强版指纹伪装
"""

import time
from logger import logging
from browser_utils import BrowserManager
from augmentcode_register import generate_email_with_timestamp


class USUserFingerprintRegister:
    """美国用户环境完全模拟注册器 - 针对地区限制检测"""
    
    def __init__(self, email: str):
        self.email = email
        self.browser_manager = None
        self.browser = None
        self.tab = None
    
    def init_browser(self):
        """初始化美国用户环境浏览器"""
        try:
            logging.info("🇺🇸 初始化美国用户环境浏览器...")

            # 创建浏览器管理器
            self.browser_manager = BrowserManager()

            # 使用原始方法初始化浏览器，但设置美国用户User-Agent
            us_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            self.browser = self.browser_manager.init_browser(user_agent=us_user_agent)

            # 覆盖原始的日语设置，改为美国英语设置
            self._override_browser_settings()
            
            if not self.browser:
                logging.error("❌ 浏览器创建失败")
                return False
            
            # 获取标签页
            self.tab = self.browser.latest_tab
            if not self.tab:
                logging.error("❌ 获取标签页失败")
                return False
            
            # 注入美国用户环境脚本
            self._inject_us_user_environment()
            
            logging.info("✅ 美国用户环境浏览器初始化成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 浏览器初始化失败: {e}")
            return False

    def _override_browser_settings(self):
        """覆盖浏览器设置为美国用户环境"""
        try:
            logging.info("🔧 覆盖浏览器设置为美国用户环境...")

            # 通过JavaScript覆盖语言设置
            override_script = """
            // 覆盖语言设置为美国英语
            Object.defineProperty(navigator, 'language', {
                get: function() { return 'en-US'; }
            });

            Object.defineProperty(navigator, 'languages', {
                get: function() { return ['en-US', 'en']; }
            });

            // 覆盖地理位置设置
            Object.defineProperty(navigator, 'geolocation', {
                get: function() {
                    return {
                        getCurrentPosition: function(success, error, options) {
                            if (success) {
                                setTimeout(() => {
                                    success({
                                        coords: {
                                            latitude: 40.7128,
                                            longitude: -74.0060,
                                            accuracy: 10
                                        },
                                        timestamp: Date.now()
                                    });
                                }, 100);
                            }
                        },
                        watchPosition: function(success, error, options) {
                            return this.getCurrentPosition(success, error, options);
                        }
                    };
                }
            });

            console.log('🇺🇸 浏览器设置已覆盖为美国用户环境');
            """

            # 注入覆盖脚本
            self.browser.run_js(override_script)

            logging.info("✅ 浏览器设置覆盖成功")

        except Exception as e:
            logging.warning(f"⚠️ 浏览器设置覆盖失败: {e}")
    
    def _inject_us_user_environment(self):
        """注入美国用户环境脚本"""
        try:
            logging.info("🇺🇸 注入美国用户环境脚本...")
            
            # 美国用户环境脚本
            us_environment_script = """
            (function() {
                'use strict';
                
                // 美国纽约坐标 (纽约市中心)
                const US_COORDS = {
                    latitude: 40.7128,
                    longitude: -74.0060,
                    accuracy: 10
                };
                
                // 美国东部时区
                const US_TIMEZONE = 'America/New_York';
                const US_TIMEZONE_OFFSET = -300; // EST: UTC-5
                
                // 1. 地理位置API伪装
                if (navigator.geolocation) {
                    const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
                    const originalWatchPosition = navigator.geolocation.watchPosition;
                    
                    navigator.geolocation.getCurrentPosition = function(success, error, options) {
                        if (success) {
                            setTimeout(() => {
                                success({
                                    coords: US_COORDS,
                                    timestamp: Date.now()
                                });
                            }, Math.random() * 100 + 50);
                        }
                    };
                    
                    navigator.geolocation.watchPosition = function(success, error, options) {
                        return navigator.geolocation.getCurrentPosition(success, error, options);
                    };
                }
                
                // 2. 时区伪装
                const originalDateToLocaleString = Date.prototype.toLocaleString;
                const originalDateToLocaleDateString = Date.prototype.toLocaleDateString;
                const originalDateToLocaleTimeString = Date.prototype.toLocaleTimeString;
                const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
                
                Date.prototype.getTimezoneOffset = function() {
                    return US_TIMEZONE_OFFSET;
                };
                
                Date.prototype.toLocaleString = function(locales, options) {
                    return originalDateToLocaleString.call(this, 'en-US', options);
                };
                
                Date.prototype.toLocaleDateString = function(locales, options) {
                    return originalDateToLocaleDateString.call(this, 'en-US', options);
                };
                
                Date.prototype.toLocaleTimeString = function(locales, options) {
                    return originalDateToLocaleTimeString.call(this, 'en-US', options);
                };
                
                // 3. 语言环境伪装
                Object.defineProperty(navigator, 'language', {
                    get: function() { return 'en-US'; }
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: function() { return ['en-US', 'en']; }
                });
                
                // 4. WebRTC完全禁用
                if (window.RTCPeerConnection) {
                    window.RTCPeerConnection = undefined;
                }
                if (window.webkitRTCPeerConnection) {
                    window.webkitRTCPeerConnection = undefined;
                }
                if (window.mozRTCPeerConnection) {
                    window.mozRTCPeerConnection = undefined;
                }
                
                // 5. 美国用户硬件配置
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: function() { return 8; }
                });
                
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: function() { return 8; }
                });
                
                // 6. 美国用户屏幕配置
                Object.defineProperty(screen, 'width', {
                    get: function() { return 1920; }
                });
                
                Object.defineProperty(screen, 'height', {
                    get: function() { return 1080; }
                });
                
                Object.defineProperty(screen, 'availWidth', {
                    get: function() { return 1920; }
                });
                
                Object.defineProperty(screen, 'availHeight', {
                    get: function() { return 1040; }
                });
                
                // 7. 美国用户平台信息
                Object.defineProperty(navigator, 'platform', {
                    get: function() { return 'Win32'; }
                });
                
                // 8. Canvas指纹轻微噪声 (美国用户特征)
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
                
                HTMLCanvasElement.prototype.toDataURL = function() {
                    const context = this.getContext('2d');
                    if (context) {
                        // 添加轻微的美国用户特征噪声
                        const imageData = context.getImageData(0, 0, this.width, this.height);
                        for (let i = 0; i < imageData.data.length; i += 4) {
                            imageData.data[i] += Math.floor(Math.random() * 3) - 1;     // R
                            imageData.data[i + 1] += Math.floor(Math.random() * 3) - 1; // G
                            imageData.data[i + 2] += Math.floor(Math.random() * 3) - 1; // B
                        }
                        context.putImageData(imageData, 0, 0);
                    }
                    return originalToDataURL.apply(this, arguments);
                };
                
                // 9. WebGL美国用户配置
                const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {
                    if (parameter === this.RENDERER) {
                        return 'ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)';
                    }
                    if (parameter === this.VENDOR) {
                        return 'Google Inc. (NVIDIA)';
                    }
                    return originalGetParameter.apply(this, arguments);
                };
                
                // 10. 美国用户User-Agent
                Object.defineProperty(navigator, 'userAgent', {
                    get: function() {
                        return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
                    }
                });
                
                console.log('🇺🇸 美国用户环境伪装已激活');
                
            })();
            """
            
            # 注入脚本
            self.tab.run_js(us_environment_script)
            
            logging.info("✅ 美国用户环境脚本注入成功")
            
        except Exception as e:
            logging.error(f"❌ 美国用户环境脚本注入失败: {e}")

    def step1_visit_promotion_page(self) -> bool:
        """步骤1: 访问推广页面"""
        try:
            logging.info("🌐 步骤1: 访问推广页面...")

            # AugmentCode推广页面
            promotion_url = "https://augmentcode.com/?utm_source=cursor&utm_medium=banner&utm_campaign=cursor-promotion&hl=en"

            # 访问页面
            self.tab.get(promotion_url)

            # 等待页面加载
            time.sleep(5)

            # 验证页面加载
            current_url = self.tab.url
            if "augmentcode.com" not in current_url:
                logging.error(f"❌ 页面加载失败，当前URL: {current_url}")
                return False

            logging.info(f"✅ 推广页面访问成功: {current_url}")

            # 截图记录
            self.tab.get_screenshot(path=f"step1_promotion_page_{int(time.time())}.png")

            logging.info("✅ 第一步完成：推广页面访问成功")
            return True

        except Exception as e:
            logging.error(f"❌ 访问推广页面失败: {e}")
            return False

    def step2_click_register_button(self) -> bool:
        """步骤2: 点击注册按钮"""
        try:
            logging.info("🖱️ 步骤2: 点击注册按钮...")

            # 等待页面完全加载
            time.sleep(5)

            # 滚动到页面顶部确保按钮可见
            self.tab.scroll.to_top()
            time.sleep(2)

            # 查找注册按钮
            register_button = None
            register_texts = ["Sign up", "Register", "Get started", "Join now", "Start free"]

            for text in register_texts:
                try:
                    register_button = self.tab.ele(f"@text()={text}", timeout=5)
                    if register_button:
                        logging.info(f"✅ 找到注册按钮: {text}")

                        # 滚动到按钮位置
                        try:
                            register_button.scroll.to_center()
                            time.sleep(1)
                        except:
                            pass

                        # 检查按钮是否可见
                        if register_button.states.is_displayed:
                            logging.info("✅ 注册按钮可见")
                            break
                        else:
                            logging.warning(f"⚠️ 注册按钮不可见: {text}")
                            register_button = None
                except:
                    continue

            if not register_button:
                # 尝试通过链接查找
                try:
                    register_button = self.tab.ele("@href*=register", timeout=5)
                    if register_button:
                        logging.info("✅ 通过链接找到注册按钮")
                        register_button.scroll.to_center()
                        time.sleep(1)
                except:
                    pass

            if not register_button:
                logging.error("❌ 未找到可用的注册按钮")
                # 截图调试
                self.tab.get_screenshot(path=f"debug_no_register_button_{int(time.time())}.png")
                return False

            # 尝试点击注册按钮
            try:
                register_button.click()
                logging.info("✅ 注册按钮点击成功")
            except Exception as click_error:
                logging.warning(f"⚠️ 直接点击失败，尝试JavaScript点击: {click_error}")
                try:
                    # 使用JavaScript点击
                    self.tab.run_js(f"arguments[0].click();", register_button)
                    logging.info("✅ JavaScript点击成功")
                except Exception as js_error:
                    logging.error(f"❌ JavaScript点击也失败: {js_error}")
                    return False

            # 等待页面跳转
            time.sleep(5)

            # 验证跳转
            new_url = self.tab.url
            logging.info(f"🔍 跳转后URL: {new_url}")

            # 截图记录
            self.tab.get_screenshot(path=f"step2_after_register_click_{int(time.time())}.png")

            logging.info("✅ 第二步完成：注册按钮点击成功")
            return True

        except Exception as e:
            logging.error(f"❌ 点击注册按钮失败: {e}")
            return False

    def step3_handle_turnstile(self) -> bool:
        """步骤3: 处理Turnstile验证"""
        try:
            logging.info("🔐 步骤3: 处理Turnstile验证...")

            # 等待Turnstile加载
            time.sleep(5)

            # 查找Turnstile iframe
            turnstile_found = False
            try:
                # 查找Turnstile相关元素
                turnstile_elements = [
                    "iframe[src*='turnstile']",
                    "iframe[src*='cloudflare']",
                    ".cf-turnstile",
                    "#cf-turnstile"
                ]

                for selector in turnstile_elements:
                    try:
                        element = self.tab.ele(selector, timeout=3)
                        if element:
                            logging.info(f"✅ 检测到Turnstile: {selector}")
                            turnstile_found = True
                            break
                    except:
                        continue

            except Exception as e:
                logging.info(f"ℹ️ Turnstile检测异常: {e}")

            if turnstile_found:
                logging.info("⏳ 等待Turnstile自动完成...")
                # 等待Turnstile自动完成
                time.sleep(10)
            else:
                logging.info("ℹ️ 未检测到Turnstile，可能已跳过")

            # 截图记录
            self.tab.get_screenshot(path=f"step3_turnstile_handled_{int(time.time())}.png")

            logging.info("✅ 第三步完成：Turnstile处理完成")
            return True

        except Exception as e:
            logging.error(f"❌ Turnstile处理失败: {e}")
            return False

    def step4_input_email(self) -> bool:
        """步骤4: 输入邮箱"""
        try:
            logging.info("📧 步骤4: 输入邮箱...")

            # 等待页面稳定
            time.sleep(3)

            # 使用传入的邮箱
            email = self.email

            # 查找邮箱输入框（使用原始脚本中成功的选择器）
            email_input = None
            email_selectors = [
                "#username",  # 原始脚本中成功的选择器
                "input[name=username]",
                "input[type=email]",
                "@type=email",
                "@placeholder*=email",
                "@name=email",
                "@id*=email"
            ]

            for selector in email_selectors:
                try:
                    if selector.startswith("#") or selector.startswith("input["):
                        email_input = self.tab.ele(selector, timeout=3)
                    else:
                        email_input = self.tab.ele(selector, timeout=3)

                    if email_input:
                        logging.info(f"✅ 找到邮箱输入框: {selector}")
                        break
                except:
                    continue

            if not email_input:
                logging.error("❌ 未找到邮箱输入框")
                return False

            # 输入邮箱
            email_input.clear()
            email_input.input(email)

            logging.info(f"✅ 邮箱输入成功: {email}")

            # 等待输入完成
            time.sleep(2)

            # 截图记录
            self.tab.get_screenshot(path=f"step4_email_input_{int(time.time())}.png")

            logging.info("✅ 第四步完成：邮箱输入成功")
            return True

        except Exception as e:
            logging.error(f"❌ 邮箱输入失败: {e}")
            return False

    def step5_click_continue(self) -> bool:
        """步骤5: 点击继续按钮"""
        try:
            logging.info("➡️ 步骤5: 点击继续按钮...")

            continue_buttons = [
                "Continue",
                "Next",
                "Proceed",
                "Submit",
                "Sign up",
                "Create account"
            ]

            for btn_text in continue_buttons:
                try:
                    button = self.tab.ele(f"@text()={btn_text}", timeout=3)
                    if button and button.states.is_enabled:
                        button.click()
                        logging.info(f"✅ 点击继续按钮成功: {btn_text}")
                        time.sleep(3)
                        return True
                except:
                    continue

            # 尝试通过类型查找
            try:
                submit_btn = self.tab.ele("input[type='submit']", timeout=3)
                if submit_btn:
                    submit_btn.click()
                    logging.info("✅ 点击提交按钮成功")
                    time.sleep(3)
                    return True
            except:
                pass

            logging.error("❌ 未找到继续按钮")
            return False

        except Exception as e:
            logging.error(f"❌ 点击继续按钮失败: {e}")
            return False

    def step6_handle_verification_code(self) -> bool:
        """步骤6: 处理验证码"""
        try:
            logging.info("📋 步骤6: 处理验证码...")

            # 等待邮件发送
            logging.info("⏳ 等待验证码邮件发送...")
            time.sleep(10)

            # 获取验证码
            try:
                from augmentcode_register import AugmentCodeRegister
                temp_register = AugmentCodeRegister(self.email)
                verification_code = temp_register.email_handler.get_augmentcode_verification_code(max_retries=5, retry_interval=30)

                if not verification_code:
                    logging.error("❌ 未获取到验证码")
                    return False

                logging.info(f"✅ 获取到验证码: {verification_code}")

            except Exception as e:
                logging.error(f"❌ 获取验证码失败: {e}")
                return False

            # 查找验证码输入框
            code_input = None
            code_selectors = [
                "#code",  # 原始脚本中成功的选择器
                "input[id=code]",
                "input[name=code]",
                "@type=text",
                "@placeholder*=code",
                "@placeholder*=verification",
                "@name*=code",
                "@id*=code"
            ]

            for selector in code_selectors:
                try:
                    if selector.startswith("#") or selector.startswith("input["):
                        code_input = self.tab.ele(selector, timeout=3)
                    else:
                        code_input = self.tab.ele(selector, timeout=3)

                    if code_input:
                        logging.info(f"✅ 找到验证码输入框: {selector}")
                        break
                except:
                    continue

            if not code_input:
                logging.error("❌ 未找到验证码输入框")
                return False

            # 输入验证码
            code_input.clear()
            code_input.input(verification_code)

            logging.info("✅ 验证码输入成功")
            time.sleep(2)

            # 提交验证码
            submit_button = None
            submit_texts = ["Continue", "Submit", "Verify", "Confirm"]

            for text in submit_texts:
                try:
                    submit_button = self.tab.ele(f"@text()={text}", timeout=3)
                    if submit_button:
                        logging.info(f"✅ 找到提交按钮: {text}")
                        break
                except:
                    continue

            if submit_button:
                submit_button.click()
                logging.info("✅ 验证码提交成功")
                time.sleep(5)
            else:
                logging.warning("⚠️ 未找到提交按钮，可能自动提交")

            # 截图记录
            self.tab.get_screenshot(path=f"step6_verification_complete_{int(time.time())}.png")

            logging.info("✅ 步骤6完成：验证码处理成功")
            return True

        except Exception as e:
            logging.error(f"❌ 步骤6失败: {e}")
            return False

    def step7_final_terms_agreement(self) -> bool:
        """步骤7: 最终条款同意（验证码后）"""
        try:
            logging.info("📋 步骤7: 最终条款同意...")

            # 检查当前URL
            current_url = self.tab.url
            logging.info(f"🔍 当前URL: {current_url}")

            # 如果不在terms-accept页面，说明可能已经完成
            if "terms-accept" not in current_url:
                logging.info("ℹ️ 不在条款同意页面，可能已完成注册")
                return True

            logging.info("🔍 检测到最终条款同意页面")

            # 等待页面完全加载
            time.sleep(2)

            # 调用条款同意处理方法
            if self._handle_terms_agreement():
                logging.info("✅ 最终条款同意处理成功")

                # 等待页面跳转
                time.sleep(5)

                # 检查是否跳转成功
                final_url = self.tab.url
                logging.info(f"🔍 处理后URL: {final_url}")

                if "terms-accept" not in final_url:
                    logging.info("🎉 成功跳转，注册流程完成！")
                    return True
                else:
                    logging.warning("⚠️ 仍在条款同意页面，可能需要手动检查")
                    return True  # 保守地认为成功
            else:
                logging.error("❌ 最终条款同意处理失败")
                return False

        except Exception as e:
            logging.error(f"❌ 步骤7失败: {e}")
            return False

    def _handle_terms_agreement(self) -> bool:
        """处理条款同意页面"""
        try:
            logging.info("📋 处理条款同意页面...")

            # 等待页面加载
            time.sleep(3)

            # 查找并勾选条款复选框
            checkbox_found = False
            checkbox_selectors = [
                "input[type='checkbox']",
                ".checkbox input",
                "#terms-checkbox",
                "@type=checkbox"
            ]

            for selector in checkbox_selectors:
                try:
                    if selector.startswith("#") or selector.startswith(".") or selector.startswith("input["):
                        checkbox = self.tab.ele(selector, timeout=3)
                    else:
                        checkbox = self.tab.ele(selector, timeout=3)

                    if checkbox and not checkbox.states.is_checked:
                        checkbox.click()
                        logging.info(f"✅ 勾选条款复选框成功: {selector}")
                        checkbox_found = True
                        time.sleep(1)
                        break
                except:
                    continue

            if not checkbox_found:
                logging.warning("⚠️ 未找到条款复选框，可能已勾选或不需要")

            # 查找并点击注册按钮
            register_button = None
            register_texts = ["Register", "Sign up", "Create account", "Complete", "Finish", "Continue"]

            for text in register_texts:
                try:
                    register_button = self.tab.ele(f"@text()={text}", timeout=3)
                    if register_button and register_button.states.is_enabled:
                        logging.info(f"✅ 找到注册按钮: {text}")
                        break
                except:
                    continue

            if not register_button:
                # 尝试通过类型查找
                try:
                    register_button = self.tab.ele("input[type='submit']", timeout=3)
                    if register_button:
                        logging.info("✅ 通过类型找到提交按钮")
                except:
                    pass

            if register_button:
                register_button.click()
                logging.info("✅ 点击注册按钮成功")
                time.sleep(3)
                return True
            else:
                logging.error("❌ 未找到注册按钮")
                return False

        except Exception as e:
            logging.error(f"❌ 条款同意处理失败: {e}")
            return False

    def run_registration(self):
        """运行完整注册流程"""
        try:
            logging.info("🇺🇸 开始美国用户环境注册流程...")

            # 初始化浏览器
            if not self.init_browser():
                return False

            # 执行注册步骤
            steps = [
                ("访问推广页面", self.step1_visit_promotion_page),
                ("点击注册按钮", self.step2_click_register_button),
                ("处理Turnstile验证", self.step3_handle_turnstile),
                ("输入邮箱", self.step4_input_email),
                ("点击继续", self.step5_click_continue),
                ("处理验证码", self.step6_handle_verification_code),
                ("最终条款同意", self.step7_final_terms_agreement)
            ]

            for step_name, step_func in steps:
                logging.info(f"\n{'='*50}")
                logging.info(f"执行: {step_name}")
                logging.info('='*50)

                if not step_func():
                    logging.error(f"❌ {step_name} 失败，停止流程")
                    return False

                logging.info(f"✅ {step_name} 完成")
                time.sleep(2)

            logging.info("\n🎉 美国用户环境注册流程完成！")
            return True

        except Exception as e:
            logging.error(f"❌ 注册流程失败: {e}")
            return False
        finally:
            input("按 Enter 键关闭浏览器...")
            if self.browser_manager:
                self.browser_manager.quit()


def main():
    """主函数"""
    try:
        logging.info("=" * 60)
        logging.info("🇺🇸 美国用户环境完全模拟注册测试")
        logging.info("=" * 60)

        # 生成邮箱
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱: {email}")

        register = USUserFingerprintRegister(email)
        success = register.run_registration()

        if success:
            logging.info("🎉 美国用户环境注册测试成功！")
            print(f"\n✅ 美国用户环境注册完成！")
            print(f"📧 邮箱: {email}")
        else:
            logging.error("😞 美国用户环境注册测试失败！")
            print("\n❌ 美国用户环境注册失败！")

        return success

    except Exception as e:
        logging.error(f"❌ 程序执行失败: {e}")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
