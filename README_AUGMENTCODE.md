# AugmentCode 自动注册工具

这是一个基于现有 Cursor 自动注册项目的 AugmentCode 注册工具，用于自动完成 AugmentCode 的注册流程。

## 文件说明

### 核心文件
- `augmentcode_register.py` - 主要的注册逻辑实现
- `augmentcode_email_handler.py` - 专用的邮箱验证码处理器
- `run_augmentcode_register.py` - 用户友好的启动脚本

### 启动脚本
- `run_augmentcode.bat` - Windows 批处理启动脚本
- `run_augmentcode.sh` - Linux/Mac Shell 启动脚本

### 测试工具
- `test_augmentcode.py` - 配置和功能测试脚本

### 必需文件
- `WIPDF.pdf` - 注册时需要上传的文件（需要用户提供）
- `.env` - 配置文件（需要用户配置）

## 功能特性

- 自动访问 AugmentCode 注册页面
- 智能处理 Turnstile 人机验证
- 自动输入邮箱和验证码
- 自动上传所需文件
- 支持多种邮箱配置（临时邮箱、IMAP、POP3）
- 完整的错误处理和日志记录
- 自动截图保存关键步骤

## 注册流程

工具将自动执行以下 6 个步骤：

1. **访问页面并点击按钮**
   - 访问 `https://www.augmentcode.com/resources/cursor`
   - 点击 "Get your free month" 按钮

2. **处理人机验证**
   - 自动检测 Turnstile 验证状态
   - 如需勾选，自动点击验证框
   - 等待验证成功

3. **输入邮箱**
   - 在邮箱输入框中输入配置的邮箱地址

4. **点击继续**
   - 点击第一个 Continue 按钮进入下一步

5. **输入验证码**
   - 自动从邮箱获取验证码
   - 输入验证码并点击第二个 Continue 按钮

6. **上传文件**
   - 自动上传项目目录下的 `WIPDF.pdf` 文件

## 使用方法

### 快速开始

**Windows 用户：**
1. 双击运行 `run_augmentcode.bat`

**Linux/Mac 用户：**
1. 在终端中运行：`./run_augmentcode.sh`

**手动运行：**
1. 运行：`python run_augmentcode_register.py`

### 详细步骤

### 1. 环境准备

确保已安装所有依赖：
```bash
pip install -r requirements.txt
```

### 2. 配置邮箱

在 `.env` 文件中配置邮箱信息：

**使用临时邮箱（推荐）：**
```env
DOMAIN=your-domain.com
TEMP_MAIL=your-temp-mail-username
TEMP_MAIL_EXT=@your-domain.com
TEMP_MAIL_EPIN=your-epin
```

**使用 IMAP 邮箱：**
```env
DOMAIN=your-domain.com
TEMP_MAIL=null
IMAP_SERVER=imap.gmail.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-password
IMAP_DIR=inbox
IMAP_PROTOCOL=IMAP
```

**使用 POP3 邮箱：**
```env
DOMAIN=your-domain.com
TEMP_MAIL=null
IMAP_SERVER=pop.gmail.com
IMAP_PORT=995
IMAP_USER=<EMAIL>
IMAP_PASS=your-password
IMAP_PROTOCOL=POP3
```

### 3. 准备文件

确保项目根目录下有 `WIPDF.pdf` 文件用于上传。

### 4. 运行注册

```bash
python augmentcode_register.py
```

## 配置选项

### 浏览器配置

```env
# 浏览器路径（可选）
BROWSER_PATH=/path/to/chrome

# 代理设置（可选）
BROWSER_PROXY=http://127.0.0.1:1080

# 无头模式（默认 True）
BROWSER_HEADLESS=False
```

### 验证码获取配置

- `max_retries`: 最大重试次数（默认 5）
- `retry_interval`: 重试间隔秒数（默认 30）

## 日志和截图

- 所有操作日志会保存在 `logs/` 目录下
- 关键步骤的截图会保存在 `screenshots/` 目录下
- 截图文件命名格式：`augmentcode_{步骤}_{时间戳}.png`

## 错误处理

工具包含完整的错误处理机制：

- 网络连接错误
- 页面元素未找到
- 验证码获取失败
- 文件上传失败
- Turnstile 验证失败

所有错误都会记录在日志中，并保存错误时的页面截图。

## 注意事项

1. **文件准备**：确保 `WIPDF.pdf` 文件存在于项目根目录
2. **邮箱配置**：根据使用的邮箱类型正确配置 `.env` 文件
3. **网络环境**：确保网络连接稳定，必要时配置代理
4. **验证码时效**：邮箱验证码通常有时效性，工具会自动重试获取
5. **人机验证**：Turnstile 验证可能需要多次尝试，工具会自动重试

## 故障排除

### 常见问题

1. **找不到页面元素**
   - 检查网络连接
   - 确认页面是否正常加载
   - 查看截图确认页面状态

2. **验证码获取失败**
   - 检查邮箱配置是否正确
   - 确认邮箱服务是否正常
   - 检查垃圾邮件文件夹

3. **文件上传失败**
   - 确认 `WIPDF.pdf` 文件存在
   - 检查文件权限
   - 确认文件大小是否符合要求

4. **Turnstile 验证失败**
   - 检查网络连接
   - 尝试关闭代理
   - 检查浏览器扩展是否正常加载

### 调试模式

设置环境变量启用调试模式：
```env
BROWSER_HEADLESS=False
```

这样可以看到浏览器的实际操作过程，便于调试问题。

## 技术实现

- 基于 `DrissionPage` 进行浏览器自动化
- 使用现有的 `BrowserManager` 和 `EmailVerificationHandler`
- 集成 Turnstile 验证处理逻辑
- 支持多种邮箱协议（IMAP/POP3/临时邮箱）

## 许可证

本工具基于原项目的开源许可证，仅供学习和研究使用。
